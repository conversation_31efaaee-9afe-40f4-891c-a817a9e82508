// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {console} from 'forge-std/console.sol';

import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';
import {QuadraticSwapFees} from 'contracts/libraries/QuadraticSwapFees.sol';
import {FactoryPairTestFixture, IPairHarness, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {DepletedAssetUtils} from 'test/utils/DepletedAssetUtils.sol';
import {MINIMUM_LIQUIDITY} from 'test/shared/utilities.sol';

/**
 * @title K Invariant Bypass Vulnerability POC
 * @notice This test investigates the alleged vulnerability in AmmalgamPair.swap()
 * where calculateBalanceAfterFees might miscalculate in depleted states,
 * potentially allowing swaps that pass the K invariant check but erode pool value.
 */
contract KInvariantBypassVulnerabilityPOC is Test {
    IPairHarness private pair;
    address private pairAddress;
    FactoryPairTestFixture private fixture;
    
    // Test accounts
    address private alice = address(0x1111);
    address private bob = address(0x2222);
    address private attacker = address(0x3333);
    
    // Pool state variables
    uint256 private initialMintX;
    uint256 private initialMintY;
    uint256 private missingXAssets;
    uint256 private missingYAssets;
    
    // Constants from AmmalgamPair
    uint256 private constant BUFFER = 95;
    uint256 private constant BUFFER_NUMERATOR = 100;
    uint256 private constant INVERSE_BUFFER = 5;
    
    // Test results tracking
    struct TestResult {
        bool vulnerabilityExists;
        uint256 valueExtracted;
        string description;
    }
    
    TestResult[] private testResults;
    
    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        pair = fixture.pair();
        pairAddress = address(pair);

        // Initialize pool with substantial liquidity
        initialMintX = 100e18;
        initialMintY = 100e18;

        // Set up initial liquidity providers
        fixture.transferTokensTo(alice, initialMintX, initialMintY);
        fixture.mintForAndInitializeBlocks(alice, initialMintX, initialMintY);

        // Create depletion scenario
        missingXAssets = 50e18;  // 50% depletion
        missingYAssets = 30e18;  // 30% depletion

        // Bob provides liquidity and then borrows X and Y tokens to create depletion
        fixture.transferTokensTo(bob, missingXAssets * 2, missingYAssets * 2);
        fixture.mintFor(bob, missingXAssets, missingYAssets); // Provide liquidity (L tokens)
        fixture.borrowFor(bob, missingXAssets, missingYAssets);  // Borrow X and Y tokens

        // Set reference reserves to current state
        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        pair.exposed_setReferenceReserves(reserveX, reserveY);

        console.log("=== Initial Pool State ===");
        console.log("Reserve X:", reserveX);
        console.log("Reserve Y:", reserveY);
        console.log("Missing X:", missingXAssets);
        console.log("Missing Y:", missingYAssets);
        console.log("Depletion threshold X:", (missingXAssets * BUFFER_NUMERATOR) / BUFFER);
        console.log("Depletion threshold Y:", (missingYAssets * BUFFER_NUMERATOR) / BUFFER);
    }
    
    /**
     * @notice Test 1: Analyze depletion boundary conditions
     * This test examines the behavior at the exact boundary where depletion state changes
     */
    function test_DepletionBoundaryAnalysis() public {
        console.log("\n=== Test 1: Depletion Boundary Analysis ===");
        
        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        (uint256 missingX, uint256 missingY) = pair.exposed_missingAssets();
        
        // Calculate depletion thresholds
        uint256 depletionThresholdX = (missingX * BUFFER_NUMERATOR) / BUFFER;
        uint256 depletionThresholdY = (missingY * BUFFER_NUMERATOR) / BUFFER;
        
        console.log("Current reserve X:", reserveX);
        console.log("Depletion threshold X:", depletionThresholdX);
        console.log("Is X depleted:", reserveX * BUFFER < missingX * BUFFER_NUMERATOR);
        
        console.log("Current reserve Y:", reserveY);
        console.log("Depletion threshold Y:", depletionThresholdY);
        console.log("Is Y depleted:", reserveY * BUFFER < missingY * BUFFER_NUMERATOR);
        
        // Test boundary crossing
        if (reserveX > depletionThresholdX) {
            _testBoundaryCrossing(true); // X token boundary
        }
        if (reserveY > depletionThresholdY) {
            _testBoundaryCrossing(false); // Y token boundary
        }
    }
    
    /**
     * @notice Test boundary crossing behavior
     */
    function _testBoundaryCrossing(bool testXToken) private {
        console.log(testXToken ? "\n--- Testing X Token Boundary ---" : "\n--- Testing Y Token Boundary ---");
        
        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        (uint256 missingX, uint256 missingY) = pair.exposed_missingAssets();
        
        if (testXToken) {
            uint256 depletionThreshold = (missingX * BUFFER_NUMERATOR) / BUFFER;
            uint256 amountToDeplete = reserveX - depletionThreshold + 1;
            
            console.log("Amount needed to cross X depletion boundary:", amountToDeplete);
            
            // Try to perform swap that crosses boundary
            _attemptBoundaryExploit(amountToDeplete, 0, true);
        } else {
            uint256 depletionThreshold = (missingY * BUFFER_NUMERATOR) / BUFFER;
            uint256 amountToDeplete = reserveY - depletionThreshold + 1;
            
            console.log("Amount needed to cross Y depletion boundary:", amountToDeplete);
            
            // Try to perform swap that crosses boundary
            _attemptBoundaryExploit(0, amountToDeplete, false);
        }
    }
    
    /**
     * @notice Attempt to exploit boundary crossing
     */
    function _attemptBoundaryExploit(uint256 amountXOut, uint256 amountYOut, bool isXToken) private {
        // Record initial state
        (uint256 initialReserveX, uint256 initialReserveY,) = pair.getReserves();
        uint256 initialK = initialReserveX * initialReserveY;
        
        console.log("Initial K:", initialK);
        console.log("Attempting swap - X out:", amountXOut, "Y out:", amountYOut);
        
        // Provide tokens for the swap
        if (isXToken) {
            // Swapping Y for X, need to provide Y tokens
            uint256 estimatedYIn = _estimateRequiredInput(amountXOut, initialReserveY, initialReserveX);
            fixture.transferTokensTo(attacker, 0, estimatedYIn * 2); // Provide extra
            
            vm.startPrank(attacker);
            fixture.tokenY().transfer(pairAddress, estimatedYIn * 2);
            
            try pair.swap(amountXOut, 0, attacker, "") {
                console.log("Swap succeeded!");
                _analyzePostSwapState(initialK, "X token boundary crossing");
            } catch Error(string memory reason) {
                console.log("Swap failed:", reason);
            } catch {
                console.log("Swap failed with unknown error");
            }
            vm.stopPrank();
        } else {
            // Swapping X for Y, need to provide X tokens
            uint256 estimatedXIn = _estimateRequiredInput(amountYOut, initialReserveX, initialReserveY);
            fixture.transferTokensTo(attacker, estimatedXIn * 2, 0); // Provide extra
            
            vm.startPrank(attacker);
            fixture.tokenX().transfer(pairAddress, estimatedXIn * 2);
            
            try pair.swap(0, amountYOut, attacker, "") {
                console.log("Swap succeeded!");
                _analyzePostSwapState(initialK, "Y token boundary crossing");
            } catch Error(string memory reason) {
                console.log("Swap failed:", reason);
            } catch {
                console.log("Swap failed with unknown error");
            }
            vm.stopPrank();
        }
    }
    
    /**
     * @notice Estimate required input for a swap (simplified)
     */
    function _estimateRequiredInput(uint256 amountOut, uint256 reserveIn, uint256 reserveOut) private pure returns (uint256) {
        // Simple constant product formula estimation
        return (amountOut * reserveIn) / (reserveOut - amountOut) + 1;
    }
    
    /**
     * @notice Analyze post-swap state for vulnerabilities
     */
    function _analyzePostSwapState(uint256 initialK, string memory testDescription) private {
        (uint256 finalReserveX, uint256 finalReserveY,) = pair.getReserves();
        uint256 finalK = finalReserveX * finalReserveY;
        
        console.log("Final K:", finalK);
        console.log("K change:", finalK > initialK ? "INCREASED" : "DECREASED");
        
        if (finalK < initialK) {
            uint256 kLoss = initialK - finalK;
            console.log("K INVARIANT VIOLATED! Loss:", kLoss);
            
            testResults.push(TestResult({
                vulnerabilityExists: true,
                valueExtracted: kLoss,
                description: testDescription
            }));
        } else {
            console.log("K invariant maintained");
            
            testResults.push(TestResult({
                vulnerabilityExists: false,
                valueExtracted: 0,
                description: testDescription
            }));
        }
    }
    
    /**
     * @notice Test 2: Edge case analysis around depletion calculations
     */
    function test_DepletionCalculationEdgeCases() public {
        console.log("\n=== Test 2: Depletion Calculation Edge Cases ===");

        // Test various scenarios around the depletion boundary
        _testEdgeCase("Exactly at depletion threshold", 0);
        _testEdgeCase("1 wei above depletion threshold", 1);
        _testEdgeCase("1 wei below depletion threshold", type(uint256).max); // Will underflow to large number
    }

    function _testEdgeCase(string memory description, uint256 offset) private {
        console.log("\n--- Testing:", description, "---");

        // This is a simplified edge case test
        // In a real exploit, we would manipulate the pool state more precisely
        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        console.log("Current reserves - X:", reserveX, "Y:", reserveY);

        // Record this as a test case
        testResults.push(TestResult({
            vulnerabilityExists: false,
            valueExtracted: 0,
            description: description
        }));
    }

    /**
     * @notice Test 3: Comprehensive depletion state manipulation
     */
    function test_DepletionStateManipulation() public {
        console.log("\n=== Test 3: Depletion State Manipulation ===");

        // Save initial state
        (uint256 initialReserveX, uint256 initialReserveY,) = pair.getReserves();
        (uint256 initialMissingX, uint256 initialMissingY) = pair.exposed_missingAssets();

        console.log("Initial state:");
        console.log("Reserve X:", initialReserveX, "Missing X:", initialMissingX);
        console.log("Reserve Y:", initialReserveY, "Missing Y:", initialMissingY);

        // Test 3a: Try to manipulate X token to depletion boundary
        _testDepletionManipulation(true);

        // Test 3b: Try to manipulate Y token to depletion boundary
        _testDepletionManipulation(false);
    }

    function _testDepletionManipulation(bool manipulateX) private {
        string memory tokenName = manipulateX ? "X" : "Y";
        console.log("\n--- Manipulating", tokenName, "token depletion state ---");

        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        (uint256 missingX, uint256 missingY) = pair.exposed_missingAssets();

        if (manipulateX) {
            uint256 depletionThreshold = (missingX * BUFFER_NUMERATOR) / BUFFER;
            console.log("X depletion threshold:", depletionThreshold);
            console.log("Current X reserve:", reserveX);

            if (reserveX > depletionThreshold) {
                // Try to bring X close to depletion threshold through swaps
                uint256 targetOut = reserveX - depletionThreshold - 1e15; // Leave small buffer
                console.log("Attempting to extract X:", targetOut);

                _attemptPrecisionExploit(targetOut, 0, "X depletion manipulation");
            }
        } else {
            uint256 depletionThreshold = (missingY * BUFFER_NUMERATOR) / BUFFER;
            console.log("Y depletion threshold:", depletionThreshold);
            console.log("Current Y reserve:", reserveY);

            if (reserveY > depletionThreshold) {
                // Try to bring Y close to depletion threshold through swaps
                uint256 targetOut = reserveY - depletionThreshold - 1e15; // Leave small buffer
                console.log("Attempting to extract Y:", targetOut);

                _attemptPrecisionExploit(0, targetOut, "Y depletion manipulation");
            }
        }
    }

    /**
     * @notice Test 4: Precision exploit attempts
     */
    function test_PrecisionExploitAttempts() public {
        console.log("\n=== Test 4: Precision Exploit Attempts ===");

        // Test small amounts that might cause rounding issues
        _attemptPrecisionExploit(1, 0, "Minimal X extraction");
        _attemptPrecisionExploit(0, 1, "Minimal Y extraction");
        _attemptPrecisionExploit(1000, 0, "Small X extraction");
        _attemptPrecisionExploit(0, 1000, "Small Y extraction");
    }

    function _attemptPrecisionExploit(uint256 amountXOut, uint256 amountYOut, string memory testName) private {
        console.log("\n--- Precision exploit:", testName, "---");

        // Record initial state
        (uint256 initialReserveX, uint256 initialReserveY,) = pair.getReserves();
        uint256 initialK = initialReserveX * initialReserveY;

        // Estimate required input
        uint256 estimatedInput;
        bool isXOut = amountXOut > 0;

        if (isXOut) {
            estimatedInput = _estimateRequiredInput(amountXOut, initialReserveY, initialReserveX);
            fixture.transferTokensTo(attacker, 0, estimatedInput * 2);

            vm.startPrank(attacker);
            fixture.tokenY().transfer(pairAddress, estimatedInput * 2);
        } else {
            estimatedInput = _estimateRequiredInput(amountYOut, initialReserveX, initialReserveY);
            fixture.transferTokensTo(attacker, estimatedInput * 2, 0);

            vm.startPrank(attacker);
            fixture.tokenX().transfer(pairAddress, estimatedInput * 2);
        }

        try pair.swap(amountXOut, amountYOut, attacker, "") {
            console.log("Precision exploit swap succeeded");
            _analyzePostSwapState(initialK, testName);
        } catch Error(string memory reason) {
            console.log("Precision exploit failed:", reason);
            testResults.push(TestResult({
                vulnerabilityExists: false,
                valueExtracted: 0,
                description: string(abi.encodePacked(testName, " - Failed"))
            }));
        } catch {
            console.log("Precision exploit failed with unknown error");
            testResults.push(TestResult({
                vulnerabilityExists: false,
                valueExtracted: 0,
                description: string(abi.encodePacked(testName, " - Failed"))
            }));
        }

        vm.stopPrank();
    }

    /**
     * @notice Test 5: Fee calculation discrepancy analysis
     */
    function test_FeeCalculationDiscrepancy() public {
        console.log("\n=== Test 5: Fee Calculation Discrepancy Analysis ===");

        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        (uint256 missingX, uint256 missingY) = pair.exposed_missingAssets();
        (uint256 refX, uint256 refY) = pair.referenceReserves();

        console.log("Analyzing fee calculation differences...");
        console.log("Reserve X:", reserveX, "Missing X:", missingX);
        console.log("Reserve Y:", reserveY, "Missing Y:", missingY);
        console.log("Reference X:", refX, "Reference Y:", refY);

        // Check if either token is near depletion threshold
        bool xNearDepletion = _isNearDepletionThreshold(reserveX, missingX);
        bool yNearDepletion = _isNearDepletionThreshold(reserveY, missingY);

        console.log("X near depletion:", xNearDepletion);
        console.log("Y near depletion:", yNearDepletion);

        if (xNearDepletion || yNearDepletion) {
            console.log("Found tokens near depletion threshold - testing fee discrepancy");
            _testFeeDiscrepancy(xNearDepletion, yNearDepletion);
        } else {
            console.log("No tokens near depletion threshold");
            testResults.push(TestResult({
                vulnerabilityExists: false,
                valueExtracted: 0,
                description: "Fee discrepancy test - No near-depletion state"
            }));
        }
    }

    function _isNearDepletionThreshold(uint256 reserve, uint256 missing) private pure returns (bool) {
        if (missing == 0) return false;

        uint256 threshold = (missing * BUFFER_NUMERATOR) / BUFFER;
        uint256 tolerance = threshold / 100; // 1% tolerance

        return reserve <= threshold + tolerance && reserve >= threshold - tolerance;
    }

    function _testFeeDiscrepancy(bool xNearDepletion, bool yNearDepletion) private {
        console.log("Testing fee calculation discrepancy...");

        // This would require more complex manipulation to test the exact
        // mathematical conditions where fee calculations might differ
        // For now, record as a test case
        testResults.push(TestResult({
            vulnerabilityExists: false,
            valueExtracted: 0,
            description: "Fee discrepancy analysis - Requires deeper mathematical analysis"
        }));
    }
    
    /**
     * @notice Final analysis and conclusion
     */
    function test_FinalAnalysis() public {
        console.log("\n=== FINAL VULNERABILITY ANALYSIS ===");
        
        bool anyVulnerabilityFound = false;
        uint256 totalValueExtracted = 0;
        
        for (uint256 i = 0; i < testResults.length; i++) {
            TestResult memory result = testResults[i];
            console.log("Test:", result.description);
            console.log("Vulnerable:", result.vulnerabilityExists);
            console.log("Value extracted:", result.valueExtracted);
            console.log("---");
            
            if (result.vulnerabilityExists) {
                anyVulnerabilityFound = true;
                totalValueExtracted += result.valueExtracted;
            }
        }
        
        console.log("\n=== CONCLUSION ===");
        if (anyVulnerabilityFound) {
            console.log("VULNERABILITY CONFIRMED!");
            console.log("Total value that could be extracted:", totalValueExtracted);
        } else {
            console.log("NO VULNERABILITY FOUND");
            console.log("The K invariant appears to be properly protected");
        }
        
        // Assert the final conclusion
        assertFalse(anyVulnerabilityFound, "Vulnerability should not exist in a secure system");
    }
}
