// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {console} from 'forge-std/console.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';

import {AmmalgamFactory, PairFactory} from 'contracts/factories/AmmalgamFactory.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IFactoryCallback} from 'contracts/interfaces/factories/IFactoryCallback.sol';
import {IAmmalgamERC20} from 'contracts/interfaces/tokens/IAmmalgamERC20.sol';
import {deployFactory} from 'contracts/utils/deployHelper.sol';
import {SaturationAndGeometricTWAPState} from 'contracts/SaturationAndGeometricTWAPState.sol';
import {DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG} from 'contracts/libraries/constants.sol';

/**
 * @title Factory Access Control Vulnerability POC
 * @notice This test investigates the potential vulnerability in AmmalgamFactory.generateTokensWithinFactory()
 * where any contract can call the function during pair creation to steal tokens.
 */
contract FactoryAccessControlVulnerabilityPOC is Test {
    IAmmalgamFactory private factory;
    address private attacker;
    address private legitimateUser;
    
    // Test tokens
    address private tokenA = address(0x1111);
    address private tokenB = address(0x2222);
    
    // Attack results tracking
    struct AttackResult {
        bool successful;
        address attackerAddress;
        uint256 tokensReceived;
        string description;
    }
    
    AttackResult[] private attackResults;
    
    function setUp() public {
        attacker = address(0x3333);
        legitimateUser = address(0x4444);
        
        // Deploy factory with all required components
        SaturationAndGeometricTWAPState saturationState = 
            new SaturationAndGeometricTWAPState(DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG);
        
        factory = deployFactory(
            address(this), // feeToSetter
            new PairFactory(),
            address(saturationState)
        );
        
        console.log("=== Factory Access Control Vulnerability POC Setup ===");
        console.log("Factory deployed at:", address(factory));
        console.log("Attacker address:", attacker);
        console.log("Legitimate user:", legitimateUser);
    }
    
    /**
     * @notice Test 1: Direct attack on generateTokensWithinFactory
     */
    function test_DirectAttackOnGenerateTokens() public {
        console.log("\n=== Test 1: Direct Attack on generateTokensWithinFactory ===");
        
        // First, try to call generateTokensWithinFactory without any config
        vm.startPrank(attacker);
        
        try factory.generateTokensWithinFactory() {
            console.log("ERROR: Direct attack succeeded without config!");
            attackResults.push(AttackResult({
                successful: true,
                attackerAddress: attacker,
                tokensReceived: 6, // 6 tokens created
                description: "Direct attack without config"
            }));
        } catch {
            console.log("Direct attack failed (expected) - no config exists");
            attackResults.push(AttackResult({
                successful: false,
                attackerAddress: attacker,
                tokensReceived: 0,
                description: "Direct attack without config - failed as expected"
            }));
        }
        
        vm.stopPrank();
    }
    
    /**
     * @notice Test 2: Race condition attack during pair creation
     */
    function test_RaceConditionAttackDuringPairCreation() public {
        console.log("\n=== Test 2: Race Condition Attack During Pair Creation ===");
        
        // Deploy malicious contract that will attack during pair creation
        MaliciousContract maliciousContract = new MaliciousContract(factory);
        
        console.log("Deployed malicious contract at:", address(maliciousContract));
        
        // Attempt the race condition attack
        vm.startPrank(legitimateUser);
        
        console.log("Legitimate user attempting to create pair...");
        
        try factory.createPair(tokenA, tokenB) {
            console.log("Pair creation completed");
            
            // Check if malicious contract succeeded in stealing tokens
            bool attackSucceeded = maliciousContract.attackSucceeded();
            uint256 tokensStolen = maliciousContract.tokensReceived();
            
            console.log("Attack succeeded:", attackSucceeded);
            console.log("Tokens stolen:", tokensStolen);
            
            attackResults.push(AttackResult({
                successful: attackSucceeded,
                attackerAddress: address(maliciousContract),
                tokensReceived: tokensStolen,
                description: "Race condition attack during pair creation"
            }));
            
        } catch Error(string memory reason) {
            console.log("Pair creation failed:", reason);
            attackResults.push(AttackResult({
                successful: false,
                attackerAddress: address(maliciousContract),
                tokensReceived: 0,
                description: string(abi.encodePacked("Pair creation failed: ", reason))
            }));
        }
        
        vm.stopPrank();
    }
    
    /**
     * @notice Test 3: Front-running attack simulation
     */
    function test_FrontRunningAttackSimulation() public {
        console.log("\n=== Test 3: Front-Running Attack Simulation ===");
        
        // Simulate a front-running scenario where attacker sees createPair transaction
        // and tries to call generateTokensWithinFactory in the same block
        
        FrontRunningAttacker frontRunner = new FrontRunningAttacker(factory);
        
        console.log("Deployed front-running attacker at:", address(frontRunner));
        
        // Simulate the attack
        vm.startPrank(legitimateUser);
        
        console.log("Simulating front-running attack...");
        
        // In a real scenario, the attacker would monitor the mempool and front-run
        // Here we simulate by having the attacker contract attempt the attack
        bool attackResult = frontRunner.attemptFrontRunningAttack(tokenA, tokenB);
        
        console.log("Front-running attack result:", attackResult);
        
        attackResults.push(AttackResult({
            successful: attackResult,
            attackerAddress: address(frontRunner),
            tokensReceived: attackResult ? 6 : 0,
            description: "Front-running attack simulation"
        }));
        
        vm.stopPrank();
    }
    
    /**
     * @notice Test 4: Measure impact of successful attack
     */
    function test_MeasureAttackImpact() public {
        console.log("\n=== Test 4: Measuring Attack Impact ===");
        
        // Calculate the potential impact if the attack succeeds
        console.log("Analyzing potential impact of successful attack:");
        console.log("- Attacker could receive 6 tokens intended for legitimate pair");
        console.log("- Legitimate pair creation would fail");
        console.log("- Protocol integrity would be compromised");
        console.log("- Users could lose funds or access to trading pairs");
        
        // This test measures theoretical impact
        attackResults.push(AttackResult({
            successful: false, // Theoretical analysis
            attackerAddress: address(0),
            tokensReceived: 0,
            description: "Impact analysis - theoretical damage assessment"
        }));
    }
    
    /**
     * @notice Final analysis and conclusion
     */
    function test_FinalVulnerabilityAnalysis() public {
        console.log("\n=== FINAL FACTORY VULNERABILITY ANALYSIS ===");
        
        bool anyAttackSucceeded = false;
        uint256 totalTokensStolen = 0;
        
        for (uint256 i = 0; i < attackResults.length; i++) {
            AttackResult memory result = attackResults[i];
            console.log("Attack:", result.description);
            console.log("Successful:", result.successful);
            console.log("Tokens stolen:", result.tokensReceived);
            console.log("---");
            
            if (result.successful) {
                anyAttackSucceeded = true;
                totalTokensStolen += result.tokensReceived;
            }
        }
        
        console.log("\n=== CONCLUSION ===");
        if (anyAttackSucceeded) {
            console.log("VULNERABILITY CONFIRMED!");
            console.log("Factory access control can be bypassed");
            console.log("Total tokens that could be stolen:", totalTokensStolen);
            console.log("Impact: HIGH - Unauthorized token generation possible");
        } else {
            console.log("NO EXPLOITABLE VULNERABILITY FOUND");
            console.log("Factory access control appears secure");
            console.log("All attack attempts failed as expected");
        }
        
        // The test should fail if vulnerability exists (for security testing)
        assertFalse(anyAttackSucceeded, "Factory should not allow unauthorized token generation");
    }
}

/**
 * @notice Malicious contract that attempts to steal tokens during pair creation
 */
contract MaliciousContract {
    IAmmalgamFactory private factory;
    bool public attackSucceeded = false;
    uint256 public tokensReceived = 0;
    
    constructor(IAmmalgamFactory _factory) {
        factory = _factory;
    }
    
    // This function would be called during the vulnerable window
    function attemptAttack() external {
        try factory.generateTokensWithinFactory() returns (IERC20, IERC20, IAmmalgamERC20[6] memory tokens) {
            attackSucceeded = true;
            tokensReceived = 6; // Successfully received 6 tokens
            
            // In a real attack, the attacker would now control these tokens
            console.log("ATTACK SUCCEEDED! Malicious contract received tokens:");
            for (uint256 i = 0; i < 6; i++) {
                console.log("Token", i, ":", address(tokens[i]));
            }
        } catch {
            attackSucceeded = false;
            tokensReceived = 0;
        }
    }
}

/**
 * @notice Front-running attacker contract
 */
contract FrontRunningAttacker {
    IAmmalgamFactory private factory;
    
    constructor(IAmmalgamFactory _factory) {
        factory = _factory;
    }
    
    function attemptFrontRunningAttack(address tokenA, address tokenB) external returns (bool) {
        // In a real front-running attack, this would be more sophisticated
        // The attacker would monitor for createPair transactions and try to call
        // generateTokensWithinFactory during the vulnerable window
        
        try factory.generateTokensWithinFactory() {
            console.log("Front-running attack succeeded!");
            return true;
        } catch {
            console.log("Front-running attack failed");
            return false;
        }
    }
}
