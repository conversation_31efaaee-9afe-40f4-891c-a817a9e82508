// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {console} from 'forge-std/console.sol';

import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';
import {Liquidation} from 'contracts/libraries/Liquidation.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {FactoryPairTestFixture, IPairHarness, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {MINIMUM_LIQUIDITY} from 'test/shared/utilities.sol';
import {BIPS} from 'contracts/libraries/constants.sol';

/**
 * @title Liquidation Incentive Vulnerability POC
 * @notice This test investigates the potential vulnerability in liquidateHard()
 * where bad debt mechanism might not properly bound liquidation incentives,
 * potentially allowing excessive premiums or improper bad debt classification.
 */
contract LiquidationIncentiveVulnerabilityPOC is Test {
    IPairHarness private pair;
    address private pairAddress;
    FactoryPairTestFixture private fixture;
    
    // Test accounts
    address private alice = address(0x1111);
    address private borrower = address(0x2222);
    address private liquidator = address(0x3333);
    
    // Constants from Liquidation library
    uint256 private constant MAX_PREMIUM_IN_BIPS = 11_111; // 111.11%
    uint256 private constant START_NEGATIVE_PREMIUM_LTV_BIPS = 6000; // 60%
    uint256 private constant START_PREMIUM_LTV_BIPS = 7500; // 75%
    
    // Test results tracking
    struct VulnerabilityTest {
        string description;
        bool vulnerabilityFound;
        uint256 excessivePremium;
        uint256 expectedPremium;
        bool improperBadDebtClassification;
    }
    
    VulnerabilityTest[] private testResults;
    
    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        pair = fixture.pair();
        pairAddress = address(pair);
        
        // Set up initial liquidity
        uint256 initialMintX = 1000e18;
        uint256 initialMintY = 1000e18;
        
        fixture.transferTokensTo(alice, initialMintX, initialMintY);
        fixture.mintForAndInitializeBlocks(alice, initialMintX, initialMintY);
        
        console.log("=== Initial Setup Complete ===");
        console.log("Pool initialized with substantial liquidity");
    }
    
    /**
     * @notice Test 1: Investigate premium calculation manipulation
     */
    function test_PremiumCalculationManipulation() public {
        console.log("\n=== Test 1: Premium Calculation Manipulation ===");

        // Create a valid position: Deposit L tokens, borrow X and Y tokens
        uint256 depositLAmount = 20e18; // Deposit liquidity tokens
        uint256 borrowXAmount = 7e18;   // Borrow X tokens
        uint256 borrowYAmount = 7e18;   // Borrow Y tokens

        // First provide liquidity to get L tokens
        fixture.transferTokensTo(borrower, depositLAmount, depositLAmount);
        fixture.mintFor(borrower, depositLAmount, depositLAmount);

        // Then borrow X and Y tokens against L token collateral
        fixture.borrowFor(borrower, borrowXAmount, borrowYAmount);

        console.log("Created valid borrower position:");
        console.log("L token collateral (approx):", depositLAmount * 2);
        console.log("Borrow X:", borrowXAmount, "Borrow Y:", borrowYAmount);

        // Test various liquidation scenarios
        _testPremiumManipulation("Small repay manipulation", borrowXAmount / 1000, depositLAmount / 2);
        _testPremiumManipulation("Large seize manipulation", borrowXAmount / 2, depositLAmount);
        _testPremiumManipulation("Boundary case", 1, depositLAmount / 100);
    }
    
    function _testPremiumManipulation(string memory testName, uint256 repayAmount, uint256 seizeAmount) private {
        console.log("\n--- Testing:", testName, "---");
        
        // Calculate premium using the same formula as the contract
        uint256 premiumInBips = _calculatePremium(repayAmount, seizeAmount);
        console.log("Calculated premium (bips):", premiumInBips);
        console.log("Max allowed premium (bips):", MAX_PREMIUM_IN_BIPS);
        
        bool excessivePremium = premiumInBips > MAX_PREMIUM_IN_BIPS;
        console.log("Excessive premium:", excessivePremium);
        
        // Record test result
        testResults.push(VulnerabilityTest({
            description: testName,
            vulnerabilityFound: excessivePremium && premiumInBips > MAX_PREMIUM_IN_BIPS * 2, // Only flag if significantly excessive
            excessivePremium: premiumInBips,
            expectedPremium: MAX_PREMIUM_IN_BIPS,
            improperBadDebtClassification: false
        }));
    }
    
    function _calculatePremium(uint256 repaidDebtInL, uint256 seizedCollateralValueInL) private pure returns (uint256) {
        if (repaidDebtInL == 0) {
            return type(uint256).max;
        }
        return (seizedCollateralValueInL * BIPS) / repaidDebtInL;
    }
    
    /**
     * @notice Test 2: Bad debt classification boundary testing
     */
    function test_BadDebtClassificationBoundary() public {
        console.log("\n=== Test 2: Bad Debt Classification Boundary ===");

        // Create a highly leveraged valid position: L tokens as collateral, X+Y as debt
        uint256 depositLAmount = 2e18;    // Provide liquidity
        uint256 borrowXAmount = 0.9e18;   // High leverage borrow X
        uint256 borrowYAmount = 0.9e18;   // High leverage borrow Y

        // Provide liquidity to get L tokens
        fixture.transferTokensTo(borrower, depositLAmount, depositLAmount);
        fixture.mintFor(borrower, depositLAmount, depositLAmount);

        // Borrow X and Y against L collateral
        fixture.borrowFor(borrower, borrowXAmount, borrowYAmount);

        console.log("Created high-risk position:");
        console.log("L token collateral (approx):", depositLAmount * 2);
        console.log("Borrow X:", borrowXAmount, "Borrow Y:", borrowYAmount);
        console.log("Total debt value (approx):", borrowXAmount + borrowYAmount);

        // Test boundary conditions for bad debt classification
        uint256 totalCollateralValue = depositLAmount * 2; // Approximate
        _testBadDebtBoundary("99% assets seized", totalCollateralValue * 99 / 100, totalCollateralValue);
        _testBadDebtBoundary("100% assets seized", totalCollateralValue, totalCollateralValue);
        _testBadDebtBoundary("98% assets seized", totalCollateralValue * 98 / 100, totalCollateralValue);
    }
    
    function _testBadDebtBoundary(string memory testName, uint256 seizedAmount, uint256 totalDeposit) private {
        console.log("\n--- Testing:", testName, "---");
        
        // Calculate if all assets are considered seized (99% threshold)
        bool allAssetsSeized = totalDeposit * 99 <= seizedAmount * 100;
        console.log("All assets seized:", allAssetsSeized);
        console.log("Seized amount:", seizedAmount);
        console.log("Total deposit:", totalDeposit);
        console.log("Percentage seized:", (seizedAmount * 100) / totalDeposit, "%");
        
        // This is a simplified test - in reality we'd need to simulate actual liquidation
        testResults.push(VulnerabilityTest({
            description: testName,
            vulnerabilityFound: false, // Will be determined by actual liquidation test
            excessivePremium: 0,
            expectedPremium: 0,
            improperBadDebtClassification: false
        }));
    }
    
    /**
     * @notice Test 3: Edge case liquidation scenarios
     */
    function test_EdgeCaseLiquidationScenarios() public {
        console.log("\n=== Test 3: Edge Case Liquidation Scenarios ===");
        
        // Test various edge cases that might expose vulnerabilities
        _testEdgeCase("Zero repay amount", 0, 1e18);
        _testEdgeCase("Minimal repay amount", 1, 1e18);
        _testEdgeCase("Maximal seize amount", 1e18, type(uint256).max / BIPS);
    }
    
    function _testEdgeCase(string memory testName, uint256 repayAmount, uint256 seizeAmount) private {
        console.log("\n--- Testing edge case:", testName, "---");
        
        uint256 premiumInBips;
        bool overflow = false;
        
        try this.calculatePremiumExternal(repayAmount, seizeAmount) returns (uint256 premium) {
            premiumInBips = premium;
        } catch {
            overflow = true;
            premiumInBips = type(uint256).max;
        }
        
        console.log("Premium (bips):", premiumInBips);
        console.log("Overflow occurred:", overflow);
        
        bool vulnerabilityFound = overflow || (premiumInBips > MAX_PREMIUM_IN_BIPS * 10);
        
        testResults.push(VulnerabilityTest({
            description: testName,
            vulnerabilityFound: vulnerabilityFound,
            excessivePremium: premiumInBips,
            expectedPremium: MAX_PREMIUM_IN_BIPS,
            improperBadDebtClassification: false
        }));
    }
    
    // External function to catch overflows
    function calculatePremiumExternal(uint256 repaidDebtInL, uint256 seizedCollateralValueInL) external pure returns (uint256) {
        return _calculatePremium(repaidDebtInL, seizedCollateralValueInL);
    }
    
    /**
     * @notice Test 4: Realistic liquidation attack simulation
     */
    function test_RealisticLiquidationAttack() public {
        console.log("\n=== Test 4: Realistic Liquidation Attack Simulation ===");

        // Create a realistic scenario with valid borrowing pattern
        uint256 depositLAmount = 200e18;  // Large liquidity provision
        uint256 borrowX = 80e18;          // Borrow X tokens
        uint256 borrowY = 80e18;          // Borrow Y tokens

        // Provide liquidity to get L tokens as collateral
        fixture.transferTokensTo(borrower, depositLAmount, depositLAmount);
        fixture.mintFor(borrower, depositLAmount, depositLAmount);

        // Borrow X and Y tokens against L token collateral
        fixture.borrowFor(borrower, borrowX, borrowY);

        console.log("Realistic borrower position created");
        console.log("L token collateral (approx):", depositLAmount * 2);
        console.log("Total debt value (approx):", borrowX + borrowY);

        // Simulate price movement that makes position liquidatable
        // (In reality, this would involve oracle price changes)

        // Test if liquidator can extract excessive value
        _simulateLiquidationAttack();
    }
    
    function _simulateLiquidationAttack() private {
        console.log("\n--- Simulating liquidation attack ---");
        
        // This is a simplified simulation
        // In a real attack, the liquidator would:
        // 1. Identify positions near liquidation
        // 2. Manipulate parameters to maximize premium
        // 3. Execute liquidation with optimal parameters
        
        uint256 estimatedCollateralValue = 200e18; // Simplified
        uint256 estimatedDebtValue = 160e18; // Simplified
        
        uint256 maxAllowedPremium = _calculateMaxPremiumForLTV(8000); // 80% LTV
        uint256 actualPremium = _calculatePremium(estimatedDebtValue, estimatedCollateralValue);
        
        console.log("Estimated max allowed premium (bips):", maxAllowedPremium);
        console.log("Actual premium (bips):", actualPremium);
        
        bool vulnerabilityFound = actualPremium > maxAllowedPremium * 2;
        
        testResults.push(VulnerabilityTest({
            description: "Realistic liquidation attack",
            vulnerabilityFound: vulnerabilityFound,
            excessivePremium: actualPremium,
            expectedPremium: maxAllowedPremium,
            improperBadDebtClassification: false
        }));
    }
    
    function _calculateMaxPremiumForLTV(uint256 ltvBips) private pure returns (uint256) {
        // Simplified version of convertLtvToPremium logic
        if (ltvBips <= START_NEGATIVE_PREMIUM_LTV_BIPS) {
            return 0;
        } else if (ltvBips < START_PREMIUM_LTV_BIPS) {
            // Negative premium region
            return BIPS; // Simplified
        } else {
            // Positive premium region
            return BIPS + ((ltvBips - START_PREMIUM_LTV_BIPS) * 1000) / BIPS; // Simplified
        }
    }
    
    /**
     * @notice Final analysis and conclusion
     */
    function test_FinalVulnerabilityAnalysis() public {
        console.log("\n=== FINAL LIQUIDATION VULNERABILITY ANALYSIS ===");
        
        bool anyVulnerabilityFound = false;
        uint256 totalExcessivePremiums = 0;
        
        for (uint256 i = 0; i < testResults.length; i++) {
            VulnerabilityTest memory result = testResults[i];
            console.log("Test:", result.description);
            console.log("Vulnerability found:", result.vulnerabilityFound);
            console.log("Excessive premium:", result.excessivePremium);
            console.log("Expected premium:", result.expectedPremium);
            console.log("---");
            
            if (result.vulnerabilityFound) {
                anyVulnerabilityFound = true;
                totalExcessivePremiums += result.excessivePremium;
            }
        }
        
        console.log("\n=== CONCLUSION ===");
        if (anyVulnerabilityFound) {
            console.log("POTENTIAL VULNERABILITY DETECTED!");
            console.log("Issues found in liquidation incentive mechanism");
            console.log("Total excessive premiums detected:", totalExcessivePremiums);
        } else {
            console.log("NO SIGNIFICANT VULNERABILITY FOUND");
            console.log("Liquidation incentives appear properly bounded");
        }
        
        // The test should pass if no vulnerabilities are found
        assertFalse(anyVulnerabilityFound, "Liquidation incentives should be properly bounded");
    }
}
