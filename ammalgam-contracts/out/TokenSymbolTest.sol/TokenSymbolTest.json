{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "stringToBytes32", "inputs": [{"name": "source", "type": "string", "internalType": "string"}], "outputs": [{"name": "result", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "testUniSymbol32Bytes", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testUniSymbol32BytesEmpty", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testUniSymbol33CharsSymbol", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testUniSymbolCAPsReturns", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testUniSymbolEmptyString", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testUniSymbolFuzz", "inputs": [{"name": "name", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testUniSymbolReturnBomb", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testUniSymbolReturns", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testUniSymbolReturnsZeroAddress", "inputs": [], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "testUniSymbolTokenWithoutSymbol", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "718:6933:167:-:0;;;3126:44:97;;;3166:4;-1:-1:-1;;3126:44:97;;;;;;;;1016:26:107;;;;;;;;;;;1162:9:167;1139:32;;718:6933;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561000f575f5ffd5b5060043610610148575f3560e01c8063b0464fdc116100bf578063d0d9d4ce11610079578063d0d9d4ce14610249578063e16eafba14610251578063e20c9f7114610259578063e4970fe014610261578063f22e0b3c14610269578063fa7626d41461027c575f5ffd5b8063b0464fdc146101f0578063b5508aa9146101f8578063ba414fa614610200578063c5505ee014610218578063cbdb0cb714610220578063cfb5192814610228575f5ffd5b80633f7286f4116101105780633f7286f41461019957806366d9a9a0146101a157806372323e86146101b65780637c6f90eb146101be57806385226c81146101c6578063916a17c6146101db575f5ffd5b806313893aad1461014c5780631ed7831c146101565780632045ad11146101745780632ade38801461017c5780633e5e3c2314610191575b5f5ffd5b610154610289565b005b61015e610389565b60405161016b91906118bb565b60405180910390f35b6101546103e9565b610184610469565b60405161016b9190611934565b61015e6105a5565b61015e610603565b6101a9610661565b60405161016b9190611a41565b6101546107c5565b6101546108d6565b6101ce610932565b60405161016b9190611abf565b6101e36109fd565b60405161016b9190611b16565b6101e3610ade565b6101ce610bbf565b610208610c8a565b604051901515815260200161016b565b610154610d2a565b610154610d7e565b61023b610236366004611bf9565b610e38565b60405190815260200161016b565b610154610e55565b610154610ec1565b61015e610f65565b610154610fc3565b610154610277366004611bf9565b61103c565b601f546102089060ff1681565b6027546040516102989061186f565b908152602001604051809103905ff0801580156102b7573d5f5f3e3d5ffd5b50602680546001600160a01b0319166001600160a01b039290921691821790555f906102e290611149565b90506103868160265f9054906101000a90046001600160a01b03166001600160a01b03166395d89b416040518163ffffffff1660e01b81526004015f60405180830381865afa158015610337573d5f5f3e3d5ffd5b505050506040513d5f823e601f3d908101601f1916820160405261035e9190810190611c73565b6040518060400160405280601981526020015f5160206163355f395f51905f528152506113d2565b50565b606060168054806020026020016040519081016040528092919081815260200182805480156103df57602002820191905f5260205f20905b81546001600160a01b031681526001909101906020018083116103c1575b5050505050905090565b6040516103f59061187c565b604051809103905ff08015801561040e573d5f5f3e3d5ffd5b50602180546001600160a01b0319166001600160a01b039290921691821790555f61043882611149565b9050610465816104478461143e565b6040518060600160405280602a81526020016162ea602a91396113d2565b5050565b6060601e805480602002602001604051908101604052809291908181526020015f905b8282101561059c575f84815260208082206040805180820182526002870290920180546001600160a01b03168352600181018054835181870281018701909452808452939591948681019491929084015b82821015610585578382905f5260205f200180546104fa90611ce8565b80601f016020809104026020016040519081016040528092919081815260200182805461052690611ce8565b80156105715780601f1061054857610100808354040283529160200191610571565b820191905f5260205f20905b81548152906001019060200180831161055457829003601f168201915b5050505050815260200190600101906104dd565b50505050815250508152602001906001019061048c565b50505050905090565b606060188054806020026020016040519081016040528092919081815260200182805480156103df57602002820191905f5260205f209081546001600160a01b031681526001909101906020018083116103c1575050505050905090565b606060178054806020026020016040519081016040528092919081815260200182805480156103df57602002820191905f5260205f209081546001600160a01b031681526001909101906020018083116103c1575050505050905090565b6060601b805480602002602001604051908101604052809291908181526020015f905b8282101561059c578382905f5260205f2090600202016040518060400160405290815f820180546106b490611ce8565b80601f01602080910402602001604051908101604052809291908181526020018280546106e090611ce8565b801561072b5780601f106107025761010080835404028352916020019161072b565b820191905f5260205f20905b81548152906001019060200180831161070e57829003601f168201915b50505050508152602001600182018054806020026020016040519081016040528092919081815260200182805480156107ad57602002820191905f5260205f20905f905b82829054906101000a900460e01b6001600160e01b0319168152602001906004019060208260030104928301926001038202915080841161076f5790505b50505050508152505081526020019060010190610684565b6040516107d190611888565b604051809103905ff0801580156107ea573d5f5f3e3d5ffd5b50601f8054610100600160a81b0319166101006001600160a01b03938416810291909117918290555f92610822929190910416611149565b90505f6108a7601f60019054906101000a90046001600160a01b03166001600160a01b03166395d89b416040518163ffffffff1660e01b81526004015f60405180830381865afa158015610878573d5f5f3e3d5ffd5b505050506040513d5f823e601f3d908101601f1916820160405261089f9190810190611c73565b5f602061147c565b905061046582826040518060400160405280601981526020015f5160206163355f395f51905f528152506113d2565b6040516108e290611895565b60208082525f90820152604001604051809103905ff080158015610908573d5f5f3e3d5ffd5b50602280546001600160a01b0319166001600160a01b039290921691821790555f61043882611149565b6060601a805480602002602001604051908101604052809291908181526020015f905b8282101561059c578382905f5260205f2001805461097290611ce8565b80601f016020809104026020016040519081016040528092919081815260200182805461099e90611ce8565b80156109e95780601f106109c0576101008083540402835291602001916109e9565b820191905f5260205f20905b8154815290600101906020018083116109cc57829003601f168201915b505050505081526020019060010190610955565b6060601d805480602002602001604051908101604052809291908181526020015f905b8282101561059c575f8481526020908190206040805180820182526002860290920180546001600160a01b03168352600181018054835181870281018701909452808452939491938583019392830182828015610ac657602002820191905f5260205f20905f905b82829054906101000a900460e01b6001600160e01b03191681526020019060040190602082600301049283019260010382029150808411610a885790505b50505050508152505081526020019060010190610a20565b6060601c805480602002602001604051908101604052809291908181526020015f905b8282101561059c575f8481526020908190206040805180820182526002860290920180546001600160a01b03168352600181018054835181870281018701909452808452939491938583019392830182828015610ba757602002820191905f5260205f20905f905b82829054906101000a900460e01b6001600160e01b03191681526020019060040190602082600301049283019260010382029150808411610b695790505b50505050508152505081526020019060010190610b01565b60606019805480602002602001604051908101604052809291908181526020015f905b8282101561059c578382905f5260205f20018054610bff90611ce8565b80601f0160208091040260200160405190810160405280929190818152602001828054610c2b90611ce8565b8015610c765780601f10610c4d57610100808354040283529160200191610c76565b820191905f5260205f20905b815481529060010190602001808311610c5957829003601f168201915b505050505081526020019060010190610be2565b6008545f9060ff1615610ca1575060085460ff1690565b604051630667f9d760e41b8152737109709ecfa91a80626ff3989d68f67f5b1dd12d600482018190526519985a5b195960d21b60248301525f9163667f9d7090604401602060405180830381865afa158015610cff573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190610d239190611d20565b1415905090565b5f610d345f611149565b905061038681610d435f61143e565b6040518060400160405280601b81526020017f53686f756c642072657475726e207468652030206164647265737300000000008152506113d2565b5f60405180606001604052806021815260200161631460219139905080604051610da790611895565b610db19190611d37565b604051809103905ff080158015610dca573d5f5f3e3d5ffd5b50602280546001600160a01b0319166001600160a01b039290921691821790555f90610df590611149565b90505f610e04835f602061147c565b9050610e3382826040518060400160405280601981526020015f5160206163355f395f51905f528152506113d2565b505050565b80515f9082908203610e4c57505f92915050565b50506020015190565b610e6c60405180602001604052805f815250610e38565b604051610e78906118a2565b908152602001604051809103905ff080158015610e97573d5f5f3e3d5ffd5b50602080546001600160a01b0319166001600160a01b039290921691821790555f61043882611149565b60408051808201825260048152634341505360e01b602082015290518190610ee8906118ae565b610ef29190611d37565b604051809103905ff080158015610f0b573d5f5f3e3d5ffd5b50602380546001600160a01b0319166001600160a01b039290921691821790555f90610f3690611149565b905061046581836040518060400160405280601981526020015f5160206163355f395f51905f528152506113d2565b606060158054806020026020016040519081016040528092919081815260200182805480156103df57602002820191905f5260205f209081546001600160a01b031681526001909101906020018083116103c1575050505050905090565b60408051808201909152600381526241424360e81b6020820152610fe681610e38565b604051610ff2906118a2565b908152602001604051809103905ff080158015611011573d5f5f3e3d5ffd5b50602080546001600160a01b0319166001600160a01b039290921691821790555f90610f3690611149565b8051604051632631f2b160e11b81528115156004820152737109709ecfa91a80626ff3989d68f67f5b1dd12d90634c63e562906024015f6040518083038186803b158015611088575f5ffd5b505afa15801561109a573d5f5f3e3d5ffd5b5050505080602010156110ab575060205b816040516110b890611895565b6110c29190611d37565b604051809103905ff0801580156110db573d5f5f3e3d5ffd5b50602280546001600160a01b0319166001600160a01b039290921691821790555f9061110690611149565b90505f611114845f8561147c565b905061114382826040518060400160405280601981526020015f5160206163355f395f51905f528152506113d2565b50505050565b6040805160048152602481019091526020810180516001600160e01b03166395d89b4160e01b1790526060905f90819061118a908590614e209086906115e8565b91509150816111d4576040805160048152602481019091526020810180516001600160e01b0316631eedf1af60e31b1790526111ce908590614e20906060906115e8565b90925090505b8180156111e2575080516060145b15611281575f5f828060200190518101906111fd9190611d49565b9150915081602014801561121057505f81115b1561127e5761122081602061166c565b90505f8167ffffffffffffffff81111561123c5761123c611b8d565b6040519080825280601f01601f191660200182016040528015611266576020820181803683370190505b50606094909401516020850152509195945050505050565b50505b81801561128f575080516020145b156113c1575f5b8151811080156112ca5750602060f81b8282815181106112b8576112b8611d6b565b01602001516001600160f81b03191610155b80156112fa5750607e60f81b8282815181106112e8576112e8611d6b565b01602001516001600160f81b03191611155b15611311578061130981611d93565b915050611296565b80156113bf575f8167ffffffffffffffff81111561133157611331611b8d565b6040519080825280601f01601f19166020018201604052801561135b576020820181803683370190505b5090505f5b828110156113b55783818151811061137a5761137a611d6b565b602001015160f81c60f81b82828151811061139757611397611d6b565b60200101906001600160f81b03191690815f1a905350600101611360565b5095945050505050565b505b6113ca8461143e565b949350505050565b6040516306decadb60e31b8152737109709ecfa91a80626ff3989d68f67f5b1dd12d906336f656d89061140d90869086908690600401611dab565b5f6040518083038186803b158015611423575f5ffd5b505afa158015611435573d5f5f3e3d5ffd5b50505050505050565b604051606082811b6bffffffffffffffffffffffff191660208301529061147690603401604051602081830303815290604052611683565b92915050565b6060838284106114e35760405162461bcd60e51b815260206004820152602760248201527f537461727420696e646578206d757374206265206c657373207468616e20656e6044820152660c840d2dcc8caf60cb1b60648201526084015b60405180910390fd5b80518311156115345760405162461bcd60e51b815260206004820152601760248201527f456e6420696e646578206f7574206f6620626f756e647300000000000000000060448201526064016114da565b5f61153f8585611ded565b67ffffffffffffffff81111561155757611557611b8d565b6040519080825280601f01601f191660200182016040528015611581576020820181803683370190505b509050845b848110156113b5578281815181106115a0576115a0611d6b565b01602001516001600160f81b031916826115ba8884611ded565b815181106115ca576115ca611d6b565b60200101906001600160f81b03191690815f1a905350600101611586565b5f60605f5f5f8661ffff1667ffffffffffffffff81111561160b5761160b611b8d565b6040519080825280601f01601f191660200182016040528015611635576020820181803683370190505b5090505f5f8751602089018c8cfa91503d925086831115611654578692505b828152825f602083013e909890975095505050505050565b5f81831061167a578161167c565b825b9392505050565b60605f825160026116949190611e00565b61169f906002611e17565b67ffffffffffffffff8111156116b7576116b7611b8d565b6040519080825280601f01601f1916602001820160405280156116e1576020820181803683370190505b509050600360fc1b815f815181106116fb576116fb611d6b565b60200101906001600160f81b03191690815f1a905350600f60fb1b8160018151811061172957611729611d6b565b60200101906001600160f81b03191690815f1a90535060025f5b8451811015611866575f600486838151811061176157611761611d6b565b602001015160f81c60f81b60f81c60ff16901c60ff1690505f86838151811061178c5761178c611d6b565b60209101015160f81c600f1690506117a5600a83611e2a565b6117b0906027611e00565b6117bb836030611e17565b6117c59190611e17565b60f81b85856117d381611d93565b9650815181106117e5576117e5611d6b565b60200101906001600160f81b03191690815f1a905350611806600a82611e2a565b611811906027611e00565b61181c826030611e17565b6118269190611e17565b60f81b858561183481611d93565b96508151811061184657611846611d6b565b60200101906001600160f81b03191690815f1a9053505050600101611743565b50909392505050565b6135db80611e4a83390190565b60588061542583390190565b6105d88061547d83390190565b6103e680615a5583390190565b60c980615e3b83390190565b6103e680615f0483390190565b602080825282518282018190525f918401906040840190835b818110156118fb5783516001600160a01b03168352602093840193909201916001016118d4565b509095945050505050565b5f81518084528060208401602086015e5f602082860101526020601f19601f83011685010191505092915050565b5f602082016020835280845180835260408501915060408160051b8601019250602086015f5b828110156119f157603f19878603018452815180516001600160a01b03168652602090810151604082880181905281519088018190529101906060600582901b8801810191908801905f5b818110156119d757605f198a85030183526119c1848651611906565b60209586019590945092909201916001016119a5565b50919750505060209485019492909201915060010161195a565b50929695505050505050565b5f8151808452602084019350602083015f5b82811015611a375781516001600160e01b031916865260209586019590910190600101611a0f565b5093949350505050565b5f602082016020835280845180835260408501915060408160051b8601019250602086015f5b828110156119f157603f198786030184528151805160408752611a8d6040880182611906565b9050602082015191508681036020880152611aa881836119fd565b965050506020938401939190910190600101611a67565b5f602082016020835280845180835260408501915060408160051b8601019250602086015f5b828110156119f157603f19878603018452611b01858351611906565b94506020938401939190910190600101611ae5565b5f602082016020835280845180835260408501915060408160051b8601019250602086015f5b828110156119f157868503603f19018452815180516001600160a01b03168652602090810151604091870182905290611b77908701826119fd565b9550506020938401939190910190600101611b3c565b634e487b7160e01b5f52604160045260245ffd5b604051601f8201601f1916810167ffffffffffffffff81118282101715611bca57611bca611b8d565b604052919050565b5f67ffffffffffffffff821115611beb57611beb611b8d565b50601f01601f191660200190565b5f60208284031215611c09575f5ffd5b813567ffffffffffffffff811115611c1f575f5ffd5b8201601f81018413611c2f575f5ffd5b8035611c42611c3d82611bd2565b611ba1565b818152856020838501011115611c56575f5ffd5b816020840160208301375f91810160200191909152949350505050565b5f60208284031215611c83575f5ffd5b815167ffffffffffffffff811115611c99575f5ffd5b8201601f81018413611ca9575f5ffd5b8051611cb7611c3d82611bd2565b818152856020838501011115611ccb575f5ffd5b8160208401602083015e5f91810160200191909152949350505050565b600181811c90821680611cfc57607f821691505b602082108103611d1a57634e487b7160e01b5f52602260045260245ffd5b50919050565b5f60208284031215611d30575f5ffd5b5051919050565b602081525f61167c6020830184611906565b5f5f60408385031215611d5a575f5ffd5b505080516020909101519092909150565b634e487b7160e01b5f52603260045260245ffd5b634e487b7160e01b5f52601160045260245ffd5b5f60018201611da457611da4611d7f565b5060010190565b606081525f611dbd6060830186611906565b8281036020840152611dcf8186611906565b90508281036040840152611de38185611906565b9695505050505050565b8181038181111561147657611476611d7f565b808202811582820484141761147657611476611d7f565b8082018082111561147657611476611d7f565b5f82611e4457634e487b7160e01b5f52601260045260245ffd5b50049056fe610240604052348015610010575f5ffd5b506040516135db3803806135db83398101604081905261002f9161093c565b6040518060a0016040528060405161004690610930565b604051809103905ff08015801561005f573d5f5f3e3d5ffd5b506001600160a01b031681526020015f6001600160a01b0316815260200160405180604001604052806008815260200167416d6d616c67616d60c01b815250815260200160405180604001604052806006815260200165414d4d2d563160d01b815250815260200160018152503381806040015180604051806040016040528060018152602001603160f81b815250835f0151600a6207a1208787604001518860600151816003908161011291906109ea565b50600461011f82826109ea565b5050505f5f610133836102ad60201b60201c565b9150915081610143576012610145565b805b60ff1660a05250506001600160a01b03166080525f82900361017a576040516357d413bb60e11b815260040160405180910390fd5b60c09190915260e05260016005556001600160a01b0381166101b657604051631e4fbdf760e01b81525f60048201526024015b60405180910390fd5b6101bf81610383565b506101cb8260086103d4565b6101a0526101da8160096103d4565b6101c0528151602080840191909120610160528151908201206101805246610120526102696101605161018051604080517f8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f60208201529081019290925260608201524660808201523060a08201525f9060c00160405160208183030381529060405280519060200120905090565b61010052505030610140525080516001600160a01b039081166101e0526080820151610220526020909101511661020052506102a790503382610406565b50610b45565b60408051600481526024810182526020810180516001600160e01b031663313ce56760e01b17905290515f918291829182916001600160a01b038716916102f391610aa4565b5f60405180830381855afa9150503d805f811461032b576040519150601f19603f3d011682016040523d82523d5f602084013e610330565b606091505b509150915081801561034457506020815110155b15610377575f8180602001905181019061035e919061093c565b905060ff8111610375576001969095509350505050565b505b505f9485945092505050565b600780546001600160a01b038381166001600160a01b0319831681179093556040519116919082907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0905f90a35050565b5f6020835110156103ef576103e88361043e565b9050610400565b816103fa84826109ea565b5060ff90505b92915050565b6001600160a01b03821661042f5760405163ec442f0560e01b81525f60048201526024016101ad565b61043a5f838361047b565b5050565b5f5f829050601f81511115610468578260405163305a27a960e01b81526004016101ad9190610aba565b805161047382610aef565b179392505050565b61048683838361048b565b505050565b610496838383610560565b6001600160a01b038316158015906104b657506001600160a01b03821615155b80156104c4575060ff5f5c16155b15610486575f5f5f60036102205110156104e3578592508491506104ed565b5083915084905060015b6101e05160405163019653a160e11b81526001600160a01b038581166004830152848116602483015283151560448301529091169063032ca742906064015f604051808303815f87803b158015610542575f5ffd5b505af1158015610554573d5f5f3e3d5ffd5b50505050505050505050565b600561056b8161071c565b610576848484610745565b5f821180156105975750826001600160a01b0316846001600160a01b031614155b15610713576001600160a01b0384165f9081526006602052604081206105bc9061086b565b6001600160a01b0385165f908152600660205260408120919250906105e09061086b565b82518151919250905f5b828110156106b7575f85828151811061060557610605610b12565b602002602001015190505f5f90505b8381101561069a5785818151811061062e5761062e610b12565b60200260200101516001600160a01b0316826001600160a01b0316036106925761065a828c8c8c6108da565b5f86828151811061066d5761066d610b12565b60200260200101906001600160a01b031690816001600160a01b03168152505061069a565b600101610614565b8381036106ad576106ad828c5f8c6108da565b50506001016105ea565b505f5b8181101561070d575f8482815181106106d5576106d5610b12565b602002602001015190505f6001600160a01b0316816001600160a01b03161461070457610704815f8b8b6108da565b506001016106ba565b50505050505b60019055505050565b80546001190161073f576040516306fda65d60e31b815260040160405180910390fd5b60029055565b6001600160a01b03831661076f578060025f8282546107649190610b26565b909155506107df9050565b6001600160a01b0383165f90815260208190526040902054818110156107c15760405163391434e360e21b81526001600160a01b038516600482015260248101829052604481018390526064016101ad565b6001600160a01b0384165f9081526020819052604090209082900390555b6001600160a01b0382166107fb57600280548290039055610819565b6001600160a01b0382165f9081526020819052604090208054820190555b816001600160a01b0316836001600160a01b03167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef8360405161085e91815260200190565b60405180910390a3505050565b6060815463ffffffff60a01b811660a01c6001600160a01b0382169150604051925080600101602002830160405280835280156108d3576020830182815260015b828110156108d057858101546001600160a01b0316602082028301526001016108ac565b50505b5050919050565b60e051604051637a1f1aa960e01b80825260048201869052602482018590526044820184905291905a5f5f6064845f8c88f161092657826040603f8302041015610926573d5f833e3d82fd5b5050505050505050565b60e8806134f383390190565b5f6020828403121561094c575f5ffd5b5051919050565b634e487b7160e01b5f52604160045260245ffd5b600181811c9082168061097b57607f821691505b60208210810361099957634e487b7160e01b5f52602260045260245ffd5b50919050565b601f82111561048657805f5260205f20601f840160051c810160208510156109c45750805b601f840160051c820191505b818110156109e3575f81556001016109d0565b5050505050565b81516001600160401b03811115610a0357610a03610953565b610a1781610a118454610967565b8461099f565b6020601f821160018114610a49575f8315610a325750848201515b5f19600385901b1c1916600184901b1784556109e3565b5f84815260208120601f198516915b82811015610a785787850151825560209485019460019092019101610a58565b5084821015610a9557868401515f19600387901b60f8161c191681555b50505050600190811b01905550565b5f82518060208501845e5f920191825250919050565b602081525f82518060208401528060208501604085015e5f604082850101526040601f19601f83011684010191505092915050565b80516020808301519190811015610999575f1960209190910360031b1b16919050565b634e487b7160e01b5f52603260045260245ffd5b8082018082111561040057634e487b7160e01b5f52601160045260245ffd5b60805160a05160c05160e05161010051610120516101405161016051610180516101a0516101c0516101e05161020051610220516128ca610c295f395f81816103660152818161075b0152611dbc01525f610a2501525f8181610528015281816106db015281816111ac015281816111f001528181611522015281816115660152611e2501525f6114e301525f6114b601525f61109c01525f61107401525f610fcf01525f610ff901525f61102301525f81816104a7015261195901525f818161040601526113e901525f61189801525f81816103b6015261118a01526128ca5ff3fe608060405234801561000f575f5ffd5b506004361061028b575f3560e01c80637b4e04e811610161578063b460af94116100ca578063d505accf11610084578063d505accf146105ef578063d905777e14610602578063dd62ed3e14610615578063e5adf3ab1461064d578063ef8b30f7146105a9578063f2fde38b14610660575f5ffd5b8063b460af9414610583578063ba08765214610596578063c63d75b6146103da578063c6e6f592146105a9578063c982681b146105bc578063ce96cb77146105dc575f5ffd5b806395d89b411161011b57806395d89b4114610508578063a1291f7f14610510578063a8aa1b3114610523578063a9059cbb1461054a578063ac2c3e1d1461055d578063b3d7f6b914610570575f5ffd5b80637b4e04e81461047c5780637ecebe001461048f57806382b36169146104a257806384b0196e146104c95780638da5cb5b146104e457806394bf804d146104f5575f5ffd5b806334aa98321161020357806351924740116101bd578063519247401461040157806362a0d2cb146104285780636e553f651461043b5780636ea3d9881461044e57806370a0823114610461578063715018a614610474575f5ffd5b806334aa9832146103a25780633644e515146103ac57806338d52e0f146103b4578063402d267d146103da5780634c412cd2146103ee5780634cdad506146102d2575f5ffd5b80630a28a477116102545780630a28a4771461030857806318160ddd1461031b57806323b872dd146103235780632d6516b61461033657806330fa738c14610361578063313ce56714610388575f5ffd5b8062d137dd1461028f57806301e1d114146102b557806306fdde03146102bd57806307a2d13a146102d2578063095ea7b3146102e5575b5f5ffd5b6102a261029d36600461239d565b610673565b6040519081526020015b60405180910390f35b6102a26106d8565b6102c561079d565b6040516102ac9190612402565b6102a26102e0366004612414565b61082d565b6102f86102f336600461242b565b61083e565b60405190151581526020016102ac565b6102a2610316366004612414565b610855565b6002546102a2565b6102f8610331366004612455565b610861565b61034961034436600461242b565b610884565b6040516001600160a01b0390911681526020016102ac565b6102a27f000000000000000000000000000000000000000000000000000000000000000081565b6103906108ac565b60405160ff90911681526020016102ac565b6103aa6108ba565b005b6102a26108c5565b7f0000000000000000000000000000000000000000000000000000000000000000610349565b6102a26103e8366004612493565b505f1990565b6103aa6103fc3660046124ae565b6108ce565b6102a27f000000000000000000000000000000000000000000000000000000000000000081565b6103aa6104363660046124ae565b61093c565b6102a26104493660046124f1565b610999565b6102f861045c36600461239d565b6109ca565b6102a261046f366004612493565b6109eb565b6103aa6109f5565b6103aa61048a366004612493565b610a06565b6102a261049d366004612493565b610aa0565b6102a27f000000000000000000000000000000000000000000000000000000000000000081565b6104d1610aaa565b6040516102ac9796959493929190612514565b6007546001600160a01b0316610349565b6102a26105033660046124f1565b610aec565b6102c5610b07565b6103aa61051e366004612455565b610b16565b6103497f000000000000000000000000000000000000000000000000000000000000000081565b6102f861055836600461242b565b610b2e565b6102a261056b366004612493565b610b3b565b6102a261057e366004612414565b610b5b565b6102a26105913660046125aa565b610b67565b6102a26105a43660046125aa565b610bbd565b6102a26105b7366004612414565b610c0a565b6105cf6105ca366004612493565b610c15565b6040516102ac91906125e9565b6102a26105ea366004612493565b610c38565b6103aa6105fd366004612634565b610c4b565b6102a2610610366004612493565b610d81565b6102a261062336600461239d565b6001600160a01b039182165f90815260016020908152604080832093909416825291909152205490565b6103aa61065b366004612493565b610d8b565b6103aa61066e366004612493565b610d95565b5f6005610681815460021490565b1561069f576040516306fda65d60e31b815260040160405180910390fd5b6106a983856109ca565b156106cd576001600160a01b0383165f9081526020819052604090205491506106d1565b5f91505b5092915050565b5f7f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03166301e1d1146040518163ffffffff1660e01b815260040160c060405180830381865afa158015610735573d5f5f3e3d5ffd5b505050506040513d601f19601f8201168201806040525081019061075991906126d4565b7f00000000000000000000000000000000000000000000000000000000000000006006811061078a5761078a612755565b60200201516001600160801b0316905090565b6060600380546107ac90612769565b80601f01602080910402602001604051908101604052809291908181526020018280546107d890612769565b80156108235780601f106107fa57610100808354040283529160200191610823565b820191905f5260205f20905b81548152906001019060200180831161080657829003601f168201915b5050505050905090565b5f610838825f610dcf565b92915050565b5f3361084b818585610df9565b5060019392505050565b5f610838826001610e06565b5f3361086e858285610e30565b610879858585610e93565b506001949350505050565b6001600160a01b0382165f9081526006602052604081206108a59083610ef0565b9392505050565b5f6108b5610efb565b905090565b6108c333610f04565b565b5f6108b5610fc3565b6108d66110ec565b826001600160a01b0316846001600160a01b03167fdcbc1c05240f31ff3ad067ef1ee35ce4997762752e3a095284754544f4c709d78484604051610924929190918252602082015260400190565b60405180910390a36109368382611119565b50505050565b6109446110ec565b60408051838152602081018390526001600160a01b03808716929086169133917ffbde797d201c681b91056529119e0b02407c7bb96a4a2c75c01fc9667232c8db910160405180910390a46109363382611151565b5f5f196109aa565b60405180910390fd5b5f6109b485610c0a565b90506109c233858784611185565b949350505050565b6001600160a01b0382165f9081526006602052604081206108a5908361124d565b5f6108388261127f565b6109fd6110ec565b6108c35f611289565b60405163a50833cd60e01b81526001600160a01b0382811660048301527f0000000000000000000000000000000000000000000000000000000000000000169063a50833cd90602401602060405180830381865afa158015610a6a573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190610a8e91906127bc565b15610a9d57610a9d33826112da565b50565b5f61083882611492565b5f6060805f5f5f6060610abb6114af565b610ac36114dc565b604080515f80825260208201909252600f60f81b9b939a50919850469750309650945092509050565b5f5f195f610af985610b5b565b90506109c233858388611185565b6060600480546107ac90612769565b610b1e6110ec565b610b29838383610e93565b505050565b5f3361084b818585610e93565b6001600160a01b0381165f90815260066020526040812061083890611509565b5f610838826001610dcf565b5f5f610b7283610c38565b905080851115610b9b57828582604051633fa733bb60e21b81526004016109a19392919061279b565b5f610ba586610855565b9050610bb4338686898561151b565b95945050505050565b5f5f610bc883610d81565b905080851115610bf157828582604051632e52afbb60e21b81526004016109a19392919061279b565b5f610bfb8661082d565b9050610bb4338686848a61151b565b5f610838825f610e06565b6001600160a01b0381165f908152600660205260409020606090610838906115c4565b5f610838610c45836109eb565b5f610dcf565b83421115610c6f5760405163313c898160e11b8152600481018590526024016109a1565b5f7f6e71edae12b1b97f4d1f60370fef10105fa2faae0126114a169c64845d6126c9888888610cba8c6001600160a01b03165f908152600a6020526040902080546001810190915590565b6040805160208101969096526001600160a01b0394851690860152929091166060840152608083015260a082015260c0810186905260e0016040516020818303038152906040528051906020012090505f610d1482611633565b90505f610d238287878761165f565b9050896001600160a01b0316816001600160a01b031614610d6a576040516325c0072360e11b81526001600160a01b0380831660048301528b1660248201526044016109a1565b610d758a8a8a610df9565b50505050505050505050565b5f610838826109eb565b610a9d338261168b565b610d9d6110ec565b6001600160a01b038116610dc657604051631e4fbdf760e01b81525f60048201526024016109a1565b610a9d81611289565b5f6108a583610ddc6106d8565b6002546001866003811115610df357610df36127db565b1461172a565b610b298383836001611745565b5f6108a583610e136106d8565b6002546001866003811115610e2a57610e2a6127db565b14611817565b6001600160a01b038381165f908152600160209081526040808320938616835292905220545f198110156109365781811015610e8557828183604051637dc7a0d960e11b81526004016109a19392919061279b565b61093684848484035f611745565b6001600160a01b038316610ebc57604051634b637e8f60e11b81525f60048201526024016109a1565b6001600160a01b038216610ee55760405163ec442f0560e01b81525f60048201526024016109a1565b610b29838383611832565b5f6108a5838361183d565b5f6108b5611891565b6001600160a01b0381165f908152600660205260408120610f24906118bc565b90505f610f30836109eb565b90505f5b8251811015610936575f838281518110610f5057610f50612755565b602002602001015190507fd580c0b3363ee46dd07e6c2c50e637920eebd2b82481b0ccb43f66d1d9dded798582604051610fa09291906001600160a01b0392831681529116602082015260400190565b60405180910390a18215610fba57610fba81865f86611933565b50600101610f34565b5f306001600160a01b037f00000000000000000000000000000000000000000000000000000000000000001614801561101b57507f000000000000000000000000000000000000000000000000000000000000000046145b1561104557507f000000000000000000000000000000000000000000000000000000000000000090565b6108b5604080517f8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f60208201527f0000000000000000000000000000000000000000000000000000000000000000918101919091527f000000000000000000000000000000000000000000000000000000000000000060608201524660808201523060a08201525f9060c00160405160208183030381529060405280519060200120905090565b6007546001600160a01b031633146108c35760405163118cdaa760e01b81523360048201526024016109a1565b6001600160a01b0382166111425760405163ec442f0560e01b81525f60048201526024016109a1565b61114d5f8383611832565b5050565b6001600160a01b03821661117a57604051634b637e8f60e11b81525f60048201526024016109a1565b61114d825f83611832565b6111d17f0000000000000000000000000000000000000000000000000000000000000000857f00000000000000000000000000000000000000000000000000000000000000008561199d565b60405163f340fa0160e01b81526001600160a01b0384811660048301527f0000000000000000000000000000000000000000000000000000000000000000169063f340fa01906024015f604051808303815f87803b158015611231575f5ffd5b505af1158015611243573d5f5f3e3d5ffd5b5050505050505050565b6001600160a01b0381165f908152600160201b8301602052604081205480158015906109c257505f1914159392505050565b5f610838826119d3565b600780546001600160a01b038381166001600160a01b0319831681179093556040519116919082907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0905f90a35050565b6001600160a01b03811661130157604051632a96f9e160e21b815260040160405180910390fd5b306001600160a01b0316816001600160a01b03166382bfefc86040518163ffffffff1660e01b8152600401602060405180830381865afa158015611347573d5f5f3e3d5ffd5b505050506040513d601f19601f8201168201806040525081019061136b91906127ef565b6001600160a01b03161461139257604051630d742d7960e11b815260040160405180910390fd5b6001600160a01b0382165f9081526006602052604090206113b39082611a1b565b6113d0576040516323f8ed6f60e11b815260040160405180910390fd5b6001600160a01b0382165f9081526006602052604090207f00000000000000000000000000000000000000000000000000000000000000009061141290611509565b11156114315760405163f2bb8d3360e01b815260040160405180910390fd5b604080516001600160a01b038085168252831660208201527f1b7c4ecbed8f766ef52b760f0b12e9fd9f6d5d89a1ed1985291a2e312f78afa5910160405180910390a15f61147e836109eb565b90508015610b2957610b29825f8584611933565b6001600160a01b0381165f908152600a6020526040812054610838565b60606108b57f00000000000000000000000000000000000000000000000000000000000000006008611a88565b60606108b57f00000000000000000000000000000000000000000000000000000000000000006009611a88565b80545f9060a01c63ffffffff16610838565b61154730867f00000000000000000000000000000000000000000000000000000000000000008461199d565b6040516351cff8d960e01b81526001600160a01b0385811660048301527f000000000000000000000000000000000000000000000000000000000000000016906351cff8d9906024015f604051808303815f87803b1580156115a7575f5ffd5b505af11580156115b9573d5f5f3e3d5ffd5b505050505050505050565b6060815463ffffffff60a01b811660a01c6001600160a01b03821691506040519250806001016020028301604052808352801561162c576020830182815260015b8281101561162957858101546001600160a01b031660208202830152600101611605565b50505b5050919050565b5f61083861163f610fc3565b8360405161190160f01b8152600281019290925260228201526042902090565b5f5f5f5f61166f88888888611b31565b92509250925061167f8282611bf9565b50909695505050505050565b6001600160a01b0382165f9081526006602052604090206116ac9082611cb1565b6116c95760405163ec59358360e01b815260040160405180910390fd5b604080516001600160a01b038085168252831660208201527fd580c0b3363ee46dd07e6c2c50e637920eebd2b82481b0ccb43f66d1d9dded79910160405180910390a15f611716836109eb565b90508015610b2957610b2982845f84611933565b5f825f036117395750836109c2565b610bb485858585611d4c565b6001600160a01b03841661176e5760405163e602df0560e01b81525f60048201526024016109a1565b6001600160a01b03831661179757604051634a1406b160e11b81525f60048201526024016109a1565b6001600160a01b038085165f908152600160209081526040808320938716835292905220829055801561093657826001600160a01b0316846001600160a01b03167f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b9258460405161180991815260200190565b60405180910390a350505050565b5f835f036118265750836109c2565b610bb485848685611d4c565b610b29838383611d77565b81545f90829060a01c63ffffffff161161186a57604051634e23d03560e01b815260040160405180910390fd5b6001600160a01b038383600160201b811061188757611887612755565b0154169392505050565b5f6108b5817f000000000000000000000000000000000000000000000000000000000000000061281e565b60606118c7826115c4565b8051909150801561192d576001600160a01b0383555f5b8181101561162c575f1984600160201b015f85848151811061190257611902612755565b6020908102919091018101516001600160a01b031682528101919091526040015f20556001016118de565b50919050565b604051637a1f1aa960e01b808252600482018590526024820184905260448201839052907f0000000000000000000000000000000000000000000000000000000000000000905a5f5f6064845f8c88f161124357826040603f8302041015611243573d5f833e3d82fd5b6119ab848484846001611e78565b61093657604051635274afe760e01b81526001600160a01b03851660048201526024016109a1565b5f60056119e1815460021490565b156119ff576040516306fda65d60e31b815260040160405180910390fd5b50506001600160a01b03165f9081526020819052604090205490565b6001600160a01b0381165f908152600160201b830160205260408120548015801590611a4857505f198114155b15611a56575f915050610838565b611a608484611ee5565b6001600160a01b0384165f908152600160201b86016020526040902055506001905092915050565b606060ff8314611aa257611a9b83611f2c565b9050610838565b818054611aae90612769565b80601f0160208091040260200160405190810160405280929190818152602001828054611ada90612769565b8015611b255780601f10611afc57610100808354040283529160200191611b25565b820191905f5260205f20905b815481529060010190602001808311611b0857829003601f168201915b50505050509050610838565b5f80807f7fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a0841115611b6a57505f91506003905082611bef565b604080515f808252602082018084528a905260ff891692820192909252606081018790526080810186905260019060a0016020604051602081039080840390855afa158015611bbb573d5f5f3e3d5ffd5b5050604051601f1901519150506001600160a01b038116611be657505f925060019150829050611bef565b92505f91508190505b9450945094915050565b5f826003811115611c0c57611c0c6127db565b03611c15575050565b6001826003811115611c2957611c296127db565b03611c475760405163f645eedf60e01b815260040160405180910390fd5b6002826003811115611c5b57611c5b6127db565b03611c7c5760405163fce698f760e01b8152600481018290526024016109a1565b6003826003811115611c9057611c906127db565b0361114d576040516335e2f38360e21b8152600481018290526024016109a1565b6001600160a01b0381165f908152600160201b83016020526040812080545f19909155801580611ce157505f1981145b15611cef575f915050610838565b5f611cf985611f69565b9050836001600160a01b0316816001600160a01b03161461087957611d22855f19840183611fd0565b6001600160a01b0381165f908152600160201b860160205260409020829055506001949350505050565b5f611d578486612837565b905081611d6d57611d688382612862565b610bb4565b610bb48184612026565b611d82838383612059565b6001600160a01b03831615801590611da257506001600160a01b03821615155b8015611db0575060ff5f5c16155b15610b29575f5f5f60037f00000000000000000000000000000000000000000000000000000000000000001015611dec57859250849150611df6565b5083915084905060015b60405163019653a160e11b81526001600160a01b038481166004830152838116602483015282151560448301527f0000000000000000000000000000000000000000000000000000000000000000169063032ca742906064015f604051808303815f87803b158015611e66575f5ffd5b505af1158015610d75573d5f5f3e3d5ffd5b6040516323b872dd60e01b5f8181526001600160a01b038781166004528616602452604485905291602083606481808c5af1925060015f51148316611ed4578383151615611ec8573d5f823e3d81fd5b5f883b113d1516831692505b604052505f60605295945050505050565b5f825463ffffffff60a01b811660a01c805f8114611f1657600160a01b83018655600160ff1b851782870155611f20565b600160a01b851786555b50600101949350505050565b60605f611f3883612215565b6040805160208082528183019092529192505f91906020820181803683375050509182525060208101929092525090565b80545f9063035157cf60e11b9063ffffffff60a082901c16808015611fb05760018114611fb8576001600160a01b0360018303870154169450600160a01b83038655611629565b835f5260045ffd5b5050600160ff1b90935550506001600160a01b031690565b8254634e23d03560e01b9063ffffffff60a082901c166001600160a01b038216818610611fff57835f5260045ffd5b85801561201657600160ff1b861787890155611243565b5091909118909217909355505050565b5f815f0361203857612038601261223c565b81600184038161204a5761204a61284e565b04600101831515029392505050565b60056120648161224d565b61206f848484612276565b5f821180156120905750826001600160a01b0316846001600160a01b031614155b1561220c576001600160a01b0384165f9081526006602052604081206120b5906115c4565b6001600160a01b0385165f908152600660205260408120919250906120d9906115c4565b82518151919250905f5b828110156121b0575f8582815181106120fe576120fe612755565b602002602001015190505f5f90505b838110156121935785818151811061212757612127612755565b60200260200101516001600160a01b0316826001600160a01b03160361218b57612153828c8c8c611933565b5f86828151811061216657612166612755565b60200260200101906001600160a01b031690816001600160a01b031681525050612193565b60010161210d565b8381036121a6576121a6828c5f8c611933565b50506001016120e3565b505f5b81811015612206575f8482815181106121ce576121ce612755565b602002602001015190505f6001600160a01b0316816001600160a01b0316146121fd576121fd815f8b8b611933565b506001016121b3565b50505050505b60018155610936565b5f60ff8216601f81111561083857604051632cd44ac360e21b815260040160405180910390fd5b634e487b715f52806020526024601cfd5b805460011901612270576040516306fda65d60e31b815260040160405180910390fd5b60029055565b6001600160a01b0383166122a0578060025f8282546122959190612881565b909155506122fd9050565b6001600160a01b0383165f90815260208190526040902054818110156122df5783818360405163391434e360e21b81526004016109a19392919061279b565b6001600160a01b0384165f9081526020819052604090209082900390555b6001600160a01b03821661231957600280548290039055612337565b6001600160a01b0382165f9081526020819052604090208054820190555b816001600160a01b0316836001600160a01b03167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef8360405161237c91815260200190565b60405180910390a3505050565b6001600160a01b0381168114610a9d575f5ffd5b5f5f604083850312156123ae575f5ffd5b82356123b981612389565b915060208301356123c981612389565b809150509250929050565b5f81518084528060208401602086015e5f602082860101526020601f19601f83011685010191505092915050565b602081525f6108a560208301846123d4565b5f60208284031215612424575f5ffd5b5035919050565b5f5f6040838503121561243c575f5ffd5b823561244781612389565b946020939093013593505050565b5f5f5f60608486031215612467575f5ffd5b833561247281612389565b9250602084013561248281612389565b929592945050506040919091013590565b5f602082840312156124a3575f5ffd5b81356108a581612389565b5f5f5f5f608085870312156124c1575f5ffd5b84356124cc81612389565b935060208501356124dc81612389565b93969395505050506040820135916060013590565b5f5f60408385031215612502575f5ffd5b8235915060208301356123c981612389565b60ff60f81b8816815260e060208201525f61253260e08301896123d4565b828103604084015261254481896123d4565b606084018890526001600160a01b038716608085015260a0840186905283810360c0850152845180825260208087019350909101905f5b8181101561259957835183526020938401939092019160010161257b565b50909b9a5050505050505050505050565b5f5f5f606084860312156125bc575f5ffd5b8335925060208401356125ce81612389565b915060408401356125de81612389565b809150509250925092565b602080825282518282018190525f918401906040840190835b818110156126295783516001600160a01b0316835260209384019390920191600101612602565b509095945050505050565b5f5f5f5f5f5f5f60e0888a03121561264a575f5ffd5b873561265581612389565b9650602088013561266581612389565b95506040880135945060608801359350608088013560ff81168114612688575f5ffd5b9699959850939692959460a0840135945060c09093013592915050565b634e487b7160e01b5f52604160045260245ffd5b80516001600160801b03811681146126cf575f5ffd5b919050565b5f60c082840312156126e4575f5ffd5b82601f8301126126f2575f5ffd5b60405160c0810181811067ffffffffffffffff82111715612715576127156126a5565b6040528060c0840185811115612729575f5ffd5b845b8181101561274a5761273c816126b9565b83526020928301920161272b565b509195945050505050565b634e487b7160e01b5f52603260045260245ffd5b600181811c9082168061277d57607f821691505b60208210810361192d57634e487b7160e01b5f52602260045260245ffd5b6001600160a01b039390931683526020830191909152604082015260600190565b5f602082840312156127cc575f5ffd5b815180151581146108a5575f5ffd5b634e487b7160e01b5f52602160045260245ffd5b5f602082840312156127ff575f5ffd5b81516108a581612389565b634e487b7160e01b5f52601160045260245ffd5b60ff81811683821601908111156108385761083861280a565b80820281158282048414176108385761083861280a565b634e487b7160e01b5f52601260045260245ffd5b5f8261287c57634e487b7160e01b5f52601260045260245ffd5b500490565b808201808211156108385761083861280a56fea2646970667358221220e196ab5d6fa6f1d7568734fbd3e4da7f300f895c69bd6ec8e8a55919f17f403f64736f6c634300081c00336080604052348015600e575f5ffd5b5060ce80601a5f395ff3fe6080604052348015600e575f5ffd5b50600436106026575f3560e01c8063032ca74214602a575b5f5ffd5b603a60353660046056565b505050565b005b80356001600160a01b03811681146051575f5ffd5b919050565b5f5f5f606084860312156067575f5ffd5b606e84603c565b9250607a60208501603c565b915060408401358015158114608d575f5ffd5b80915050925092509256fea2646970667358221220ab6d9146c4873b54afa6a728fd3b08d78e467333c1730c5057fa6c60b6c56d2b64736f6c634300081c00336080604052348015600e575f5ffd5b50603e80601a5f395ff3fe60806040525f5ffdfea2646970667358221220903377b67e81caf39680db551e0c55a1cbd427a57e0c6f89fa2e3675f390e05564736f6c634300081c00336080604052348015600e575f5ffd5b506105bc8061001c5f395ff3fe608060405234801561000f575f5ffd5b5060043610610029575f3560e01c806395d89b411461002d575b5f5ffd5b61003561004b565b604051610042919061006e565b60405180910390f35b60606040518061052001604052806104e381526020016100a46104e39139905090565b602081525f82518060208401528060208501604085015e5f604082850101526040601f19601f8301168401019150509291505056fe566a737566626575716e767a6b656977616c7270687a746f7164636c6d6b79726978626f686d7266656e7971767767647a696c7966626b6e68616a67707478646d6f77736375626c69656b6d6672746e6a766873777a6f71676470786c726b63616e6d6a7679666965686c6e6f6274777a736b727061786d6364666775766f7168696a796e777a656c746270646d757371766b67687872616f79696a6263666c6e747677707a7365756b646d6f72687a61796a69636c666e626b76677078717274776d6f736465756e68616a6c7a6662766b72706869677178776379746d6e6f736a6c64616275666b76687870796574697a726d716e63667775676b7064616f62687a716a726c79736d69657674776e66786762756f636c7a6a7970686b61727165696d63646e74767378676f7972666a7577626c687a6b70646d76787471736977656e6d636e666462676c7a75766b72686f6a6174706579776978737a716f6675626c64676b6e6a6d7279636874767a71616f77666a72627068646d786c7a69656b7376746e79676371756f6d776272646a6b7066686c65697a6176717873676f6e6379746d75717270776a687a64666265696b766e6c7973786774636f767277616d7562706c6e7a7165696664686b6a6d77746379766f78726e6771706273647568657a6c6b6a79697378726361626f7767666870786574766e6471756d7a6c697664726b626e656a7377786779717a666f6163766a6c7072686b6d747762737a767579716f636e646c69656d6674766e727078687a73616b7767756264717965696c637672746d6e666a73786f6865707167776c7a646e626b61756d7679726874696a706c73637866676f777a6e626574716b76797564726d6878706c7377636e696f6a61677464717a66627876796572756f6b6d686e6c70647a6d6369676a7166626f7968786b7473647775656c616e6f706967726a637679746268666d6b6c737565716470777a6e7678637261746f696279666a6b6d78677a646e68736c6f70716575727677716d746a636b6e796668627a78646769706c61746e7379756f7772686d656b6c6a7178667a6370766267646e746f617772756d7965716873706c6b7a766a63667872676974646f716e626176796b77687a6c70786d73756563726a6f69677471666c77646e6d62796b787669757a707263616a656f736c746662676864776d746b71787563796e697a76617072656c6b667173626e64746d6f6a79786875696376776c6e7a6f786770726d6a61786473796b6c666562777a6e686f636776757269706d71656a79667a786164746b77636273676f6c68726e6d76796a706c75667762656b746178726e6a687a767179646d676f77747365696b666c62757a716e796a6870777264616d7663676f6678756c6b6274707a6e69767173796577726d6f6375686478616c706a6b7a746667696576626e797173776468636f61706c6d747275696b786a7a7662667771796568676e6373646c726f706b6d7578697a76747966656e626477686a72716b6c6d73637470786f756779696166687a6e766b726a6f6c7165626475777473707a79786d676e76686b62777266636c6a6f74797875707a696471736d65676e686c62616669766b72777074646d6f75786a79637a68716c6e6b6267656976726a61747079776473787a6d66636f6875716e65706c7476796b787369776d7264717a6a6f676261686663756ca264697066735822122037726cc5f36db672578e214025313ef02554f32c2ec538d26e4f4f4f95ffc1aa64736f6c634300081c003360c0604052600360809081526241424360e81b60a0525f9061002190826100f6565b5034801561002d575f5ffd5b506040516103e63803806103e683398101604081905261004c916101b0565b5f61005782826100f6565b5050610260565b634e487b7160e01b5f52604160045260245ffd5b600181811c9082168061008657607f821691505b6020821081036100a457634e487b7160e01b5f52602260045260245ffd5b50919050565b601f8211156100f157805f5260205f20601f840160051c810160208510156100cf5750805b601f840160051c820191505b818110156100ee575f81556001016100db565b50505b505050565b81516001600160401b0381111561010f5761010f61005e565b6101238161011d8454610072565b846100aa565b6020601f821160018114610155575f831561013e5750848201515b5f19600385901b1c1916600184901b1784556100ee565b5f84815260208120601f198516915b828110156101845787850151825560209485019460019092019101610164565b50848210156101a157868401515f19600387901b60f8161c191681555b50505050600190811b01905550565b5f602082840312156101c0575f5ffd5b81516001600160401b038111156101d5575f5ffd5b8201601f810184136101e5575f5ffd5b80516001600160401b038111156101fe576101fe61005e565b604051601f8201601f19908116603f011681016001600160401b038111828210171561022c5761022c61005e565b604052818152828201602001861015610243575f5ffd5b8160208401602083015e5f91810160200191909152949350505050565b6101798061026d5f395ff3fe608060405234801561000f575f5ffd5b5060043610610029575f3560e01c806395d89b411461002d575b5f5ffd5b61003561004b565b60405161004291906100d6565b60405180910390f35b5f80546100579061010b565b80601f01602080910402602001604051908101604052809291908181526020018280546100839061010b565b80156100ce5780601f106100a5576101008083540402835291602001916100ce565b820191905f5260205f20905b8154815290600101906020018083116100b157829003601f168201915b505050505081565b602081525f82518060208401528060208501604085015e5f604082850101526040601f19601f83011684010191505092915050565b600181811c9082168061011f57607f821691505b60208210810361013d57634e487b7160e01b5f52602260045260245ffd5b5091905056fea2646970667358221220e8bed963fb3d467935f50a8eea7809995df8f81d86afcbf6616af79688e4d59a64736f6c634300081c00336080604052348015600e575f5ffd5b5060405160c938038060c9833981016040819052602991602f565b5f556045565b5f60208284031215603e575f5ffd5b5051919050565b60798060505f395ff3fe6080604052348015600e575f5ffd5b50600436106026575f3560e01c806395d89b4114602a575b5f5ffd5b60315f5481565b60405190815260200160405180910390f3fea2646970667358221220106e3dfdfd0d620e68632627ee7c58348a5fbc1a9c80f1b417220e7755f11abd64736f6c634300081c003360c0604052600360809081526241424360e81b60a0525f9061002190826100f6565b5034801561002d575f5ffd5b506040516103e63803806103e683398101604081905261004c916101b0565b5f61005782826100f6565b5050610260565b634e487b7160e01b5f52604160045260245ffd5b600181811c9082168061008657607f821691505b6020821081036100a457634e487b7160e01b5f52602260045260245ffd5b50919050565b601f8211156100f157805f5260205f20601f840160051c810160208510156100cf5750805b601f840160051c820191505b818110156100ee575f81556001016100db565b50505b505050565b81516001600160401b0381111561010f5761010f61005e565b6101238161011d8454610072565b846100aa565b6020601f821160018114610155575f831561013e5750848201515b5f19600385901b1c1916600184901b1784556100ee565b5f84815260208120601f198516915b828110156101845787850151825560209485019460019092019101610164565b50848210156101a157868401515f19600387901b60f8161c191681555b50505050600190811b01905550565b5f602082840312156101c0575f5ffd5b81516001600160401b038111156101d5575f5ffd5b8201601f810184136101e5575f5ffd5b80516001600160401b038111156101fe576101fe61005e565b604051601f8201601f19908116603f011681016001600160401b038111828210171561022c5761022c61005e565b604052818152828201602001861015610243575f5ffd5b8160208401602083015e5f91810160200191909152949350505050565b6101798061026d5f395ff3fe608060405234801561000f575f5ffd5b5060043610610029575f3560e01c8063f76f8d781461002d575b5f5ffd5b61003561004b565b60405161004291906100d6565b60405180910390f35b5f80546100579061010b565b80601f01602080910402602001604051908101604052809291908181526020018280546100839061010b565b80156100ce5780601f106100a5576101008083540402835291602001916100ce565b820191905f5260205f20905b8154815290600101906020018083116100b157829003601f168201915b505050505081565b602081525f82518060208401528060208501604085015e5f604082850101526040601f19601f83011684010191505092915050565b600181811c9082168061011f57607f821691505b60208210810361013d57634e487b7160e01b5f52602260045260245ffd5b5091905056fea2646970667358221220290d114be7af333ac16ac0eee93d80c7e4b6b931fa9b23a113692ef755eb242864736f6c634300081c003353686f756c642072657475726e207468652061646472657373206f66206d6f636b20636f6e7472616374303132333435363738393031323334353637383930313233343536373839303132496e636f72726563742073796d626f6c2072657475726e656400000000000000a264697066735822122058c0dd24bc248b04d2db9ec0c5e31204cc1177f8fb1c8710e28572103e51c80364736f6c634300081c0033", "sourceMap": "718:6933:167:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1610:244;;;:::i;:::-;;2907:134:100;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;5689:400:167;;;:::i;3823:151:100:-;;;:::i;:::-;;;;;;;:::i;3684:133::-;;;:::i;3385:141::-;;;:::i;3193:186::-;;;:::i;:::-;;;;;;;:::i;1178:426:167:-;;;:::i;2590:419::-;;;:::i;3047:140:100:-;;;:::i;:::-;;;;;;;:::i;3532:146::-;;;:::i;:::-;;;;;;;:::i;2754:147::-;;;:::i;2459:141::-;;;:::i;1243:204:96:-;;;:::i;:::-;;;6174:14:195;;6167:22;6149:41;;6137:2;6122:18;1243:204:96;6009:187:195;5467:216:167;;;:::i;4923:538::-;;;:::i;6682:319::-;;;;;;:::i;:::-;;:::i;:::-;;;7653:25:195;;;7641:2;7626:18;6682:319:167;7507:177:195;3015:440:167;;;:::i;1860:358::-;;;:::i;2606:142:100:-;;;:::i;2224:360:167:-;;;:::i;6095:581::-;;;;;;:::i;:::-;;:::i;1016:26:107:-;;;;;;;;;1610:244:167;1681:12;;1667:27;;;;;:::i;:::-;7653:25:195;;;7641:2;7626:18;1667:27:167;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1659:5:167;:35;;-1:-1:-1;;;;;;1659:35:167;-1:-1:-1;;;;;1659:35:167;;;;;;;;;-1:-1:-1;;1733:37:167;;:21;:37::i;:::-;1704:66;;1780:67;1789:12;1803:5;;;;;;;;;-1:-1:-1;;;;;1803:5:167;-1:-1:-1;;;;;1803:12:167;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1803:14:167;;;;;;;;;;;;:::i;:::-;1780:67;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;1780:67:167;;;:8;:67::i;:::-;1649:205;1610:244::o;2907:134:100:-;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2999:35:100;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;5689:400:167:-;5769:27;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;5749:17:167;:47;;-1:-1:-1;;;;;;5749:47:167;-1:-1:-1;;;;;5749:47:167;;;;;;;;;-1:-1:-1;5888:35:167;5749:47;5888:21;:35::i;:::-;5865:58;;5987:95;5996:6;6004:31;6022:12;6004:17;:31::i;:::-;5987:95;;;;;;;;;;;;;;;;;:8;:95::i;:::-;5739:350;;5689:400::o;3823:151:100:-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3926:41:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;3684:133::-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3775:35:100;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3480:39:100;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;3193:186::-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3317:55:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1178:426:167;1252:26;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1230:19:167;:48;;-1:-1:-1;;;;;;1230:48:167;;-1:-1:-1;;;;;1230:48:167;;;;;;;;;;;;;-1:-1:-1;;1317:51:167;;1347:19;;;;;1317:21;:51::i;:::-;1288:80;;1443:28;1474:46;1484:19;;;;;;;;;-1:-1:-1;;;;;1484:19:167;-1:-1:-1;;;;;1484:26:167;;:28;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1484:28:167;;;;;;;;;;;;:::i;:::-;1514:1;1517:2;1474:9;:46::i;:::-;1443:77;;1530:67;1539:12;1553:14;1530:67;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;1530:67:167;;;:8;:67::i;2590:419::-;2667:33;;;;;:::i;:::-;9150:2:195;9132:21;;;9189:1;9169:18;;;9162:29;9223:2;9208:18;2667:33:167;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2643:21:167;:57;;-1:-1:-1;;;;;;2643:57:167;-1:-1:-1;;;;;2643:57:167;;;;;;;;;-1:-1:-1;2802:35:167;2643:57;2802:21;:35::i;3047:140:100:-;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3532:146;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2754:147;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2459:141;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1243:204:96;1302:7;;1282:4;;1302:7;;1298:143;;;-1:-1:-1;1332:7:96;;;;;1243:204::o;1298:143::-;1377:39;;-1:-1:-1;;;1377:39:96;;:7;:39;;;9411:51:195;;;-1:-1:-1;;;9478:18:195;;;9471:34;1428:1:96;;1377:7;;9384:18:195;;1377:39:96;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;1370:60;;1243:204;:::o;5467:216:167:-;5532:20;5555:33;5585:1;5555:21;:33::i;:::-;5532:56;;5598:78;5607:6;5615:29;5641:1;5615:17;:29::i;:::-;5598:78;;;;;;;;;;;;;;;;;:8;:78::i;4923:538::-;4978:36;:74;;;;;;;;;;;;;;;;;;;5116:22;5086:53;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;5062:21:167;:77;;-1:-1:-1;;;;;;5062:77:167;-1:-1:-1;;;;;5062:77:167;;;;;;;;;-1:-1:-1;;5178:53:167;;:21;:53::i;:::-;5149:82;;5306:28;5337:40;5347:22;5371:1;5374:2;5337:9;:40::i;:::-;5306:71;;5387:67;5396:12;5410:14;5387:67;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;5387:67:167;;;:8;:67::i;:::-;4968:493;;;4923:538::o;6682:319::-;6852:26;;6764:14;;6831:6;;6852:31;;6848:72;;-1:-1:-1;6906:3:167;;6682:319;-1:-1:-1;;6682:319:167:o;6848:72::-;-1:-1:-1;;6981:2:167;6969:15;6963:22;;6682:319::o;3015:440::-;3125:19;;;;;;;;;;;;;:15;:19::i;:::-;3094:51;;;;;:::i;:::-;7653:25:195;;;7641:2;7626:18;3094:51:167;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;3069:22:167;:76;;-1:-1:-1;;;;;;3069:76:167;-1:-1:-1;;;;;3069:76:167;;;;;;;;;-1:-1:-1;3248:35:167;3069:76;3248:21;:35::i;1860:358::-;1913:37;;;;;;;;;;;-1:-1:-1;;;1913:37:167;;;;1988:49;;1913:37;;1988:49;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1960:25:167;:77;;-1:-1:-1;;;;;;1960:77:167;-1:-1:-1;;;;;1960:77:167;;;;;;;;;-1:-1:-1;;2076:57:167;;:21;:57::i;:::-;2047:86;;2144:67;2153:12;2167:14;2144:67;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;2144:67:167;;;:8;:67::i;2606:142:100:-;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2702:39:100;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;2224:360:167:-;2273:36;;;;;;;;;;;;-1:-1:-1;;;2273:36:167;;;;2375:31;2273:36;2375:15;:31::i;:::-;2344:63;;;;;:::i;:::-;7653:25:195;;;7641:2;7626:18;2344:63:167;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2319:22:167;:88;;-1:-1:-1;;;;;;2319:88:167;-1:-1:-1;;;;;2319:88:167;;;;;;;;;-1:-1:-1;;2446:54:167;;:21;:54::i;6095:581::-;6194:18;;6222:26;;-1:-1:-1;;;6222:26:167;;6232:15;;;6222:26;;;6149:41:195;6222:9:167;;;;6122:18:195;;6222:26:167;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6267:10;6262:2;:15;6258:36;;;-1:-1:-1;6292:2:167;6258:36;6359:4;6329:35;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;6305:21:167;:59;;-1:-1:-1;;;;;;6305:59:167;-1:-1:-1;;;;;6305:59:167;;;;;;;;;-1:-1:-1;;6403:53:167;;:21;:53::i;:::-;6374:82;;6531:28;6562:30;6572:4;6578:1;6581:10;6562:9;:30::i;:::-;6531:61;;6602:67;6611:12;6625:14;6602:67;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;6602:67:167;;;:8;:67::i;:::-;6163:513;;;6095:581;:::o;345:1700:27:-;626:35;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;626:35:27;-1:-1:-1;;;626:35:27;;;416:13;;442:12;;;;477:194;;536:5;;555:6;;416:13;;477:45;:194::i;:::-;441:230;;;;687:7;682:271;;893:35;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;893:35:27;-1:-1:-1;;;893:35:27;;;728:214;;791:5;;814:6;;838:2;;728:45;:214::i;:::-;710:232;;-1:-1:-1;710:232:27;-1:-1:-1;682:271:27;967:7;:28;;;;;978:4;:11;993:2;978:17;967:28;963:597;;;1012:14;1028:11;1054:4;1043:36;;;;;;;;;;;;:::i;:::-;1011:68;;;;1097:6;1107:4;1097:14;:25;;;;;1121:1;1115:3;:7;1097:25;1093:457;;;1231:12;1235:3;1240:2;1231:3;:12::i;:::-;1225:18;;1261:23;1297:3;1287:14;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1287:14:27;-1:-1:-1;1470:2:27;1460:13;;;;1454:20;1449:2;1433:19;;1426:49;-1:-1:-1;1261:40:27;;345:1700;-1:-1:-1;;;;;345:1700:27:o;1093:457::-;997:563;;963:597;1574:7;:28;;;;;1585:4;:11;1600:2;1585:17;1574:28;1570:439;;;1618:11;1647:106;1660:4;:11;1654:3;:17;:38;;;;;1688:4;1675:17;;:4;1680:3;1675:9;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;;1675:9:27;:17;;1654:38;:59;;;;;1709:4;1696:17;;:4;1701:3;1696:9;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;;1696:9:27;:17;;1654:59;1647:106;;;1733:5;;;;:::i;:::-;;;;1647:106;;;1771:7;;1767:232;;1798:19;1830:3;1820:14;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1820:14:27;-1:-1:-1;1798:36:27;-1:-1:-1;1857:9:27;1852:94;1876:3;1872:1;:7;1852:94;;;1920:4;1925:1;1920:7;;;;;;;;:::i;:::-;;;;;;;;;1908:6;1915:1;1908:9;;;;;;;;:::i;:::-;;;;:19;-1:-1:-1;;;;;1908:19:27;;;;;;;;-1:-1:-1;1881:3:27;;1852:94;;;-1:-1:-1;1977:6:27;345:1700;-1:-1:-1;;;;;345:1700:27:o;1767:232::-;1604:405;1570:439;2026:12;2032:5;2026;:12::i;:::-;2019:19;345:1700;-1:-1:-1;;;;345:1700:27:o;4348:146:96:-;4458:29;;-1:-1:-1;;;4458:29:96;;:11;;;;:29;;4470:4;;4476:5;;4483:3;;4458:29;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4348:146;;;:::o;2051:141:27:-;2159:25;;2120:13;11378:15:195;;;-1:-1:-1;;11374:53:195;2159:25:27;;;11362:66:195;2120:13:27;2152:33;;11444:12:195;;2159:25:27;;;;;;;;;;;;2152:6;:33::i;:::-;2145:40;2051:141;-1:-1:-1;;2051:141:27:o;7115:534:167:-;7213:13;7268:3;7290:21;;;7282:73;;;;-1:-1:-1;;;7282:73:167;;11669:2:195;7282:73:167;;;11651:21:195;11708:2;11688:18;;;11681:30;11747:34;11727:18;;;11720:62;-1:-1:-1;;;11798:18:195;;;11791:37;11845:19;;7282:73:167;;;;;;;;;7385:8;:15;7373:8;:27;;7365:63;;;;-1:-1:-1;;;7365:63:167;;12077:2:195;7365:63:167;;;12059:21:195;12116:2;12096:18;;;12089:30;12155:25;12135:18;;;12128:53;12198:18;;7365:63:167;11875:347:195;7365:63:167;7439:19;7471:21;7482:10;7471:8;:21;:::i;:::-;7461:32;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;7461:32:167;-1:-1:-1;7439:54:167;-1:-1:-1;7520:10:167;7503:109;7536:8;7532:1;:12;7503:109;;;7590:8;7599:1;7590:11;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;;7590:11:167;7565:6;7572:14;7576:10;7572:1;:14;:::i;:::-;7565:22;;;;;;;;:::i;:::-;;;;:36;-1:-1:-1;;;;;7565:36:167;;;;;;;;-1:-1:-1;7546:3:167;;7503:109;;3411:1263:48;3579:4;3585:12;3645:15;3670:13;3693:24;3730:8;3720:19;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;3720:19:48;;3693:46;;4208:1;4179;4142:9;4136:16;4104:4;4093:9;4089:20;4051:7;4022:4;3994:239;3982:251;;4300:16;4289:27;;4344:8;4335:7;4332:21;4329:76;;;4383:8;4372:19;;4329:76;4490:7;4477:11;4470:28;4610:7;4607:1;4600:4;4587:11;4583:22;4568:50;4645:8;;;;-1:-1:-1;3411:1263:48;-1:-1:-1;;;;;;3411:1263:48:o;2848:103:27:-;2905:7;2935:1;2931;:5;:13;;2943:1;2931:13;;;2939:1;2931:13;2924:20;2848:103;-1:-1:-1;;;2848:103:27:o;2198:644::-;2269:13;2294:16;2327:4;:11;2341:1;2327:15;;;;:::i;:::-;2323:19;;:1;:19;:::i;:::-;2313:30;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2313:30:27;;2294:49;;-1:-1:-1;;;2353:3:27;2357:1;2353:6;;;;;;;;:::i;:::-;;;;:12;-1:-1:-1;;;;;2353:12:27;;;;;;;;;-1:-1:-1;;;2375:3:27;2379:1;2375:6;;;;;;;;:::i;:::-;;;;:12;-1:-1:-1;;;;;2375:12:27;;;;;;;;-1:-1:-1;2409:1:27;2397:9;2420:387;2444:4;:11;2440:1;:15;2420:387;;;2476:9;2506:1;2494:4;2499:1;2494:7;;;;;;;;:::i;:::-;;;;;;;;;2488:14;;:19;;;;2476:31;;;;2521:9;2539:4;2544:1;2539:7;;;;;;;;:::i;:::-;;;;;;;;2550:4;2533:21;;-1:-1:-1;2662:6:27;2666:2;2662:1;:6;:::i;:::-;2661:13;;2672:2;2661:13;:::i;:::-;2652:6;:1;2656:2;2652:6;:::i;:::-;:22;;;;:::i;:::-;2639:37;;2628:3;2632;;;;:::i;:::-;;;2628:8;;;;;;;;:::i;:::-;;;;:48;-1:-1:-1;;;;;2628:48:27;;;;;;;;-1:-1:-1;2724:6:27;2728:2;2724:1;:6;:::i;:::-;2723:13;;2734:2;2723:13;:::i;:::-;2714:6;:1;2718:2;2714:6;:::i;:::-;:22;;;;:::i;:::-;2701:37;;2690:3;2694;;;;:::i;:::-;;;2690:8;;;;;;;;:::i;:::-;;;;:48;-1:-1:-1;;;;;2690:48:27;;;;;;;;-1:-1:-1;;;2457:3:27;;2420:387;;;-1:-1:-1;2831:3:27;;2198:644;-1:-1:-1;;;2198:644:27:o;-1:-1:-1:-;;;;;;;;:::o;:::-;;;;;;;;:::o;:::-;;;;;;;;:::o;:::-;;;;;;;;:::o;:::-;;;;;;;;:::o;:::-;;;;;;;;:::o;14:637:195:-;204:2;216:21;;;286:13;;189:18;;;308:22;;;156:4;;387:15;;;361:2;346:18;;;156:4;430:195;444:6;441:1;438:13;430:195;;;509:13;;-1:-1:-1;;;;;505:39:195;493:52;;574:2;600:15;;;;565:12;;;;541:1;459:9;430:195;;;-1:-1:-1;642:3:195;;14:637;-1:-1:-1;;;;;14:637:195:o;656:289::-;698:3;736:5;730:12;763:6;758:3;751:19;819:6;812:4;805:5;801:16;794:4;789:3;785:14;779:47;871:1;864:4;855:6;850:3;846:16;842:27;835:38;934:4;927:2;923:7;918:2;910:6;906:15;902:29;897:3;893:39;889:50;882:57;;;656:289;;;;:::o;950:1628::-;1156:4;1204:2;1193:9;1189:18;1234:2;1223:9;1216:21;1257:6;1292;1286:13;1323:6;1315;1308:22;1361:2;1350:9;1346:18;1339:25;;1423:2;1413:6;1410:1;1406:14;1395:9;1391:30;1387:39;1373:53;;1461:2;1453:6;1449:15;1482:1;1492:1057;1506:6;1503:1;1500:13;1492:1057;;;-1:-1:-1;;1571:22:195;;;1567:36;1555:49;;1627:13;;1714:9;;-1:-1:-1;;;;;1710:35:195;1695:51;;1793:2;1785:11;;;1779:18;1679:2;1817:15;;;1810:27;;;1898:19;;1667:15;;;1930:24;;;2085:21;;;1988:2;2038:1;2034:16;;;2022:29;;2018:38;;;1976:15;;;;-1:-1:-1;2144:296:195;2160:8;2155:3;2152:17;2144:296;;;2266:2;2262:7;2253:6;2245;2241:19;2237:33;2230:5;2223:48;2298:42;2333:6;2322:8;2316:15;2298:42;:::i;:::-;2383:2;2369:17;;;;2288:52;;-1:-1:-1;2412:14:195;;;;;2188:1;2179:11;2144:296;;;-1:-1:-1;2463:6:195;;-1:-1:-1;;;2504:2:195;2527:12;;;;2492:15;;;;;-1:-1:-1;1528:1:195;1521:9;1492:1057;;;-1:-1:-1;2566:6:195;;950:1628;-1:-1:-1;;;;;;950:1628:195:o;2583:446::-;2635:3;2673:5;2667:12;2700:6;2695:3;2688:19;2732:4;2727:3;2723:14;2716:21;;2771:4;2764:5;2760:16;2794:1;2804:200;2818:6;2815:1;2812:13;2804:200;;;2883:13;;-1:-1:-1;;;;;;2879:40:195;2867:53;;2949:4;2940:14;;;;2977:17;;;;2840:1;2833:9;2804:200;;;-1:-1:-1;3020:3:195;;2583:446;-1:-1:-1;;;;2583:446:195:o;3034:1145::-;3254:4;3302:2;3291:9;3287:18;3332:2;3321:9;3314:21;3355:6;3390;3384:13;3421:6;3413;3406:22;3459:2;3448:9;3444:18;3437:25;;3521:2;3511:6;3508:1;3504:14;3493:9;3489:30;3485:39;3471:53;;3559:2;3551:6;3547:15;3580:1;3590:560;3604:6;3601:1;3598:13;3590:560;;;3697:2;3693:7;3681:9;3673:6;3669:22;3665:36;3660:3;3653:49;3731:6;3725:13;3777:2;3771:9;3808:2;3800:6;3793:18;3838:48;3882:2;3874:6;3870:15;3856:12;3838:48;:::i;:::-;3824:62;;3935:2;3931;3927:11;3921:18;3899:40;;3988:6;3980;3976:19;3971:2;3963:6;3959:15;3952:44;4019:51;4063:6;4047:14;4019:51;:::i;:::-;4009:61;-1:-1:-1;;;4105:2:195;4128:12;;;;4093:15;;;;;3626:1;3619:9;3590:560;;4184:782;4346:4;4394:2;4383:9;4379:18;4424:2;4413:9;4406:21;4447:6;4482;4476:13;4513:6;4505;4498:22;4551:2;4540:9;4536:18;4529:25;;4613:2;4603:6;4600:1;4596:14;4585:9;4581:30;4577:39;4563:53;;4651:2;4643:6;4639:15;4672:1;4682:255;4696:6;4693:1;4690:13;4682:255;;;4789:2;4785:7;4773:9;4765:6;4761:22;4757:36;4752:3;4745:49;4817:40;4850:6;4841;4835:13;4817:40;:::i;:::-;4807:50;-1:-1:-1;4892:2:195;4915:12;;;;4880:15;;;;;4718:1;4711:9;4682:255;;4971:1033;5175:4;5223:2;5212:9;5208:18;5253:2;5242:9;5235:21;5276:6;5311;5305:13;5342:6;5334;5327:22;5380:2;5369:9;5365:18;5358:25;;5442:2;5432:6;5429:1;5425:14;5414:9;5410:30;5406:39;5392:53;;5480:2;5472:6;5468:15;5501:1;5511:464;5525:6;5522:1;5519:13;5511:464;;;5590:22;;;-1:-1:-1;;5586:36:195;5574:49;;5646:13;;5691:9;;-1:-1:-1;;;;;5687:35:195;5672:51;;5770:2;5762:11;;;5756:18;5811:2;5794:15;;;5787:27;;;5756:18;5837:58;;5879:15;;5756:18;5837:58;:::i;:::-;5827:68;-1:-1:-1;;5930:2:195;5953:12;;;;5918:15;;;;;5547:1;5540:9;5511:464;;6201:127;6262:10;6257:3;6253:20;6250:1;6243:31;6293:4;6290:1;6283:15;6317:4;6314:1;6307:15;6333:275;6404:2;6398:9;6469:2;6450:13;;-1:-1:-1;;6446:27:195;6434:40;;6504:18;6489:34;;6525:22;;;6486:62;6483:88;;;6551:18;;:::i;:::-;6587:2;6580:22;6333:275;;-1:-1:-1;6333:275:195:o;6613:187::-;6662:4;6695:18;6687:6;6684:30;6681:56;;;6717:18;;:::i;:::-;-1:-1:-1;6783:2:195;6762:15;-1:-1:-1;;6758:29:195;6789:4;6754:40;;6613:187::o;6805:697::-;6874:6;6927:2;6915:9;6906:7;6902:23;6898:32;6895:52;;;6943:1;6940;6933:12;6895:52;6983:9;6970:23;7016:18;7008:6;7005:30;7002:50;;;7048:1;7045;7038:12;7002:50;7071:22;;7124:4;7116:13;;7112:27;-1:-1:-1;7102:55:195;;7153:1;7150;7143:12;7102:55;7193:2;7180:16;7218:53;7234:36;7263:6;7234:36;:::i;:::-;7218:53;:::i;:::-;7294:6;7287:5;7280:21;7342:7;7337:2;7328:6;7324:2;7320:15;7316:24;7313:37;7310:57;;;7363:1;7360;7353:12;7310:57;7418:6;7413:2;7409;7405:11;7400:2;7393:5;7389:14;7376:49;7470:1;7445:18;;;7465:2;7441:27;7434:38;;;;7449:5;6805:697;-1:-1:-1;;;;6805:697:195:o;7871:687::-;7951:6;8004:2;7992:9;7983:7;7979:23;7975:32;7972:52;;;8020:1;8017;8010:12;7972:52;8053:9;8047:16;8086:18;8078:6;8075:30;8072:50;;;8118:1;8115;8108:12;8072:50;8141:22;;8194:4;8186:13;;8182:27;-1:-1:-1;8172:55:195;;8223:1;8220;8213:12;8172:55;8256:2;8250:9;8281:53;8297:36;8326:6;8297:36;:::i;8281:53::-;8357:6;8350:5;8343:21;8405:7;8400:2;8391:6;8387:2;8383:15;8379:24;8376:37;8373:57;;;8426:1;8423;8416:12;8373:57;8474:6;8469:2;8465;8461:11;8456:2;8449:5;8445:14;8439:42;8526:1;8501:18;;;8521:2;8497:27;8490:38;;;;8505:5;7871:687;-1:-1:-1;;;;7871:687:195:o;8563:380::-;8642:1;8638:12;;;;8685;;;8706:61;;8760:4;8752:6;8748:17;8738:27;;8706:61;8813:2;8805:6;8802:14;8782:18;8779:38;8776:161;;8859:10;8854:3;8850:20;8847:1;8840:31;8894:4;8891:1;8884:15;8922:4;8919:1;8912:15;8776:161;;8563:380;;;:::o;9516:184::-;9586:6;9639:2;9627:9;9618:7;9614:23;9610:32;9607:52;;;9655:1;9652;9645:12;9607:52;-1:-1:-1;9678:16:195;;9516:184;-1:-1:-1;9516:184:195:o;9705:220::-;9854:2;9843:9;9836:21;9817:4;9874:45;9915:2;9904:9;9900:18;9892:6;9874:45;:::i;9930:343::-;10009:6;10017;10070:2;10058:9;10049:7;10045:23;10041:32;10038:52;;;10086:1;10083;10076:12;10038:52;-1:-1:-1;;10131:16:195;;10237:2;10222:18;;;10216:25;10131:16;;10216:25;;-1:-1:-1;9930:343:195:o;10278:127::-;10339:10;10334:3;10330:20;10327:1;10320:31;10370:4;10367:1;10360:15;10394:4;10391:1;10384:15;10410:127;10471:10;10466:3;10462:20;10459:1;10452:31;10502:4;10499:1;10492:15;10526:4;10523:1;10516:15;10542:135;10581:3;10602:17;;;10599:43;;10622:18;;:::i;:::-;-1:-1:-1;10669:1:195;10658:13;;10542:135::o;10682:546::-;10927:2;10916:9;10909:21;10890:4;10953:45;10994:2;10983:9;10979:18;10971:6;10953:45;:::i;:::-;11046:9;11038:6;11034:22;11029:2;11018:9;11014:18;11007:50;11080:33;11106:6;11098;11080:33;:::i;:::-;11066:47;;11161:9;11153:6;11149:22;11144:2;11133:9;11129:18;11122:50;11189:33;11215:6;11207;11189:33;:::i;:::-;11181:41;10682:546;-1:-1:-1;;;;;;10682:546:195:o;12227:128::-;12294:9;;;12315:11;;;12312:37;;;12329:18;;:::i;12360:168::-;12433:9;;;12464;;12481:15;;;12475:22;;12461:37;12451:71;;12502:18;;:::i;12533:125::-;12598:9;;;12619:10;;;12616:36;;;12632:18;;:::i;12663:217::-;12703:1;12729;12719:132;;12773:10;12768:3;12764:20;12761:1;12754:31;12808:4;12805:1;12798:15;12836:4;12833:1;12826:15;12719:132;-1:-1:-1;12865:9:195;;12663:217::o", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "stringToBytes32(string)": "cfb51928", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "testUniSymbol32Bytes()": "e4970fe0", "testUniSymbol32BytesEmpty()": "d0d9d4ce", "testUniSymbol33CharsSymbol()": "cbdb0cb7", "testUniSymbolCAPsReturns()": "e16eafba", "testUniSymbolEmptyString()": "7c6f90eb", "testUniSymbolFuzz(string)": "f22e0b3c", "testUniSymbolReturnBomb()": "72323e86", "testUniSymbolReturns()": "13893aad", "testUniSymbolReturnsZeroAddress()": "c5505ee0", "testUniSymbolTokenWithoutSymbol()": "2045ad11"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"source\",\"type\":\"string\"}],\"name\":\"stringToBytes32\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"result\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testUniSymbol32Bytes\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testUniSymbol32BytesEmpty\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testUniSymbol33CharsSymbol\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testUniSymbolCAPsReturns\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testUniSymbolEmptyString\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"}],\"name\":\"testUniSymbolFuzz\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testUniSymbolReturnBomb\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testUniSymbolReturns\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testUniSymbolReturnsZeroAddress\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testUniSymbolTokenWithoutSymbol\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"testUniSymbol33CharsSymbol()\":{\"notice\":\"Ref test case from 1inch: https://github.com/1inch/mooniswap/blob/02dccfab2ddbb8a409400288cb13441763370350/test/MooniFactory.js#L33, 33 character test is relevant because we limit our max data copy from the uniSymbol static call to 96 bytes. In this case, 33 chars symbol will take up 128 bytes. Calculated below:  | Offset (32 bytes)                                                                 |     | --------------------------------------------------------------------------------- |     | 0x0000000000000000000000000000000000000000000000000000000000000020               |     | Length (32 bytes)                                                                 |     | --------------------------------------------------------------------------------- |     | 0x0000000000000000000000000000000000000000000000000000000000000021               |     | Actual Data (64 bytes, 33 bytes of data + 31 bytes of padding)                                                                                                   |     | ------------------------------------------------------------------------------------------------------------------------- |     | 3031323334353637383930313233343536373839303132333435363738393132000000000000000000000000000000000000000000000000000000000000000000  |  total data length = 32 + 32 + 64 = 128 bytes\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/TokenSymbolTest.sol\":\"TokenSymbolTest\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/IPluginRegistry.sol\":{\"keccak256\":\"0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d\",\"dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/TokenSymbol.sol\":{\"keccak256\":\"0x628df064fdbdacfe6783964d7bf38cdf1b34e1ad07caa3cea39bf7468cc19b43\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://da6823ce0debaabe20f25281e81a4fc88de98d4df2942a5e276826ac381c227b\",\"dweb:/ipfs/QmNpEuQ25788xfcJwPk2xUB7fyP7fW5ENK2e9qgRqp1BcH\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/ERC20Base.sol\":{\"keccak256\":\"0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59\",\"dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL\"]},\"contracts/tokens/ERC4626DepositToken.sol\":{\"keccak256\":\"0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac\",\"dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol\":{\"keccak256\":\"0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22\",\"dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol\":{\"keccak256\":\"0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368\",\"dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB\"]},\"lib/1inch/token-plugins/contracts/ERC20Hooks.sol\":{\"keccak256\":\"0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5\",\"dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c\"]},\"lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol\":{\"keccak256\":\"0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8\",\"dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh\"]},\"lib/1inch/token-plugins/contracts/interfaces/IHook.sol\":{\"keccak256\":\"0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d\",\"dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS\"]},\"lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol\":{\"keccak256\":\"0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0\",\"dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z\"]},\"lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol\":{\"keccak256\":\"0x7d9d432e8f02168bf3f790e3dabcf36402782acf7ffa476cabe86fc4d8962eb2\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://1adc13e7f399f500ea5f81480ad149a50408fde7990a2c6347e6377486f389dc\",\"dweb:/ipfs/QmSvm5TUBJqknsqNJLLHqNS4MLSH5k3vNrbquVg6ZKSfx9\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol\":{\"keccak256\":\"0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e\",\"dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215\",\"dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318\",\"dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79\",\"dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol\":{\"keccak256\":\"0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834\",\"dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol\":{\"keccak256\":\"0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896\",\"dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Nonces.sol\":{\"keccak256\":\"0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e\",\"dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol\":{\"keccak256\":\"0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35\",\"dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211\",\"dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4\",\"dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol\":{\"keccak256\":\"0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f\",\"dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59\",\"dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Base.sol\":{\"keccak256\":\"0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224\",\"dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1\",\"dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e\",\"dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c\",\"dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"test/TokenSymbolTest.sol\":{\"keccak256\":\"0x089cf25f2c34233c7ed820c1aba91ca49cef877d1c475130d70cc96ba278e15d\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://1d5bad5d14b4a3a61f7293287e215b16619fa1668c87d420ba4586fb00344eee\",\"dweb:/ipfs/QmcjEew8PiVB3msZiSLcpTHB5w8RTtUyPgTttyQBLB14kG\"]},\"test/shared/StubValidator.sol\":{\"keccak256\":\"0x993fea94bdebadbce94bc843802e52f5bd8f586b8253d489e001714f437d616e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://28d1e05ef470d8a0269e85a213ef22e59d80388791c4d58859f997d71b8546db\",\"dweb:/ipfs/QmP7fBSFP3oqSuA3f3WqDnXPYtWbdknqJzbqGFvGDVCnjU\"]},\"test/shared/TestERC20.sol\":{\"keccak256\":\"0xc3cba336c9ab4ff556e4252d919dda3326f0dae67b7a4bf2d819723563f34b15\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://864e6e92219834223839b46add96d035ce4feeed8ab8f2eeba4c8db8209dc179\",\"dweb:/ipfs/QmYe2JnAdyf25dwkooQrocih2gJhcL3j55Tr6HQxc2J5pr\"]},\"test/stubs/ReturnBombAttackStub.sol\":{\"keccak256\":\"0x769a9e4cde92134574572b01c04564b201603549eda0a6fd7c0018c2bc7de534\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://326572383a04e6e0a574393dad30ce77d036588aa562cd9f821768f678368e0e\",\"dweb:/ipfs/QmatH6yd87AXr1d4wPK5oH9JpgN59ZsX5KV3QBqUsjCb7L\"]},\"test/stubs/TokenWithBytes32SymbolStub.sol\":{\"keccak256\":\"0xb6c9a55e95d18c11d387872fbda6af2da5b148fb35e4a96af211b6d7a1e7226f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da64a93d32206b664304e453b7671630d2d48871ecb4ebdcf1c5820d5cc45440\",\"dweb:/ipfs/QmcpgtaeNVesQbH2ebNDHuNSVnDujwJCNTA3rxdGLKiCs2\"]},\"test/stubs/TokenWithNoSymbolStub.sol\":{\"keccak256\":\"0x88414d53422f006bc77cc30b4485742768760e6d38b924f2202bcb582fc72dea\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5d781795d8cf06a77735c9d8588a7a3f90deed9ef601e4aadf97ff200def7339\",\"dweb:/ipfs/QmTcuNgf2ymjUP2yv79u1KUEh2UkPUCjzgiicMfXawQQES\"]},\"test/stubs/TokenWithStringCAPSSymbolStub.sol\":{\"keccak256\":\"0xaa95d1579f3c21b916891d3e7c48e1a6c94c3845b38cea0bb9a3b8c6f8fe5725\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8442d3775a0a1caf35d19cbe2b9396b5579bb0bec5111382d1166b516ef490f0\",\"dweb:/ipfs/QmZrNuxtPgXrnVW2tBzK1WLHiun9Vwge2954JzvzwuySvz\"]},\"test/stubs/TokenWithStringSymbolStub.sol\":{\"keccak256\":\"0x7e8379d9fa2089315d3d420d62e7ab87b972e59a47a3b36ae42d7ae410a5fc87\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9b5a75114dbaeb0e6485f57bbfacbabe25d5f11c9b74c70fdce2a2f1196aa998\",\"dweb:/ipfs/QmT2vquPjQb7M38dBJbxthzfXSN3xcLKmVkMvdgWb1ZDmm\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "source", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "stringToBytes32", "outputs": [{"internalType": "bytes32", "name": "result", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testUniSymbol32Bytes"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testUniSymbol32BytesEmpty"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testUniSymbol33CharsSymbol"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testUniSymbolCAPsReturns"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testUniSymbolEmptyString"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "testUniSymbolFuzz"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testUniSymbolReturnBomb"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testUniSymbolReturns"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "testUniSymbolReturnsZeroAddress"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testUniSymbolTokenWithoutSymbol"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {"testUniSymbol33CharsSymbol()": {"notice": "Ref test case from 1inch: https://github.com/1inch/mooniswap/blob/02dccfab2ddbb8a409400288cb13441763370350/test/MooniFactory.js#L33, 33 character test is relevant because we limit our max data copy from the uniSymbol static call to 96 bytes. In this case, 33 chars symbol will take up 128 bytes. Calculated below:  | Offset (32 bytes)                                                                 |     | --------------------------------------------------------------------------------- |     | 0x0000000000000000000000000000000000000000000000000000000000000020               |     | Length (32 bytes)                                                                 |     | --------------------------------------------------------------------------------- |     | 0x0000000000000000000000000000000000000000000000000000000000000021               |     | Actual Data (64 bytes, 33 bytes of data + 31 bytes of padding)                                                                                                   |     | ------------------------------------------------------------------------------------------------------------------------- |     | 3031323334353637383930313233343536373839303132333435363738393132000000000000000000000000000000000000000000000000000000000000000000  |  total data length = 32 + 32 + 64 = 128 bytes"}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/TokenSymbolTest.sol": "TokenSymbolTest"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IPluginRegistry.sol": {"keccak256": "0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf", "urls": ["bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d", "dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X"], "license": "MIT"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/TokenSymbol.sol": {"keccak256": "0x628df064fdbdacfe6783964d7bf38cdf1b34e1ad07caa3cea39bf7468cc19b43", "urls": ["bzz-raw://da6823ce0debaabe20f25281e81a4fc88de98d4df2942a5e276826ac381c227b", "dweb:/ipfs/QmNpEuQ25788xfcJwPk2xUB7fyP7fW5ENK2e9qgRqp1BcH"], "license": "GPL-3.0-only"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/ERC20Base.sol": {"keccak256": "0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b", "urls": ["bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59", "dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL"], "license": "MIT"}, "contracts/tokens/ERC4626DepositToken.sol": {"keccak256": "0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be", "urls": ["bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac", "dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM"], "license": "MIT"}, "lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol": {"keccak256": "0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e", "urls": ["bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22", "dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9"], "license": "MIT"}, "lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol": {"keccak256": "0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318", "urls": ["bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368", "dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/ERC20Hooks.sol": {"keccak256": "0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2", "urls": ["bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5", "dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol": {"keccak256": "0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875", "urls": ["bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8", "dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IHook.sol": {"keccak256": "0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80", "urls": ["bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d", "dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol": {"keccak256": "0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c", "urls": ["bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0", "dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z"], "license": "MIT"}, "lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol": {"keccak256": "0x7d9d432e8f02168bf3f790e3dabcf36402782acf7ffa476cabe86fc4d8962eb2", "urls": ["bzz-raw://1adc13e7f399f500ea5f81480ad149a50408fde7990a2c6347e6377486f389dc", "dweb:/ipfs/QmSvm5TUBJqknsqNJLLHqNS4MLSH5k3vNrbquVg6ZKSfx9"], "license": "MIT OR Apache-2.0"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol": {"keccak256": "0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541", "urls": ["bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e", "dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol": {"keccak256": "0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad", "urls": ["bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215", "dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9", "urls": ["bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318", "dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db", "urls": ["bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79", "dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol": {"keccak256": "0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8", "urls": ["bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834", "dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol": {"keccak256": "0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63", "urls": ["bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896", "dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Nonces.sol": {"keccak256": "0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f", "urls": ["bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e", "dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol": {"keccak256": "0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402", "urls": ["bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35", "dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52", "urls": ["bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211", "dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5", "urls": ["bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4", "dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol": {"keccak256": "0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e", "urls": ["bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f", "dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631", "urls": ["bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59", "dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Base.sol": {"keccak256": "0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c", "urls": ["bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224", "dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol": {"keccak256": "0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b", "urls": ["bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1", "dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Test.sol": {"keccak256": "0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe", "urls": ["bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e", "dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol": {"keccak256": "0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354", "urls": ["bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c", "dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC"], "license": "MIT OR Apache-2.0"}, "lib/openzeppelin-contracts/lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "test/TokenSymbolTest.sol": {"keccak256": "0x089cf25f2c34233c7ed820c1aba91ca49cef877d1c475130d70cc96ba278e15d", "urls": ["bzz-raw://1d5bad5d14b4a3a61f7293287e215b16619fa1668c87d420ba4586fb00344eee", "dweb:/ipfs/QmcjEew8PiVB3msZiSLcpTHB5w8RTtUyPgTttyQBLB14kG"], "license": "GPL-3.0-only"}, "test/shared/StubValidator.sol": {"keccak256": "0x993fea94bdebadbce94bc843802e52f5bd8f586b8253d489e001714f437d616e", "urls": ["bzz-raw://28d1e05ef470d8a0269e85a213ef22e59d80388791c4d58859f997d71b8546db", "dweb:/ipfs/QmP7fBSFP3oqSuA3f3WqDnXPYtWbdknqJzbqGFvGDVCnjU"], "license": "GPL-3.0-only"}, "test/shared/TestERC20.sol": {"keccak256": "0xc3cba336c9ab4ff556e4252d919dda3326f0dae67b7a4bf2d819723563f34b15", "urls": ["bzz-raw://864e6e92219834223839b46add96d035ce4feeed8ab8f2eeba4c8db8209dc179", "dweb:/ipfs/QmYe2JnAdyf25dwkooQrocih2gJhcL3j55Tr6HQxc2J5pr"], "license": "GPL-3.0-only"}, "test/stubs/ReturnBombAttackStub.sol": {"keccak256": "0x769a9e4cde92134574572b01c04564b201603549eda0a6fd7c0018c2bc7de534", "urls": ["bzz-raw://326572383a04e6e0a574393dad30ce77d036588aa562cd9f821768f678368e0e", "dweb:/ipfs/QmatH6yd87AXr1d4wPK5oH9JpgN59ZsX5KV3QBqUsjCb7L"], "license": "GPL-3.0-only"}, "test/stubs/TokenWithBytes32SymbolStub.sol": {"keccak256": "0xb6c9a55e95d18c11d387872fbda6af2da5b148fb35e4a96af211b6d7a1e7226f", "urls": ["bzz-raw://da64a93d32206b664304e453b7671630d2d48871ecb4ebdcf1c5820d5cc45440", "dweb:/ipfs/QmcpgtaeNVesQbH2ebNDHuNSVnDujwJCNTA3rxdGLKiCs2"], "license": "MIT"}, "test/stubs/TokenWithNoSymbolStub.sol": {"keccak256": "0x88414d53422f006bc77cc30b4485742768760e6d38b924f2202bcb582fc72dea", "urls": ["bzz-raw://5d781795d8cf06a77735c9d8588a7a3f90deed9ef601e4aadf97ff200def7339", "dweb:/ipfs/QmTcuNgf2ymjUP2yv79u1KUEh2UkPUCjzgiicMfXawQQES"], "license": "GPL-3.0-only"}, "test/stubs/TokenWithStringCAPSSymbolStub.sol": {"keccak256": "0xaa95d1579f3c21b916891d3e7c48e1a6c94c3845b38cea0bb9a3b8c6f8fe5725", "urls": ["bzz-raw://8442d3775a0a1caf35d19cbe2b9396b5579bb0bec5111382d1166b516ef490f0", "dweb:/ipfs/QmZrNuxtPgXrnVW2tBzK1WLHiun9Vwge2954JzvzwuySvz"], "license": "MIT"}, "test/stubs/TokenWithStringSymbolStub.sol": {"keccak256": "0x7e8379d9fa2089315d3d420d62e7ab87b972e59a47a3b36ae42d7ae410a5fc87", "urls": ["bzz-raw://9b5a75114dbaeb0e6485f57bbfacbabe25d5f11c9b74c70fdce2a2f1196aa998", "dweb:/ipfs/QmT2vquPjQb7M38dBJbxthzfXSN3xcLKmVkMvdgWb1ZDmm"], "license": "MIT"}}, "version": 1}, "id": 167}