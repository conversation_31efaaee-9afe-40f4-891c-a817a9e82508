{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "testLtvBorrowLAgainstX", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowLAgainstY", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowLAndXAgainstY", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowLAndYAgainstX", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowLeveragedLongX", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowLeveragedShortX", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowTooMuchXAndY", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowXAgainstL", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowXAgainstXFails", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowXAgainstXYFails", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowXAgainstY", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowXAndLAgainstY", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowYAgainstL", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowYAgainstX", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowYAgainstYFails", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBorrowYAndLAgainstX", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBurnLWithXAgainstL", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBurnWithXAgainstLAndY", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBurnWithYAgainstL", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvBurnWithYAgainstLAndX", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvWithdrawXWithLAgainstX", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvWithdrawXWithYAgainstX", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvWithdrawXWithYAgainstXAndL", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvWithdrawYWithLAgainstY", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvWithdrawYWithXAgainstY", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLtvWithdrawYWithXAgainstYAndL", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testMaxSlippageFails", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testSlippageBeyondQuadraticFee", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "error", "name": "AmmalgamCannotBorrowAgainstSameCollateral", "inputs": []}, {"type": "error", "name": "AmmalgamDepositIsNotStrictlyBigger", "inputs": []}, {"type": "error", "name": "AmmalgamLTV", "inputs": []}, {"type": "error", "name": "AmmalgamMaxSlippage", "inputs": []}, {"type": "error", "name": "AmmalgamTooMuchLeverage", "inputs": []}, {"type": "error", "name": "InsufficientLiquidity", "inputs": []}, {"type": "error", "name": "MissingGteActual", "inputs": []}, {"type": "error", "name": "MissingOutGteReserveOut", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "955:29933:152:-:0;;;3126:44:97;;;3166:4;-1:-1:-1;;3126:44:97;;;;;;;;1016:26:107;;;;;;;;;;;955:29933:152;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "955:29933:152:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;7464:717;;;:::i;:::-;;2605:474;;;:::i;6736:722::-;;;:::i;22676:618::-;;;:::i;2907:134:100:-;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;12003:477:152;;;:::i;3823:151:100:-;;;:::i;:::-;;;;;;;:::i;14728:794:152:-;;;:::i;18274:1106::-;;;:::i;9094:907::-;;;:::i;16324:994::-;;;:::i;3684:133:100:-;;;:::i;3385:141::-;;;:::i;4470:684:152:-;;;:::i;15528:790::-;;;:::i;3805:659::-;;;:::i;20494:1080::-;;;:::i;3193:186:100:-;;;:::i;:::-;;;;;;;:::i;3047:140::-;;;:::i;:::-;;;;;;;:::i;19386:1102:152:-;;;:::i;3532:146:100:-;;;:::i;:::-;;;;;;;:::i;12486:477:152:-;;;:::i;10007:898::-;;;:::i;3085:714::-;;;:::i;2128:471::-;;;:::i;2754:147:100:-;;;:::i;5160:750:152:-;;;:::i;2459:141:100:-;;;:::i;17324:944:152:-;;;:::i;1243:204:96:-;;;:::i;:::-;;;6174:14:195;;6167:22;6149:41;;6137:2;6122:18;1243:204:96;6009:187:195;10911:904:152;;;:::i;23300:533::-;;;:::i;14132:590::-;;;:::i;8187:901::-;;;:::i;13586:540::-;;;:::i;21580:1090::-;;;:::i;12969:611::-;;;:::i;1361:761::-;;;:::i;2606:142:100:-;;;:::i;1016:26:107:-;;;;;;;;;7464:717:152;7534:6;7569:4;7515:16;7603:19;7569:4;1031;7603:19;:::i;:::-;7584:38;-1:-1:-1;7632:16:152;7651:19;7662:8;1077:4;7651:19;:::i;:::-;7632:38;;7681:15;7699:68;7738:8;7748;7758;7699:38;:68::i;:::-;7681:86;-1:-1:-1;7778:25:152;7806:32;7819:13;7681:86;7829:3;7819:13;:::i;:::-;1297:2;7806:12;:32::i;:::-;7778:60;-1:-1:-1;7848:19:152;7870:28;7890:8;7778:60;7870:28;:::i;:::-;7848:50;-1:-1:-1;7909:15:152;7927:30;7937:19;7948:8;7937;:19;:::i;:::-;7927:9;:30::i;:::-;7909:48;;7968:41;8012:48;8041:8;8051;8012:28;:48::i;:::-;8070:22;;:33;;:47;;;:22;-1:-1:-1;8128:46:152;8070:22;8166:7;8128:24;:46::i;:::-;7505:676;;;;;;;;;7464:717::o;2605:474::-;2674:7;2656:15;2720:67;2674:7;1031:4;1077;2720:38;:67::i;:::-;2692:95;-1:-1:-1;2797:19:152;2819:42;2832:23;2692:95;2832:3;:23;:::i;2819:42::-;2797:64;;2872:41;2916:48;1031:4;1077;2916:28;:48::i;:::-;2974:22;;:33;;:47;;;:22;-1:-1:-1;3032:40:152;2974:22;3061:1;3064:7;3032:15;:40::i;:::-;2646:433;;;;2605:474::o;6736:722::-;6806:6;6841:4;6787:16;6875:19;6841:4;1031;6875:19;:::i;:::-;6856:38;-1:-1:-1;6904:16:152;6923:19;6934:8;1077:4;6923:19;:::i;:::-;6904:38;;6953:15;6971:68;7010:8;7020;7030;6971:38;:68::i;:::-;6953:86;-1:-1:-1;7050:25:152;7078:32;7091:13;6953:86;7101:3;7091:13;:::i;7078:32::-;7050:60;-1:-1:-1;7120:19:152;7142:28;7162:8;7050:60;7142:28;:::i;:::-;7120:50;-1:-1:-1;7181:15:152;7199:30;7209:19;7220:8;7209;:19;:::i;7199:30::-;7181:48;;7240:41;7284:48;7313:8;7323;7284:28;:48::i;:::-;7342:22;;:33;;:47;;;:22;-1:-1:-1;7400:51:152;7342:22;7438:12;:7;7448:2;7438:12;:::i;:::-;7400:24;:51::i;22676:618::-;22744:7;22725:16;22779:20;22798:1;1131:4;22779:20;:::i;:::-;22761:38;-1:-1:-1;22828:4:152;22861;22809:16;22895:19;22828:4;1031;22895:19;:::i;:::-;22876:38;-1:-1:-1;22924:16:152;22943:19;22954:8;1077:4;22943:19;:::i;:::-;22924:38;;22972:41;23016:48;23045:8;23055;23016:28;:48::i;:::-;23074:22;;:33;;:44;;;23128:22;;:32;;:42;;;23181:56;;-1:-1:-1;;;23181:56:152;;23074:22;;-1:-1:-1;;;;;;;;;;;;23181:15:152;;;:56;;-1:-1:-1;;;23197:39:152;23181:56;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;23247:40;23275:11;23247:27;:40::i;:::-;22715:579;;;;;;;22676:618::o;2907:134:100:-;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2999:35:100;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;12003:477:152:-;12081:4;12113;12059:19;12172:53;1031:4;12113;12059:19;;12172;:53::i;:::-;12235:22;;:33;;:47;;;12292:22;;12235;;-1:-1:-1;12327:7:152;;373:1:19;12292:32:152;;;;:42;12345:78;;-1:-1:-1;;;12345:78:152;;-1:-1:-1;;;;;;;;;;;12345:15:152;;;:78;;-1:-1:-1;;;12361:61:152;12345:78;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;12433:40;12461:11;12433:27;:40::i;:::-;12049:431;;;12003:477::o;3823:151:100:-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3926:41:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;14728:794:152:-;14806:4;14839:6;14874:4;14786:17;14907:68;14839:6;1031:4;14874;14907:38;:68::i;:::-;14889:86;-1:-1:-1;14986:25:152;15014:32;15027:13;14889:86;15037:3;15027:13;:::i;15014:32::-;14986:60;-1:-1:-1;15056:32:152;15091:28;15111:8;14986:60;15091:28;:::i;:::-;15056:63;-1:-1:-1;15129:19:152;15151:36;15178:9;15056:63;15151:36;:::i;:::-;15129:58;-1:-1:-1;15197:15:152;15215:30;15225:19;15236:8;15225;:19;:::i;15215:30::-;15197:48;;15256:41;15300:48;1031:4;1077;15300:28;:48::i;:::-;15256:92;-1:-1:-1;15412:9:152;15394:15;:11;15408:1;15394:15;:::i;:::-;:27;;;;:::i;:::-;15358:22;;:33;;:63;15431:22;;:32;;:42;;;15484:31;15358:22;15484:18;:31::i;18274:1106::-;18344:4;18374:6;18328:13;18406:24;18416:13;18374:6;18344:4;18416:13;:::i;18406:24::-;18390:40;-1:-1:-1;18441:17:152;18461:9;18469:1;18390:40;18461:9;:::i;:::-;18441:29;-1:-1:-1;18481:23:152;18519:1;18507:9;:5;18515:1;18507:9;:::i;:::-;:13;;;;:::i;:::-;18481:39;-1:-1:-1;18530:23:152;18568:1;18556:9;:5;18564:1;18556:9;:::i;:::-;:13;;;;:::i;:::-;18530:39;-1:-1:-1;18579:26:152;18608;18619:15;1031:4;18608:26;:::i;:::-;18579:55;-1:-1:-1;18644:26:152;18673;18684:15;1077:4;18673:26;:::i;:::-;18644:55;-1:-1:-1;18890:15:152;18938:88;19002:3;18978:21;1297:2;18978:15;:21;:::i;:::-;:27;;;;:::i;:::-;1077:4;1031;18938:39;:88::i;:::-;18920:106;;:15;:106;:::i;:::-;18890:136;;19109:41;19153:68;19182:18;19202;19153:28;:68::i;:::-;19109:112;-1:-1:-1;19267:17:152;19275:9;19267:5;:17;:::i;:::-;19231:22;;:53;19294:22;;19329:7;;373:1:19;19294:32:152;;;;:42;19347:26;19361:11;19347:13;:26::i;:::-;18318:1062;;;;;;;;;;18274:1106::o;9094:907::-;9168:4;9201:6;9235:7;9149:16;9272:19;9168:4;1031;9272:19;:::i;:::-;9253:38;-1:-1:-1;9301:16:152;9320:19;9331:8;1077:4;9320:19;:::i;:::-;9301:38;-1:-1:-1;9350:15:152;9368:78;9407:18;9418:7;9407:8;:18;:::i;:::-;9427:8;9437;9368:38;:78::i;:::-;9350:96;-1:-1:-1;9457:25:152;9485:32;9498:13;9350:96;9508:3;9498:13;:::i;9485:32::-;9457:60;-1:-1:-1;9527:19:152;9549:28;9569:8;9457:60;9549:28;:::i;:::-;9527:50;-1:-1:-1;9588:15:152;9606:30;9616:19;9627:8;9616;:19;:::i;9606:30::-;9588:48;;9647:41;9691:48;9720:8;9730;9691:28;:48::i;:::-;9749:22;;:33;;:47;;;9806:22;;9749;;-1:-1:-1;9841:7:152;;404:1:19;9806:32:152;;;;:42;9878:40;9906:11;9878:27;:40::i;:::-;9948:46;9973:11;9986:7;9948:24;:46::i;16324:994::-;16406:4;16452;16386:17;16485:33;16406:4;16452;16485:33;:::i;:::-;16466:52;-1:-1:-1;16545:4:152;16575:6;16529:13;16607:58;16545:4;16575:6;1031:4;16545;16607:24;:58::i;:::-;16591:74;-1:-1:-1;16676:16:152;16707:104;16787:3;1297:2;16748:29;16756:21;16748:5;:29;:::i;:::-;16747:37;;;;:::i;:::-;:43;;;;:::i;:::-;1031:4;1077;16707:39;:104::i;:::-;16676:135;-1:-1:-1;16821:15:152;16839:16;16676:135;16839:5;:16;:::i;:::-;16821:34;-1:-1:-1;16866:16:152;16885;16896:5;1031:4;16885:16;:::i;:::-;16866:35;-1:-1:-1;16911:16:152;16930;16941:5;1077:4;16930:16;:::i;:::-;16911:35;;16957:41;17001:48;17030:8;17040;17001:28;:48::i;:::-;16957:92;-1:-1:-1;17095:20:152;17106:9;17095:8;:20;:::i;:::-;17059:22;;:33;;:56;17125:22;;:41;;;17176:22;;:32;;:42;;;17276:35;17059:22;17308:2;17276:18;:35::i;:::-;16376:942;;;;;;;;;;;16324:994::o;3684:133:100:-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3775:35:100;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3480:39:100;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;4470:684:152:-;4542:4;4572:6;4526:13;4607:9;4615:1;4542:4;4607:9;:::i;:::-;4589:27;-1:-1:-1;4626:15:152;4664:90;4730:3;1297:2;4705:15;4589:27;4705:5;:15;:::i;4664:90::-;4656:98;;:5;:98;:::i;:::-;4626:128;;4764:13;4841:1;4780:58;4805:5;4812;1031:4;1077;4780:24;:58::i;:::-;:62;;;;:::i;:::-;4764:78;-1:-1:-1;4853:16:152;4872;4883:5;1031:4;4872:16;:::i;:::-;4853:35;-1:-1:-1;4898:16:152;4917;4928:5;1077:4;4917:16;:::i;:::-;4898:35;;4944:41;4988:51;5008:8;5018;5028:4;5034;4988:19;:51::i;:::-;5049:22;;:41;;;:22;-1:-1:-1;5101:46:152;5049:22;5130:7;5139;5101:15;:46::i;:::-;4516:638;;;;;;;;4470:684::o;15528:790::-;15606:4;15639:6;15674:4;15586:17;15707:68;15674:4;;1031;15707:38;:68::i;:::-;15689:86;-1:-1:-1;15786:25:152;15814:32;15827:13;15689:86;15837:3;15827:13;:::i;15814:32::-;15786:60;-1:-1:-1;15856:32:152;15891:28;15911:8;15786:60;15891:28;:::i;:::-;15856:63;-1:-1:-1;15929:19:152;15951:36;15978:9;15856:63;15951:36;:::i;:::-;15929:58;-1:-1:-1;15997:15:152;16015:30;16025:19;16036:8;16025;:19;:::i;16015:30::-;15997:48;;16056:41;16100:48;1031:4;1077;16100:28;:48::i;:::-;16056:92;-1:-1:-1;16194:23:152;16208:9;16194:11;:23;:::i;:::-;16158:22;;:33;;:59;16227:22;;:32;;:42;;;16280:31;16158:22;16280:18;:31::i;3805:659::-;3872:6;3904;3856:13;3947:78;4001:3;3987:11;1297:2;3872:6;3987:11;:::i;3947:78::-;3921:104;-1:-1:-1;4035:15:152;4053:23;3921:104;4053:5;:23;:::i;:::-;4035:41;;4087:13;4103:58;4128:5;4135;1031:4;1077;4103:24;:58::i;:::-;4087:74;-1:-1:-1;4172:16:152;4191;4202:5;1031:4;4191:16;:::i;:::-;4172:35;-1:-1:-1;4217:16:152;4236;4247:5;1077:4;4236:16;:::i;:::-;4217:35;;4263:41;4307:48;4336:8;4346;4307:28;:48::i;:::-;4365:22;;:41;;;:22;-1:-1:-1;4417:40:152;4365:22;;4449:7;4417:15;:40::i;20494:1080::-;20567:4;20597:6;20551:13;20629:24;20639:13;20597:6;20567:4;20639:13;:::i;20629:24::-;20613:40;-1:-1:-1;20663:16:152;20682:9;20690:1;20682:5;:9;:::i;:::-;20663:28;-1:-1:-1;20702:17:152;20722:9;20730:1;20722:5;:9;:::i;:::-;20702:29;-1:-1:-1;20742:23:152;20780:1;20768:9;:5;20776:1;20768:9;:::i;:::-;:13;;;;:::i;:::-;20742:39;-1:-1:-1;20791:23:152;20829:1;20817:9;:5;20825:1;20817:9;:::i;:::-;:13;;;;:::i;:::-;20791:39;-1:-1:-1;20840:26:152;20869;20880:15;1031:4;20869:26;:::i;:::-;20840:55;-1:-1:-1;20905:26:152;20934;20945:15;1077:4;20934:26;:::i;:::-;20905:55;-1:-1:-1;21089:15:152;21137:101;21214:3;1297:2;21178:26;21196:8;21178:15;:26;:::i;:::-;21177:34;;;;:::i;21137:101::-;21107:131;;:15;:131;:::i;:::-;21089:149;;21249:41;21293:68;21322:18;21342;21293:28;:68::i;:::-;21249:112;-1:-1:-1;21407:17:152;21415:9;21407:5;:17;:::i;:::-;21371:22;;:53;21434:22;;:33;;:44;;;21488:22;;21523:7;;373:1:19;21488:32:152;;;;:42;21541:26;21555:11;21541:13;:26::i;3193:186:100:-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3317:55:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3047:140;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;19386:1102:152;19455:4;19485:6;19439:13;19517:24;19527:13;19485:6;19455:4;19527:13;:::i;19517:24::-;19501:40;-1:-1:-1;19551:17:152;19598:1;19571:24;19581:13;19589:5;19581;:13;:::i;19571:24::-;:28;;;;:::i;:::-;19551:48;-1:-1:-1;19610:23:152;19648:1;19636:9;:5;19644:1;19636:9;:::i;:::-;:13;;;;:::i;:::-;19610:39;-1:-1:-1;19659:23:152;19697:1;19685:9;:5;19693:1;19685:9;:::i;:::-;:13;;;;:::i;:::-;19659:39;-1:-1:-1;19708:26:152;19737;19748:15;1031:4;19737:26;:::i;:::-;19708:55;-1:-1:-1;19773:26:152;19802;19813:15;1077:4;19802:26;:::i;:::-;19773:55;-1:-1:-1;19998:15:152;20046:88;20110:3;20086:21;1297:2;20086:15;:21;:::i;20046:88::-;20028:106;;:15;:106;:::i;:::-;19998:136;;20217:41;20261:68;20290:18;20310;20261:28;:68::i;:::-;20217:112;-1:-1:-1;20375:17:152;20383:9;20375:5;:17;:::i;:::-;20339:22;;:53;20402:22;;20437:7;;404:1:19;20402:32:152;;3532:146:100;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;12486:477:152;12564:4;12596;12542:19;12655:53;1031:4;12564;12542:19;;12655;:53::i;:::-;12718:22;;:33;;:47;;;12775:22;;12718;;-1:-1:-1;12810:7:152;;404:1:19;12775:32:152;;10007:898;10081:4;10114:6;10148:4;10062:16;10182:19;10081:4;1031;10182:19;:::i;:::-;10163:38;-1:-1:-1;10211:16:152;10230:19;10241:8;1077:4;10230:19;:::i;:::-;10211:38;-1:-1:-1;10260:15:152;10278:78;10317:18;10328:7;10317:8;:18;:::i;:::-;10337:8;10347;10278:38;:78::i;:::-;10260:96;-1:-1:-1;10367:25:152;10395:32;10408:13;10260:96;10418:3;10408:13;:::i;10395:32::-;10367:60;-1:-1:-1;10437:19:152;10459:28;10479:8;10367:60;10459:28;:::i;:::-;10437:50;-1:-1:-1;10498:15:152;10516:30;10526:19;10537:8;10526;:19;:::i;10516:30::-;10498:48;;10557:41;10601:48;10630:8;10640;10601:28;:48::i;:::-;10659:22;;:33;;:47;;;10716:22;;:32;;:42;;;10659:22;-1:-1:-1;10788:40:152;10659:22;10788:27;:40::i;:::-;10858;10874:11;10887:7;10896:1;10858:15;:40::i;3085:714::-;3152:6;3184;3136:13;3227:78;3281:3;3267:11;1297:2;3184:6;3267:11;:::i;3227:78::-;3201:104;-1:-1:-1;3315:15:152;3333:23;3201:104;3333:5;:23;:::i;:::-;3315:41;;3367:13;3383:58;3408:5;3415;1031:4;1077;3383:24;:58::i;:::-;3367:74;-1:-1:-1;3452:16:152;3471;3482:5;1031:4;3471:16;:::i;:::-;3452:35;-1:-1:-1;3497:16:152;3516;3527:5;1077:4;3516:16;:::i;:::-;3497:35;;3543:41;3587:48;3616:8;3626;3587:28;:48::i;:::-;3645:22;;:41;;;:22;-1:-1:-1;3697:95:152;3645:22;3726:62;3777:7;3786:1;3726:50;:62::i;:::-;3790:1;3697:15;:95::i;2128:471::-;2197:4;2179:15;2240:67;2197:4;1077;1031;2240:38;:67::i;:::-;2212:95;-1:-1:-1;2317:19:152;2339:42;2352:23;2212:95;2352:3;:23;:::i;2339:42::-;2317:64;;2392:41;2436:48;1031:4;1077;2436:28;:48::i;:::-;2494:22;;:33;;:47;;;:22;-1:-1:-1;2552:40:152;2494:22;2581:7;2590:1;2552:15;:40::i;2754:147:100:-;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5160:750:152;5233:4;5263:6;5217:13;5297:9;5305:1;5263:6;5297:9;:::i;:::-;5279:27;-1:-1:-1;5385:15:152;5423:90;5489:3;1297:2;5464:15;5279:27;5464:5;:15;:::i;5423:90::-;5415:98;;:5;:98;:::i;:::-;5385:128;;5524:13;5540:58;5565:5;5572;1031:4;1077;5540:24;:58::i;:::-;5524:74;-1:-1:-1;5609:16:152;5628;5639:5;1031:4;5628:16;:::i;:::-;5609:35;-1:-1:-1;5654:16:152;5673;5684:5;1077:4;5673:16;:::i;:::-;5654:35;;5700:41;5744:51;5764:8;5774;5784:4;5790;5744:19;:51::i;:::-;5805:22;;:41;;;:22;-1:-1:-1;5857:46:152;5805:22;5886:7;5895;5857:15;:46::i;2459:141:100:-;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;17324:944:152;17406:4;;17386:17;17485:33;17406:4;;17485:33;:::i;:::-;17466:52;-1:-1:-1;17545:4:152;17575:6;17529:13;17607:58;17545:4;17575:6;1031:4;17545;17607:24;:58::i;:::-;17591:74;-1:-1:-1;17676:16:152;17707:104;17787:3;1297:2;17748:29;17756:21;17748:5;:29;:::i;17707:104::-;17676:135;-1:-1:-1;17822:15:152;17840:16;17676:135;17840:5;:16;:::i;:::-;17822:34;-1:-1:-1;17867:16:152;17886;17897:5;1031:4;17886:16;:::i;:::-;17867:35;-1:-1:-1;17912:16:152;17931;17942:5;1077:4;17931:16;:::i;:::-;17912:35;;17958:41;18002:48;18031:8;18041;18002:28;:48::i;:::-;17958:92;-1:-1:-1;18096:20:152;18107:9;18096:8;:20;:::i;:::-;18060:22;;:33;;:56;18126:22;;:41;;;18177:22;;:32;;:42;;;18230:31;18060:22;18230:18;:31::i;1243:204:96:-;1302:7;;1282:4;;1302:7;;1298:143;;;-1:-1:-1;1332:7:96;;;;;1243:204::o;1298:143::-;1377:39;;-1:-1:-1;;;1377:39:96;;-1:-1:-1;;;;;;;;;;;1377:39:96;;;8021:51:195;;;-1:-1:-1;;;8088:18:195;;;8081:34;1428:1:96;;1377:7;;7994:18:195;;1377:39:96;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;1370:60;;1243:204;:::o;10911:904:152:-;10985:4;11018:6;11052:4;10966:16;11086:19;10985:4;1031;11086:19;:::i;:::-;11067:38;-1:-1:-1;11115:16:152;11134:19;11145:8;1077:4;11134:19;:::i;:::-;11115:38;-1:-1:-1;11164:15:152;11182:78;11221:18;11232:7;11221:8;:18;:::i;11182:78::-;11164:96;-1:-1:-1;11271:25:152;11299:32;11312:13;11164:96;11322:3;11312:13;:::i;11299:32::-;11271:60;-1:-1:-1;11341:19:152;11363:28;11383:8;11271:60;11363:28;:::i;:::-;11341:50;-1:-1:-1;11402:15:152;11420:30;11430:19;11441:8;11430;:19;:::i;11420:30::-;11402:48;;11461:41;11505:48;11534:8;11544;11505:28;:48::i;:::-;11563:22;;:33;;:47;;;11620:22;;11563;;-1:-1:-1;11655:7:152;;373:1:19;11620:32:152;;23300:533;23378:4;23411;23359:16;23444:26;23457:9;23468:1;23444:12;:26::i;:::-;23425:45;;23481:15;23499:66;23539:5;23546:8;23556;23499:39;:66::i;:::-;23481:84;;23575:41;23619:48;23648:8;23658;23619:28;:48::i;:::-;23677:22;;:33;;:44;;;:22;-1:-1:-1;23732:40:152;23677:22;23732:27;:40::i;:::-;23783:43;23799:11;23812:1;23815:7;23824:1;23783:15;:43::i;:::-;23349:484;;;;;23300:533::o;14132:590::-;14212:4;14190:19;14246:15;14260:1;14212:4;14246:15;:::i;:::-;14226:35;-1:-1:-1;14271:15:152;14301:98;14375:3;1297:2;14342:23;14226:35;14342:11;:23;:::i;14301:98::-;14271:128;;14410:41;14454:48;1031:4;1077;14454:28;:48::i;:::-;14410:92;-1:-1:-1;14548:23:152;14562:9;14548:11;:23;:::i;:::-;14512:22;;:33;;:59;14581:22;;:32;;:42;;;14681:34;14512:22;404:1:19;14681:18:152;:34::i;8187:901::-;8261:4;8294:6;8328:7;8242:16;8365:19;8261:4;1031;8365:19;:::i;:::-;8346:38;-1:-1:-1;8394:16:152;8413:19;8424:8;1077:4;8413:19;:::i;:::-;8394:38;-1:-1:-1;8443:15:152;8461:78;8500:18;8511:7;8500:8;:18;:::i;8461:78::-;8443:96;-1:-1:-1;8550:25:152;8578:32;8591:13;8443:96;8601:3;8591:13;:::i;8578:32::-;8550:60;-1:-1:-1;8620:19:152;8642:28;8662:8;8550:60;8642:28;:::i;:::-;8620:50;-1:-1:-1;8681:15:152;8699:30;8709:19;8720:8;8709;:19;:::i;8699:30::-;8681:48;;8740:41;8784:48;8813:8;8823;8784:28;:48::i;:::-;8842:22;;:33;;:47;;;8899:22;;:32;;:42;;;8842:22;-1:-1:-1;8971:40:152;8842:22;8971:27;:40::i;:::-;9041;9057:11;9070:1;9073:7;9041:15;:40::i;13586:540::-;13666:4;13644:19;13700:15;13714:1;13666:4;13700:15;:::i;:::-;13680:35;-1:-1:-1;13725:15:152;13755:98;13829:3;1297:2;13796:23;13680:35;13796:11;:23;:::i;13755:98::-;13725:128;;13864:41;13908:48;1031:4;1077;13908:28;:48::i;:::-;13864:92;-1:-1:-1;14002:23:152;14016:9;14002:11;:23;:::i;:::-;13966:22;;:33;;:59;14035:22;;:32;;:42;;;14088:31;13966:22;14088:18;:31::i;21580:1090::-;21653:4;21683:6;21637:13;21715:24;21725:13;21683:6;21653:4;21725:13;:::i;21715:24::-;21699:40;-1:-1:-1;21749:16:152;21768:10;21776:2;21768:5;:10;:::i;:::-;21749:29;-1:-1:-1;21789:17:152;21809:26;21822:9;:5;21830:1;21822:9;:::i;:::-;21833:1;21809:12;:26::i;:::-;21789:46;-1:-1:-1;21846:23:152;21872:9;21880:1;21872:5;:9;:::i;:::-;21846:35;-1:-1:-1;21891:23:152;21917:9;21925:1;21917:5;:9;:::i;:::-;21891:35;-1:-1:-1;21936:26:152;21965;21976:15;1031:4;21965:26;:::i;:::-;21936:55;-1:-1:-1;22001:26:152;22030;22041:15;1077:4;22030:26;:::i;:::-;22001:55;-1:-1:-1;22185:15:152;22233:101;22310:3;1297:2;22274:26;22292:8;22274:15;:26;:::i;22233:101::-;22203:131;;:15;:131;:::i;:::-;22185:149;;22345:41;22389:68;22418:18;22438;22389:28;:68::i;:::-;22345:112;-1:-1:-1;22503:17:152;22511:9;22503:5;:17;:::i;:::-;22467:22;;:53;22530:22;;22467:33;22530;:44;;;22584:22;;22619:7;;404:1:19;22584:32:152;;12969:611;13048:6;13086;13026:19;13157:3;1297:2;1181:19;1077:4;1031;1181:19;:::i;:::-;13121:26;;:11;:26;:::i;:::-;:32;;;;:::i;:::-;13120:40;;;;:::i;:::-;13102:58;;13171:41;13215:53;1031:4;1077;13255:5;13262;13215:19;:53::i;:::-;13278:22;;:33;;:47;;;13335:22;;:33;;;;:47;;;13392:22;;:32;;:42;;;13445:78;-1:-1:-1;;;13445:78:152;;13278:22;;-1:-1:-1;;;;;;;;;;;;13445:15:152;;;:78;;-1:-1:-1;;;13461:61:152;373:1:19;13445:78:152;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;13533:40;13561:11;13533:27;:40::i;1361:761::-;1431:4;1461;1415:13;1493:9;1431:4;1501:1;1493:9;:::i;:::-;1475:27;-1:-1:-1;1570:15:152;1588:9;:5;1596:1;1588:9;:::i;:::-;1570:27;;1608:13;1624:58;1649:5;1656;1031:4;1077;1624:24;:58::i;:::-;1608:74;;1692:15;1710:62;1735:7;1744;1031:4;1077;1710:24;:62::i;:::-;1692:80;;1783:41;1827:53;1031:4;1077;1867:5;1874;1827:19;:53::i;:::-;1890:22;;:41;;;1941:22;;:32;;:42;;;1994:71;;-1:-1:-1;;;1994:71:152;;1890:22;;-1:-1:-1;;;;;;;;;;;;1994:15:152;;;:71;;-1:-1:-1;;;2010:54:152;1994:71;;;:::i;2606:142:100:-;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2702:39:100;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;10534:256:187:-;10668:7;10690:97;10748:9;10759;10770:10;10782:1;10785;10690:57;:97::i;:::-;10683:104;;10534:256;;;;;;:::o;6215:704:89:-;6277:7;6300:1;6305;6300:6;6296:150;;6400:35;1035:4:79;6400:11:89;:35::i;:::-;6896:1;6891;6887;:5;6886:11;;;;;:::i;:::-;;6900:1;6886:15;6876:5;;;6860:42;6853:49;;6215:704;;;;;:::o;20567:5181::-;20615:7;20733:1;20728;:6;20724:53;;-1:-1:-1;20761:1:89;20567:5181::o;20724:53::-;21717:1;21745;-1:-1:-1;;;21765:16:89;;21761:92;;21808:3;21801:10;;;;;21836:2;21829:9;21761:92;21877:7;21870:2;:15;21866:90;;21912:2;21905:9;;;;;21939:2;21932:9;21866:90;21980:7;21973:2;:15;21969:90;;22015:2;22008:9;;;;;22042:2;22035:9;21969:90;22083:7;22076:2;:15;22072:89;;22118:2;22111:9;;;;;22145:1;22138:8;22072:89;22185:6;22178:2;:14;22174:87;;22219:1;22212:8;;;;;22245:1;22238:8;22174:87;22285:6;22278:2;:14;22274:87;;22319:1;22312:8;;;;;22345:1;22338:8;22274:87;22385:6;22378:2;:14;22374:61;;22419:1;22412:8;22374:61;22861:1;:6;22872:1;22860:13;;;;;24771:1;22860:13;24771:6;;;;:::i;:::-;;24766:2;:11;24765:18;;24760:23;;24891:1;24884:2;24880:1;:6;;;;;:::i;:::-;;24875:2;:11;24874:18;;24869:23;;25002:1;24995:2;24991:1;:6;;;;;:::i;:::-;;24986:2;:11;24985:18;;24980:23;;25111:1;25104:2;25100:1;:6;;;;;:::i;:::-;;25095:2;:11;25094:18;;25089:23;;25221:1;25214:2;25210:1;:6;;;;;:::i;:::-;;25205:2;:11;25204:18;;25199:23;;25331:1;25324:2;25320:1;:6;;;;;:::i;:::-;;25315:2;:11;25314:18;;25309:23;;25703:28;25728:2;25724:1;:6;;;;;:::i;:::-;;25719:11;;;34795:145:90;25703:28:89;25698:33;;;20567:5181;-1:-1:-1;;;20567:5181:89:o;24422:237:152:-;24540:41;;:::i;:::-;24600:52;24620:8;24630;24640:4;24646:5;24600:19;:52::i;27490:433::-;27602:41;27646:23;27657:11;27646:10;:23::i;:::-;27602:67;-1:-1:-1;27714:11:152;:7;27724:1;27714:11;:::i;:::-;27679:22;;:32;;:46;27736:48;;-1:-1:-1;;;27736:48:152;;-1:-1:-1;;;;;;;;;;;27736:15:152;;;:48;;-1:-1:-1;;;27752:31:152;27736:48;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;27794:40;27822:11;27794:27;:40::i;:::-;27876;27904:11;27876:27;:40::i;24665:237::-;24783:41;;:::i;:::-;24843:52;24863:8;24873;24883:5;24890:4;24843:19;:52::i;25987:176::-;26107:49;26123:11;26136:7;26145;26154:1;26107:15;:49::i;4938:334:29:-;5034:69;5091:11;5034:56;:69::i;:::-;5113:36;5152:30;5170:11;5152:17;:30::i;:::-;5113:69;;5192:73;5215:14;5231:11;:33;;;5192:22;:73::i;:::-;5024:248;4938:334;:::o;24908:1073:152:-;25077:41;;:::i;:::-;25130:29;25170:30;25180:19;25191:8;25180;:19;:::i;25170:30::-;25130:71;-1:-1:-1;25211:34:152;25248:71;25260:30;25270:19;25281:8;25270;:19;:::i;25260:30::-;-1:-1:-1;;;25297:21:152;-1:-1:-1;;;;;25248:71:152;:11;:71::i;:::-;25211:108;-1:-1:-1;25329:22:152;25413:7;25354:43;25388:8;25364:21;:8;-1:-1:-1;;;25364:21:152;:::i;:::-;:32;;;;:::i;25354:43::-;:55;;-1:-1:-1;;;25354:55:152;:::i;:::-;:67;;;;:::i;:::-;25329:92;;25445:529;;;;;;;;;;;;;;;;25503:1;25445:529;;;;25507:1;25445:529;;;;25510:1;25445:529;;;;25513:1;25445:529;;;;25516:1;25445:529;;;;25519:1;25445:529;;;;;;;25554:15;:36;;25589:1;25554:36;;;25572:14;25554:36;25445:529;;;;25623:15;:136;;25690:50;25700:39;-1:-1:-1;;;;;;;;25700:39:152;:::i;25690:50::-;:69;;25744:14;25690:69;:::i;:::-;25623:136;;;25657:14;25623:136;25445:529;;;;25801:26;25445:529;;;;25864:21;-1:-1:-1;;;;;25445:529:152;;;;;25916:8;25445:529;;;;25955:8;25445:529;;;25431:543;;25120:861;;;24908:1073;;;;;;:::o;27929:144::-;28032:34;28051:11;28064:1;28032:18;:34::i;:::-;27929:144;:::o;8761:256:187:-;8895:7;8917:97;8976:8;8986:9;8997:10;9009:1;9012;8917:58;:97::i;29248:134:152:-;29346:29;29360:11;29373:1;29346:13;:29::i;6564:436:187:-;6700:17;;6747:30;6757:19;6768:8;6757;:19;:::i;6747:30::-;6725:52;;6788:11;6803:1;6788:16;6784:214;;774:7;6828:28;6838:17;6848:7;6838;:17;:::i;6828:28::-;:48;;;;:::i;:::-;6816:60;;6784:214;;;6911:80;6946:8;6921:21;6931:11;6921:7;:21;:::i;:::-;6920:34;;;;:::i;:::-;6982:8;6957:21;6967:11;6957:7;:21;:::i;:::-;6956:34;;;;:::i;:::-;6911:8;:80::i;:::-;6899:92;;6784:214;6719:281;6564:436;;;;;;:::o;28079:599:152:-;28201:41;28245:23;28256:11;28245:10;:23::i;:::-;28303:22;;:33;;;28201:67;;-1:-1:-1;28382:40:152;28399:23;28303:33;28382:40;:::i;:::-;28346:22;;:33;;:76;28433:48;;-1:-1:-1;;;28433:48:152;;-1:-1:-1;;;;;;;;;;;28433:15:152;;;:48;;-1:-1:-1;;;28449:31:152;28433:48;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;28491:40;28519:11;28491:27;:40::i;:::-;28571:22;;28607:14;;279:1:19;28571:33:152;;;;:50;28631:40;28659:11;28631:27;:40::i;28684:558::-;28787:41;28831:23;28842:11;28831:10;:23::i;:::-;28889:22;;:33;;;28787:67;;-1:-1:-1;28968:18:152;28985:1;28889:33;28968:18;:::i;:::-;28932:22;;311:1:19;28932:33:152;;;:54;28997:48;;-1:-1:-1;;;28997:48:152;;-1:-1:-1;;;;;;;;;;;28997:15:152;;;:48;;-1:-1:-1;;;29013:31:152;28997:48;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;29055:40;29083:11;29055:27;:40::i;:::-;29135:22;;:33;;:50;;;29195:40;29223:11;29195:27;:40::i;24225:191::-;24360:7;24403:6;24386:14;24403:6;24386:5;:14;:::i;:::-;:23;;;;:::i;26169:1315::-;26346:41;26390:23;26401:11;26390:10;:23::i;:::-;26346:67;;26428:7;26439:1;26428:12;26424:1003;;26491:19;26501:9;26491:7;:19;:::i;:::-;26456:22;;373:1:19;26456:32:152;;;;:54;26524:48;;-1:-1:-1;;;26524:48:152;;-1:-1:-1;;;;;;;;;;;26524:15:152;;;:48;;-1:-1:-1;;;26540:31:152;26524:48;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;26586:40;26614:11;26586:27;:40::i;:::-;26424:1003;;;26647:7;26658:1;26647:12;26643:784;;26710:19;26720:9;26710:7;:19;:::i;:::-;26675:22;;404:1:19;26675:32:152;;26643:784;26947:19;26957:9;26947:7;:19;:::i;:::-;26912:22;;:32;;:54;26980:22;;:32;;:42;;;27037:48;;-1:-1:-1;;;27037:48:152;;-1:-1:-1;;;;;;;;;;;27037:15:152;;;:48;;-1:-1:-1;;;27053:31:152;373:1:19;27037:48:152;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;27099:40;27127:11;27099:27;:40::i;:::-;27189:22;;:32;;:42;;;27280:19;27290:9;27280:7;:19;:::i;:::-;27245:22;;:32;;:54;27314:48;;-1:-1:-1;;;27314:48:152;;-1:-1:-1;;;;;;;;;;;27314:15:152;;;:48;;-1:-1:-1;;;27330:31:152;27314:48;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;27376:40;27404:11;27376:27;:40::i;:::-;27437;27465:11;27437:27;:40::i;4198:562:193:-;4416:20;4466:10;4452;:24;4448:87;;4499:25;;-1:-1:-1;;;4499:25:193;;;;;;;;;;;4448:87;4544:21;4568:26;4581:13;4568:10;:26;:::i;:::-;4544:50;;4604:20;4627:76;4640:13;4655:10;4667:9;4678:10;4690:9;4701:1;4627:12;:76::i;:::-;4604:99;-1:-1:-1;4729:24:193;4744:9;4604:99;4729:24;:::i;:::-;4714:39;4198:562;-1:-1:-1;;;;;;;;4198:562:193:o;1776:194:79:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;29987:899:152;30086:29;;:::i;:::-;30127:28;:275;;;;;;;;30172:11;:22;;;30195:1;30172:25;;;;;;;:::i;:::-;;;;;30127:275;;;;30211:11;:22;;;30234:1;30211:25;;;;;;;:::i;:::-;;;;;30127:275;;;;30250:11;:22;;;30273:1;30250:25;;;;;;;:::i;:::-;;;;;30127:275;;;;30289:11;:22;;;30312:1;30289:25;;;;;;;:::i;:::-;;;;;30127:275;;;;30328:11;:22;;;30351:1;30328:25;;;;;;;:::i;:::-;;;;;30127:275;;;;30367:11;:22;;;30390:1;30367:25;;;;;;;:::i;:::-;;;;;30127:275;;;;;30419:460;;;;;;;;30468:10;30419:460;;;;30511:11;:29;;;30419:460;;;;30573:11;:29;;;30419:460;;;;30644:11;:38;;;30419:460;;;;30719:11;:33;;;30419:460;;;;30783:11;:27;;;30419:460;;;;30841:11;:27;;;30419:460;;;30412:467;;;29987:899;;;:::o;3908:540:29:-;4131:22;;:33;4094;;;;:70;4090:106;;4173:23;;-1:-1:-1;;;4173:23:29;;;;;;;;;;;4090:106;4259:22;;:33;;;;4306;;;;4353:32;;;;4399;;;;;4207:234;;4353:32;4207:38;:234::i;3450:452::-;3546:36;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3546:36:29;3628:22;;:33;3594:31;;;:67;3750:27;3628:22;3750:14;:27::i;:::-;3710:36;;;3671:106;3672:36;;;3671:106;3868:27;3883:11;3868:14;:27::i;:::-;3827:37;;;3787:108;;;3788:14;3450:452;-1:-1:-1;3450:452:29:o;4454:240::-;4601:47;4610:14;4626:21;4601:8;:47::i;:::-;;;4658:29;4672:14;4658:13;:29::i;7242:3683:89:-;7324:14;7375:12;7389:11;7404:12;7411:1;7414;7404:6;:12::i;:::-;7374:42;;;;7498:4;7506:1;7498:9;7494:365;;7833:11;7827:3;:17;;;;;:::i;:::-;;7820:24;;;;;;7494:365;7984:4;7969:11;:19;7965:142;;8008:84;5312:5;8028:16;;5311:36;940:4:79;5306:42:89;8008:11;:84::i;:::-;8359:17;8510:11;8507:1;8504;8497:25;8902:12;8932:15;;;8917:31;;9067:22;;;;;9800:1;9781;:15;;9780:21;;10033;;;10029:25;;10018:36;10103:21;;;10099:25;;10088:36;10175:21;;;10171:25;;10160:36;10246:21;;;10242:25;;10231:36;10319:21;;;10315:25;;10304:36;10393:21;;;10389:25;;;10378:36;9309:12;;;;9305:23;;;9330:1;9301:31;8622:18;;;8612:29;;;9416:11;;;;8665:19;;;;9160:14;;;;9409:18;;;;10868:13;;-1:-1:-1;;7242:3683:89;;;;;:::o;5904:470:193:-;6122:23;;6180:24;6192:12;6180:9;:24;:::i;:::-;6157:47;;6214:21;6238:75;6251:12;6265:9;6276:10;6288:9;6299:10;6311:1;6238:12;:75::i;:::-;6214:99;-1:-1:-1;6341:26:193;6214:99;6341:10;:26;:::i;29388:593:152:-;29505:41;29549:23;29560:11;29549:10;:23::i;:::-;29607:22;;:33;29505:67;;-1:-1:-1;29686:40:152;29703:23;29607:33;29686:40;:::i;:::-;29650:22;;:76;29736:48;;-1:-1:-1;;;29736:48:152;;-1:-1:-1;;;;;;;;;;;29736:15:152;;;:48;;-1:-1:-1;;;29752:31:152;29736:48;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;29794:40;29822:11;29794:27;:40::i;:::-;29874:22;;29910:14;;29874:22;:33;;5617:111:89;5675:7;5312:5;;;5709;;;5311:36;5306:42;;5701:20;5071:294;6865:564:193;7065:9;7086:17;7106:36;7123:8;7133;7106:16;:36::i;:::-;7086:56;;7152:17;7172:36;7189:8;7199;7172:16;:36::i;:::-;7152:56;-1:-1:-1;7219:9:193;7231:21;7152:56;7231:9;:21;:::i;:::-;7219:33;-1:-1:-1;7262:21:193;7286:81;7219:33;7302:64;7315:40;7332:1;7335:19;7346:8;7335;:19;:::i;:::-;7315:16;:40::i;:::-;7357:8;7302:12;:64::i;:::-;7286:12;:81::i;:::-;7262:105;;7381:41;7398:13;7413:8;7381:16;:41::i;:::-;7377:45;6865:564;-1:-1:-1;;;;;;;;;;;6865:564:193:o;5278:393:29:-;5513:1;5495:15;:19;:43;;;;;5537:1;5518:16;:20;5495:43;5494:94;;;;5562:1;5544:15;:19;:43;;;;;5586:1;5567:16;:20;5544:43;5490:175;;;5611:43;;-1:-1:-1;;;5611:43:29;;;;;;;;;;;9114:996;9302:22;;:32;;;;9415;;;;;9302;;9415:36;9411:342;;9532:22;;9492:250;;373:1:19;9532:32:29;;;;9582:11;:29;;;9668:11;:38;;;9724:4;9492:22;:250::i;:::-;9467:275;;;;:::i;:::-;;;9411:342;9766:22;;:32;;;:36;9762:342;;9883:22;;9843:250;;404:1:19;9883:32:29;;;;9933:11;:29;;;10019:11;:38;;;10075:4;9843:22;:250::i;:::-;9818:275;;;;:::i;:::-;;;9762:342;9114:996;;;:::o;8096:1012::-;8287:22;;:33;;;8407;;;;8287;;8403:37;8399:347;;8522:22;;8482:253;;279:1:19;8522:33:29;;;;8573:11;:29;;;8660:11;:38;;;8716:5;8482:22;:253::i;:::-;8456:279;;;;:::i;:::-;;;8399:347;8763:22;;:33;;;8759:37;8755:347;;8878:22;;8838:253;;311:1:19;8878:33:29;;;;8929:11;:29;;;9016:11;:38;;;9072:5;8838:22;:253::i;14658:1090::-;14789:27;14818:33;14867:14;:36;;;14907:1;14867:41;:86;;;;-1:-1:-1;14912:36:29;;;;:41;14867:86;14863:105;;;-1:-1:-1;14963:1:29;;-1:-1:-1;14963:1:29;14955:13;;14863:105;15031:37;15053:14;15031:21;:37::i;:::-;-1:-1:-1;14979:89:29;;-1:-1:-1;14979:89:29;-1:-1:-1;15082:30:29;;:57;;;;;15138:1;15116:19;:23;15082:57;15078:131;;;15162:36;;-1:-1:-1;;;15162:36:29;;;;;;;;;;;15078:131;15522:97;15542:19;15587:14;:31;;;15563:21;:55;15522:19;:97::i;:::-;15484:135;;4401:3:30;15683:19:29;:26;3784:2:30;15638:25:29;:42;:71;15634:97;;;15718:13;;-1:-1:-1;;;15718:13:29;;;;;;;;;;;15634:97;14658:1090;;;;;:::o;18782:684::-;18971:37;;;;18931;;19085:36;;;;19046;;;;18931:77;;;;;19046:75;19140:17;;19136:314;;19221:13;19202:16;:32;:142;;;;19328:16;4008:3:30;19282:13:29;19263:16;:32;19262:63;:82;19202:142;19177:259;;;19392:25;;-1:-1:-1;;;19392:25:29;;;;;;;;;;;1027:550:89;1088:12;;-1:-1:-1;;1471:1:89;1468;1461:20;1501:9;;;;1549:11;;;1535:12;;;;1531:30;;;;;1027:550;-1:-1:-1;;1027:550:89:o;9228:484:193:-;9309:16;9352:6;9341:7;:17;9337:73;;9381:18;;-1:-1:-1;;;9381:18:193;;;;;;;;;;;9337:73;9439:15;404:2;9439:6;:15;:::i;:::-;9423:13;:7;9433:3;9423:13;:::i;:::-;:31;9419:287;;;9616:12;404:2;9616:3;:12;:::i;:::-;9606:23;;:6;:23;:::i;:::-;9595:34;;9419:287;;;9672:16;9681:7;9672:6;:16;:::i;:::-;9671:24;;9692:3;9671:24;:::i;10041:480::-;10124:14;404:2;10170:38;10183:8;10194:12;404:2;10194:3;:12;:::i;10170:38::-;:47;;;;:::i;:::-;10154:13;:7;10164:3;10154:13;:::i;:::-;:63;10150:365;;;10389:38;10402:8;10413:12;404:2;10413:3;:12;:::i;10150:365::-;10497:7;10467:27;10480:8;10490:3;10467:12;:27::i;:::-;:37;;;;:::i;10973:428:29:-;11157:21;11194:15;11213:1;11194:20;11190:34;;-1:-1:-1;11223:1:29;11216:8;;11190:34;11250:144;11278:64;11293:15;-1:-1:-1;;;11315:17:29;11334:7;11278:14;:64::i;:::-;-1:-1:-1;;;11349:26:29;11377:7;11250:14;:144::i;:::-;11234:160;;10973:428;;;;;;;:::o;12472:393::-;12674:23;12713:15;12732:1;12713:20;12709:34;;-1:-1:-1;12742:1:29;12735:8;;12709:34;12771:87;12786:15;12803:17;12822:26;12850:7;12771:14;:87::i;13211:1441::-;13478:36;;;;13438:37;;13596:36;;;;13556:37;;;;13317:27;;;;;;13438:76;-1:-1:-1;13438:76:29;13556;13647:25;;13438:76;13647:54;;;13677:24;13676:25;13647:54;13643:1003;;;13906:14;:37;;;13867:14;:36;;;:76;13806:14;:37;;;13767:14;:36;;;:76;:177;13745:199;;13643:1003;;;13980:24;13975:671;;14109:14;:37;;;14070:14;:36;;;:76;14048:98;;14232:14;:36;;;14192:14;:37;;;:76;14164:104;;14307:4;14296:15;;13975:671;;;14333:24;14328:318;;14462:14;:37;;;14423:14;:36;;;:76;14401:98;;14585:14;:36;;;14545:14;:37;;;:76;14517:104;;14328:318;13396:1256;;13211:1441;;;;;:::o;18393:383::-;18527:7;18573:21;18550:19;:44;18546:103;;18617:21;;-1:-1:-1;;;18617:21:29;;;;;;;;;;;18546:103;18665:104;18678:43;18702:19;18678:21;:43;:::i;:::-;18724;18748:19;18724:21;:43;:::i;1908:204:20:-;1997:14;2032:5;2036:1;2032;:5;:::i;:::-;2023:14;;2056:10;:49;;2095:10;2104:1;2095:6;:10;:::i;:::-;2056:49;;;2069:23;2082:6;2090:1;2069:12;:23::i;-1:-1:-1:-;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::o;14:637:195:-;204:2;216:21;;;286:13;;189:18;;;308:22;;;156:4;;387:15;;;361:2;346:18;;;156:4;430:195;444:6;441:1;438:13;430:195;;;509:13;;-1:-1:-1;;;;;505:39:195;493:52;;574:2;600:15;;;;565:12;;;;541:1;459:9;430:195;;;-1:-1:-1;642:3:195;;14:637;-1:-1:-1;;;;;14:637:195:o;656:289::-;698:3;736:5;730:12;763:6;758:3;751:19;819:6;812:4;805:5;801:16;794:4;789:3;785:14;779:47;871:1;864:4;855:6;850:3;846:16;842:27;835:38;934:4;927:2;923:7;918:2;910:6;906:15;902:29;897:3;893:39;889:50;882:57;;;656:289;;;;:::o;950:1628::-;1156:4;1204:2;1193:9;1189:18;1234:2;1223:9;1216:21;1257:6;1292;1286:13;1323:6;1315;1308:22;1361:2;1350:9;1346:18;1339:25;;1423:2;1413:6;1410:1;1406:14;1395:9;1391:30;1387:39;1373:53;;1461:2;1453:6;1449:15;1482:1;1492:1057;1506:6;1503:1;1500:13;1492:1057;;;-1:-1:-1;;1571:22:195;;;1567:36;1555:49;;1627:13;;1714:9;;-1:-1:-1;;;;;1710:35:195;1695:51;;1793:2;1785:11;;;1779:18;1679:2;1817:15;;;1810:27;;;1898:19;;1667:15;;;1930:24;;;2085:21;;;1988:2;2038:1;2034:16;;;2022:29;;2018:38;;;1976:15;;;;-1:-1:-1;2144:296:195;2160:8;2155:3;2152:17;2144:296;;;2266:2;2262:7;2253:6;2245;2241:19;2237:33;2230:5;2223:48;2298:42;2333:6;2322:8;2316:15;2298:42;:::i;:::-;2383:2;2369:17;;;;2288:52;;-1:-1:-1;2412:14:195;;;;;2188:1;2179:11;2144:296;;;-1:-1:-1;2463:6:195;;-1:-1:-1;;;2504:2:195;2527:12;;;;2492:15;;;;;-1:-1:-1;1528:1:195;1521:9;1492:1057;;;-1:-1:-1;2566:6:195;;950:1628;-1:-1:-1;;;;;;950:1628:195:o;2583:446::-;2635:3;2673:5;2667:12;2700:6;2695:3;2688:19;2732:4;2727:3;2723:14;2716:21;;2771:4;2764:5;2760:16;2794:1;2804:200;2818:6;2815:1;2812:13;2804:200;;;2883:13;;-1:-1:-1;;;;;;2879:40:195;2867:53;;2949:4;2940:14;;;;2977:17;;;;2840:1;2833:9;2804:200;;;-1:-1:-1;3020:3:195;;2583:446;-1:-1:-1;;;;2583:446:195:o;3034:1145::-;3254:4;3302:2;3291:9;3287:18;3332:2;3321:9;3314:21;3355:6;3390;3384:13;3421:6;3413;3406:22;3459:2;3448:9;3444:18;3437:25;;3521:2;3511:6;3508:1;3504:14;3493:9;3489:30;3485:39;3471:53;;3559:2;3551:6;3547:15;3580:1;3590:560;3604:6;3601:1;3598:13;3590:560;;;3697:2;3693:7;3681:9;3673:6;3669:22;3665:36;3660:3;3653:49;3731:6;3725:13;3777:2;3771:9;3808:2;3800:6;3793:18;3838:48;3882:2;3874:6;3870:15;3856:12;3838:48;:::i;:::-;3824:62;;3935:2;3931;3927:11;3921:18;3899:40;;3988:6;3980;3976:19;3971:2;3963:6;3959:15;3952:44;4019:51;4063:6;4047:14;4019:51;:::i;:::-;4009:61;-1:-1:-1;;;4105:2:195;4128:12;;;;4093:15;;;;;3626:1;3619:9;3590:560;;4184:782;4346:4;4394:2;4383:9;4379:18;4424:2;4413:9;4406:21;4447:6;4482;4476:13;4513:6;4505;4498:22;4551:2;4540:9;4536:18;4529:25;;4613:2;4603:6;4600:1;4596:14;4585:9;4581:30;4577:39;4563:53;;4651:2;4643:6;4639:15;4672:1;4682:255;4696:6;4693:1;4690:13;4682:255;;;4789:2;4785:7;4773:9;4765:6;4761:22;4757:36;4752:3;4745:49;4817:40;4850:6;4841;4835:13;4817:40;:::i;:::-;4807:50;-1:-1:-1;4892:2:195;4915:12;;;;4880:15;;;;;4718:1;4711:9;4682:255;;4971:1033;5175:4;5223:2;5212:9;5208:18;5253:2;5242:9;5235:21;5276:6;5311;5305:13;5342:6;5334;5327:22;5380:2;5369:9;5365:18;5358:25;;5442:2;5432:6;5429:1;5425:14;5414:9;5410:30;5406:39;5392:53;;5480:2;5472:6;5468:15;5501:1;5511:464;5525:6;5522:1;5519:13;5511:464;;;5590:22;;;-1:-1:-1;;5586:36:195;5574:49;;5646:13;;5691:9;;-1:-1:-1;;;;;5687:35:195;5672:51;;5770:2;5762:11;;;5756:18;5811:2;5794:15;;;5787:27;;;5756:18;5837:58;;5879:15;;5756:18;5837:58;:::i;:::-;5827:68;-1:-1:-1;;5930:2:195;5953:12;;;;5918:15;;;;;5547:1;5540:9;5511:464;;6201:127;6262:10;6257:3;6253:20;6250:1;6243:31;6293:4;6290:1;6283:15;6317:4;6314:1;6307:15;6333:128;6400:9;;;6421:11;;;6418:37;;;6435:18;;:::i;6466:168::-;6539:9;;;6570;;6587:15;;;6581:22;;6567:37;6557:71;;6608:18;;:::i;6639:125::-;6704:9;;;6725:10;;;6722:36;;;6738:18;;:::i;6769:127::-;6830:10;6825:3;6821:20;6818:1;6811:31;6861:4;6858:1;6851:15;6885:4;6882:1;6875:15;6901:127;6962:10;6957:3;6953:20;6950:1;6943:31;6993:4;6990:1;6983:15;7017:4;7014:1;7007:15;7033:217;7073:1;7099;7089:132;;7143:10;7138:3;7134:20;7131:1;7124:31;7178:4;7175:1;7168:15;7206:4;7203:1;7196:15;7089:132;-1:-1:-1;7235:9:195;;7033:217::o;7255:202::-;-1:-1:-1;;;;;;7417:33:195;;;;7399:52;;7387:2;7372:18;;7255:202::o;7462:380::-;7541:1;7537:12;;;;7584;;;7605:61;;7659:4;7651:6;7647:17;7637:27;;7605:61;7712:2;7704:6;7701:14;7681:18;7678:38;7675:161;;7758:10;7753:3;7749:20;7746:1;7739:31;7793:4;7790:1;7783:15;7821:4;7818:1;7811:15;7675:161;;7462:380;;;:::o;8126:184::-;8196:6;8249:2;8237:9;8228:7;8224:23;8220:32;8217:52;;;8265:1;8262;8255:12;8217:52;-1:-1:-1;8288:16:195;;8126:184;-1:-1:-1;8126:184:195:o", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "testLtvBorrowLAgainstX()": "074b5cb7", "testLtvBorrowLAgainstY()": "0637bb31", "testLtvBorrowLAndXAgainstY()": "a01329e3", "testLtvBorrowLAndYAgainstX()": "c939190d", "testLtvBorrowLeveragedLongX()": "41fe6bba", "testLtvBorrowLeveragedShortX()": "b3ee0dde", "testLtvBorrowTooMuchXAndY()": "ddc09845", "testLtvBorrowXAgainstL()": "a730a803", "testLtvBorrowXAgainstXFails()": "20aaa191", "testLtvBorrowXAgainstXYFails()": "dad0b3f0", "testLtvBorrowXAgainstY()": "a99287fe", "testLtvBorrowXAndLAgainstY()": "bbb157c4", "testLtvBorrowYAgainstL()": "55293f4d", "testLtvBorrowYAgainstX()": "06cbb38f", "testLtvBorrowYAgainstYFails()": "9d067b01", "testLtvBorrowYAndLAgainstX()": "35b54e94", "testLtvBurnLWithXAgainstL()": "2fdda3a8", "testLtvBurnWithXAgainstLAndY()": "60a3a81f", "testLtvBurnWithYAgainstL()": "901176eb", "testLtvBurnWithYAgainstLAndX()": "d49acd58", "testLtvWithdrawXWithLAgainstX()": "2fb446ef", "testLtvWithdrawXWithYAgainstX()": "bccc45f4", "testLtvWithdrawXWithYAgainstXAndL()": "3921d6bd", "testLtvWithdrawYWithLAgainstY()": "54dfda37", "testLtvWithdrawYWithXAgainstY()": "cd5e1fe3", "testLtvWithdrawYWithXAgainstYAndL()": "b6cd9ca8", "testMaxSlippageFails()": "154941ae", "testSlippageBeyondQuadraticFee()": "bc008c98"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"AmmalgamCannotBorrowAgainstSameCollateral\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamDepositIsNotStrictlyBigger\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamLTV\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamMaxSlippage\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamTooMuchLeverage\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientLiquidity\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MissingGteActual\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MissingOutGteReserveOut\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowLAgainstX\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowLAgainstY\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowLAndXAgainstY\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowLAndYAgainstX\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowLeveragedLongX\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowLeveragedShortX\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowTooMuchXAndY\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowXAgainstL\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowXAgainstXFails\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowXAgainstXYFails\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowXAgainstY\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowXAndLAgainstY\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowYAgainstL\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowYAgainstX\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowYAgainstYFails\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBorrowYAndLAgainstX\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBurnLWithXAgainstL\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBurnWithXAgainstLAndY\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBurnWithYAgainstL\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvBurnWithYAgainstLAndX\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvWithdrawXWithLAgainstX\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvWithdrawXWithYAgainstX\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvWithdrawXWithYAgainstXAndL\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvWithdrawYWithLAgainstY\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvWithdrawYWithXAgainstY\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLtvWithdrawYWithXAgainstYAndL\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testMaxSlippageFails\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testSlippageBeyondQuadraticFee\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"testLtvBorrowLAgainstX()\":{\"details\":\"When testing borrowing L, we want to think about the x and y that makes up as l individually so that we can   take the net x which would be the collateral x less the x portion of the L and then compare that to the   borrowed y portion of the l. Below we show the math used to set up this test     l = sqrt(lx * ly),     p = reserveX/reserveY and sp is the slippage price,     (x - lx) * ltv / sp = ly, and     lx = p * ly   Using this we can pick an amount of X to be used as collateral and then find the price by determining the   amount of slippage to sell the X and then we can find the amount of y associated with l that could be borrowed\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"testLtvBorrowLAgainstX()\":{\"notice\":\"Test that the max borrow is correct when borrowing L against X.\"},\"testLtvBorrowXAgainstXFails()\":{\"notice\":\"If after closing a deposit a user still had a small amount of   liquidity left, could they later put in a different collateral   and borrow x?\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/PairTests/LoanToValueTests.sol\":\"LoanToValueTests\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/AmmalgamPair.sol\":{\"keccak256\":\"0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4\",\"urls\":[\"bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2\",\"dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS\"]},\"contracts/SaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76\",\"dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE\"]},\"contracts/factories/AmmalgamFactory.sol\":{\"keccak256\":\"0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b\",\"dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa\"]},\"contracts/factories/ERC20DebtLiquidityTokenFactory.sol\":{\"keccak256\":\"0x72e3ada6a2f0792a353b730c1b45ae832f9ce2f58f0bda039383f8890cb2a4f7\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4949e7b66647313aaba2e11d7edde06eb87345b476c1a20f890659c1af827b2b\",\"dweb:/ipfs/Qmf3emVXfGp1oc8iVYxnVqpJ88vnxxdj7WqPm1vzVKb1SD\"]},\"contracts/factories/ERC20LiquidityTokenFactory.sol\":{\"keccak256\":\"0x762974ca1ed600e0930a92bd2eb3a1a5f9ef0469ab2e6e811e4674e098238762\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5fd5f33537aeea9bac1f18c6fca2057899ec5f90cb8c756622eb436d5b13e27e\",\"dweb:/ipfs/QmfYznzzwN1AmdnuzNKe1R6t8UeztaZVGuzJ8vKfzjMXYN\"]},\"contracts/factories/ERC4626DebtTokenFactory.sol\":{\"keccak256\":\"0x7deeb7a40d26bc790112f29836da83050fa3554e471e1dce4dda6bf29ab9bf67\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5a46a4c8270e0b8a731259328b6c35c84de270a14f2f69ba04bc58d18400efc6\",\"dweb:/ipfs/QmQ56QbX6S9GjQinsFYtTMns6HgpcTXW1wnvQT6QgiuW1Z\"]},\"contracts/factories/ERC4626DepositTokenFactory.sol\":{\"keccak256\":\"0xf84b75119f2680f8079bb9567b0c03c0ad49b71a8c00f968d03d5fca2a954035\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c3fc7a9e300a935991746d5be835418b09e6d2b20b65e3e297d4faf28516469b\",\"dweb:/ipfs/QmQMr9MA5a3UcZCiP3e2haYqzBsbE8Pe6rDq6j6RJ3ub4Z\"]},\"contracts/factories/NewTokensFactory.sol\":{\"keccak256\":\"0x86cd420e1df8a59b11a4ab53a16971a44953f0a07741ef69d95baa4bd60126ac\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://d8cdd98060f059705b9ae2b64ab3e74395c0f3a24e12f5ac11ca7e509c6a7aa0\",\"dweb:/ipfs/QmahgKkRzuWHpQ73DHGZ4Kvd2MQG7MpfPShayJDRJQYSVr\"]},\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/ISaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20\",\"dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR\"]},\"contracts/interfaces/callbacks/IAmmalgamCallee.sol\":{\"keccak256\":\"0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d\",\"dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/factories/IAmmalgamFactory.sol\":{\"keccak256\":\"0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628\",\"dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9\"]},\"contracts/interfaces/factories/IFactoryCallback.sol\":{\"keccak256\":\"0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b\",\"dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT\"]},\"contracts/interfaces/factories/INewTokensFactory.sol\":{\"keccak256\":\"0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b\",\"dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E\"]},\"contracts/interfaces/factories/ITokenFactory.sol\":{\"keccak256\":\"0xac23e5c0441599add526b0c308faa7787f90bf01603b6dbc231944c166ca32d6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://ac574b98b2c1034786581137a218277ec58e06e9612f76814f34960383083626\",\"dweb:/ipfs/QmZgZqVnshjzuHBXJTR9g87S15CyLwJUSErGEDoJpBd4kg\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/IERC20DebtToken.sol\":{\"keccak256\":\"0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696\",\"dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b\"]},\"contracts/interfaces/tokens/IPluginRegistry.sol\":{\"keccak256\":\"0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d\",\"dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/GeometricTWAP.sol\":{\"keccak256\":\"0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa\",\"dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz\"]},\"contracts/libraries/Interest.sol\":{\"keccak256\":\"0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194\",\"dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX\"]},\"contracts/libraries/Liquidation.sol\":{\"keccak256\":\"0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877\",\"dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/Saturation.sol\":{\"keccak256\":\"0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20\",\"dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/TokenSymbol.sol\":{\"keccak256\":\"0x628df064fdbdacfe6783964d7bf38cdf1b34e1ad07caa3cea39bf7468cc19b43\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://da6823ce0debaabe20f25281e81a4fc88de98d4df2942a5e276826ac381c227b\",\"dweb:/ipfs/QmNpEuQ25788xfcJwPk2xUB7fyP7fW5ENK2e9qgRqp1BcH\"]},\"contracts/libraries/Uint16Set.sol\":{\"keccak256\":\"0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06\",\"dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/ERC20Base.sol\":{\"keccak256\":\"0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59\",\"dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL\"]},\"contracts/tokens/ERC20DebtBase.sol\":{\"keccak256\":\"0xc0a59cd54fcd847b160d662aa45a5fe7d24ed90c8030fe17fd5f9def427ed19a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://365c7f18505af36b2806404b1b3f2d897de6ac18e255ecfbb4ccc491cac7e444\",\"dweb:/ipfs/QmUqx8EBwRb6W1YQPb9MjwAhEEHNpZTCopbGWb1vbyuUpp\"]},\"contracts/tokens/ERC20DebtLiquidityToken.sol\":{\"keccak256\":\"0xf222ad5562ed41d74b0cfb5b4aad84ec9f4cb91b6d71928b30b018bab494efe8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a8e8f3e7ded2eae04c63ce3ae7a86c051d48d6db697cb6929d7064a4ec9d7371\",\"dweb:/ipfs/QmU3EuwHU3xB1e6MxaRjSRJcDMK73wfZig9uGWqZPaHnTn\"]},\"contracts/tokens/ERC20LiquidityToken.sol\":{\"keccak256\":\"0x2bb2429e551c031034c747749373d2e4c451580e9b203b689d6eaf03ad896358\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9ad5902756073578beee9068b74bd921e59a36b803cf34ef01570c670363689e\",\"dweb:/ipfs/QmTkT5K2XcB3ZbPDqd4ZAfnZMp2reCzu3Pv7JpRqhAtZHP\"]},\"contracts/tokens/ERC4626DebtToken.sol\":{\"keccak256\":\"0xe69b1ed2fb7b2d7c24c6838462001988b8e51795d215cfa74b9874d17257509e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c4f201e5f5621689046c58863ab9270cf770c68810d52269d1fc2ac93a7ccf96\",\"dweb:/ipfs/QmdtALf6LQdHhce3HsNdVtomQu8e5F5QcYU6S7H1PeBThZ\"]},\"contracts/tokens/ERC4626DepositToken.sol\":{\"keccak256\":\"0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac\",\"dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM\"]},\"contracts/tokens/PluginRegistry.sol\":{\"keccak256\":\"0x9263d71fc32da7d0ca4f8d272f8d75d565c1f06281952481322983bae9d7b488\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c9dcbc64172f4339547865b4f041826f0de5d464900f316edbe72e7d6bfb396d\",\"dweb:/ipfs/QmQykSWuY8xLJotWUPgG5JQDS5DmA2E5Hjb4c6Bz4YnbBQ\"]},\"contracts/tokens/TokenController.sol\":{\"keccak256\":\"0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159\",\"dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn\"]},\"contracts/utils/deployHelper.sol\":{\"keccak256\":\"0x9b9dd84e234bb2ffbf51da7e9ab42fe7b6329acf38de7f042d4f8abd146182f0\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://d07ded7de8e48a25ac7b0442c6e455338bd16ee483a89ad7f37585ab91865a3b\",\"dweb:/ipfs/QmeBAuZgRJEXeuX6qsGt46sTLovKNC5Pue8xFxbHMPtiBR\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol\":{\"keccak256\":\"0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22\",\"dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol\":{\"keccak256\":\"0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368\",\"dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB\"]},\"lib/1inch/token-plugins/contracts/ERC20Hooks.sol\":{\"keccak256\":\"0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5\",\"dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c\"]},\"lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol\":{\"keccak256\":\"0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8\",\"dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh\"]},\"lib/1inch/token-plugins/contracts/interfaces/IHook.sol\":{\"keccak256\":\"0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d\",\"dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS\"]},\"lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol\":{\"keccak256\":\"0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0\",\"dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z\"]},\"lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol\":{\"keccak256\":\"0x7d9d432e8f02168bf3f790e3dabcf36402782acf7ffa476cabe86fc4d8962eb2\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://1adc13e7f399f500ea5f81480ad149a50408fde7990a2c6347e6377486f389dc\",\"dweb:/ipfs/QmSvm5TUBJqknsqNJLLHqNS4MLSH5k3vNrbquVg6ZKSfx9\"]},\"lib/mangrove-core/lib/core/BitLib.sol\":{\"keccak256\":\"0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8\",\"dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr\"]},\"lib/morpho-blue/src/libraries/MathLib.sol\":{\"keccak256\":\"0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08\",\"dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd\"]},\"lib/openzeppelin-contracts/contracts/access/AccessControl.sol\":{\"keccak256\":\"0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d\",\"dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv\"]},\"lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e\",\"dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol\":{\"keccak256\":\"0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38\",\"dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol\":{\"keccak256\":\"0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4\",\"dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol\":{\"keccak256\":\"0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007\",\"dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol\":{\"keccak256\":\"0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819\",\"dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol\":{\"keccak256\":\"0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e\",\"dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215\",\"dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol\":{\"keccak256\":\"0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051\",\"dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol\":{\"keccak256\":\"0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78\",\"dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318\",\"dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79\",\"dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"keccak256\":\"0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26\",\"dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol\":{\"keccak256\":\"0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9\",\"dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol\":{\"keccak256\":\"0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834\",\"dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol\":{\"keccak256\":\"0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92\",\"dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol\":{\"keccak256\":\"0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896\",\"dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Nonces.sol\":{\"keccak256\":\"0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e\",\"dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/Pausable.sol\":{\"keccak256\":\"0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c\",\"dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8\"]},\"lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol\":{\"keccak256\":\"0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35\",\"dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211\",\"dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4\",\"dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol\":{\"keccak256\":\"0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f\",\"dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59\",\"dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4\",\"dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]},\"lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol\":{\"keccak256\":\"0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b\",\"dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr\"]},\"lib/openzeppelin-contracts/contracts/utils/types/Time.sol\":{\"keccak256\":\"0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6\",\"dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Base.sol\":{\"keccak256\":\"0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224\",\"dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1\",\"dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e\",\"dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c\",\"dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"test/InterestTests/InterestFixture.sol\":{\"keccak256\":\"0x458f1f72b1417a73ecdea81c25b269592e95c1808ca6aaa6b60a25243e143ed3\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://abeb9b791b75f44d48f898182c673d80ea1c0f513fffe48a6834fdebebc6fdbe\",\"dweb:/ipfs/QmU92joERfyZJaTonAknmtRBkTjs5Jb7S2zM8Zk1XAnhwj\"]},\"test/PairTests/LoanToValueTests.sol\":{\"keccak256\":\"0x5984c38ab8d1c04d75f7d6ed655ec1767c764a6c301ca41ae78ee00be04b0502\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://e4259a71d00590a01e59b528c1929b5335e07d62fff545c6f6ab0e3bab2689e0\",\"dweb:/ipfs/QmTkX7KPYUxujeJ3NqzF2wn2B4UaVicndbSmSTzXucGgXY\"]},\"test/example/PeripheralDelegationContractExample.sol\":{\"keccak256\":\"0xf212fd0b2dd3a358c826623bd320e3aa0630892b9f6ba777b8126d3e2cfcfb14\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://2ad79b2d7eb1b46c69383b9e9090bf2031ff779e46637d850b881db3dc84d797\",\"dweb:/ipfs/QmTPx8qw1zdYXRzjBnmuzMCt8yiErwFiLBk47xnbTm1erP\"]},\"test/shared/FactoryPairTestFixture.sol\":{\"keccak256\":\"0x62487d7b3402461a61bd0c99be82302c8c1a94533c525a0ff6bcfe888af730a5\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://dfd509aaec3469ed23d1cda8c6f603b7f0163fc29ec4ba6e4b06b60ca8fdc042\",\"dweb:/ipfs/QmTPDPM7kt77VNWr61MVZGmNZp67RG8jKzdmz7zwWep4GE\"]},\"test/shared/StubErc20.sol\":{\"keccak256\":\"0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918\",\"dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn\"]},\"test/shared/utilities.sol\":{\"keccak256\":\"0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416\",\"dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG\"]},\"test/utils/DepletedAssetUtils.sol\":{\"keccak256\":\"0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e\",\"dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR\"]},\"test/utils/constants.sol\":{\"keccak256\":\"0xe7d13ea4f26a2c43b7beed68c83a0e36555ead8f6bfd181430c74f853546fc34\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5098f47b615afa3d6489c2c8c2576f6202601fb15b1f32e6900639986e44f1fd\",\"dweb:/ipfs/QmPU1Ejtv4yY7eqjW1SpVgvS8vMqwyEjMeNGCLax3Mwk9d\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "AmmalgamCannotBorrowAgainstSameCollateral"}, {"inputs": [], "type": "error", "name": "AmmalgamDepositIsNotStrictlyBigger"}, {"inputs": [], "type": "error", "name": "AmmalgamLTV"}, {"inputs": [], "type": "error", "name": "AmmalgamMaxSlippage"}, {"inputs": [], "type": "error", "name": "AmmalgamTooMuchLeverage"}, {"inputs": [], "type": "error", "name": "InsufficientLiquidity"}, {"inputs": [], "type": "error", "name": "MissingGteActual"}, {"inputs": [], "type": "error", "name": "MissingOutGteReserveOut"}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowLAgainstX"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowLAgainstY"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowLAndXAgainstY"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowLAndYAgainstX"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowLeveragedLongX"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowLeveragedShortX"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowTooMuchXAndY"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowXAgainstL"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowXAgainstXFails"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowXAgainstXYFails"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowXAgainstY"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowXAndLAgainstY"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowYAgainstL"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowYAgainstX"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowYAgainstYFails"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBorrowYAndLAgainstX"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBurnLWithXAgainstL"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBurnWithXAgainstLAndY"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBurnWithYAgainstL"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvBurnWithYAgainstLAndX"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvWithdrawXWithLAgainstX"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvWithdrawXWithYAgainstX"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvWithdrawXWithYAgainstXAndL"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvWithdrawYWithLAgainstY"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvWithdrawYWithXAgainstY"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLtvWithdrawYWithXAgainstYAndL"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testMaxSlippageFails"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testSlippageBeyondQuadraticFee"}], "devdoc": {"kind": "dev", "methods": {"testLtvBorrowLAgainstX()": {"details": "When testing borrowing L, we want to think about the x and y that makes up as l individually so that we can   take the net x which would be the collateral x less the x portion of the L and then compare that to the   borrowed y portion of the l. Below we show the math used to set up this test     l = sqrt(lx * ly),     p = reserveX/reserveY and sp is the slippage price,     (x - lx) * ltv / sp = ly, and     lx = p * ly   Using this we can pick an amount of X to be used as collateral and then find the price by determining the   amount of slippage to sell the X and then we can find the amount of y associated with l that could be borrowed"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"testLtvBorrowLAgainstX()": {"notice": "Test that the max borrow is correct when borrowing L against X."}, "testLtvBorrowXAgainstXFails()": {"notice": "If after closing a deposit a user still had a small amount of   liquidity left, could they later put in a different collateral   and borrow x?"}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/PairTests/LoanToValueTests.sol": "LoanToValueTests"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/AmmalgamPair.sol": {"keccak256": "0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4", "urls": ["bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2", "dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS"], "license": null}, "contracts/SaturationAndGeometricTWAPState.sol": {"keccak256": "0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419", "urls": ["bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76", "dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE"], "license": "GPL-3.0-only"}, "contracts/factories/AmmalgamFactory.sol": {"keccak256": "0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e", "urls": ["bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b", "dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa"], "license": "GPL-3.0-only"}, "contracts/factories/ERC20DebtLiquidityTokenFactory.sol": {"keccak256": "0x72e3ada6a2f0792a353b730c1b45ae832f9ce2f58f0bda039383f8890cb2a4f7", "urls": ["bzz-raw://4949e7b66647313aaba2e11d7edde06eb87345b476c1a20f890659c1af827b2b", "dweb:/ipfs/Qmf3emVXfGp1oc8iVYxnVqpJ88vnxxdj7WqPm1vzVKb1SD"], "license": "GPL-3.0-only"}, "contracts/factories/ERC20LiquidityTokenFactory.sol": {"keccak256": "0x762974ca1ed600e0930a92bd2eb3a1a5f9ef0469ab2e6e811e4674e098238762", "urls": ["bzz-raw://5fd5f33537aeea9bac1f18c6fca2057899ec5f90cb8c756622eb436d5b13e27e", "dweb:/ipfs/QmfYznzzwN1AmdnuzNKe1R6t8UeztaZVGuzJ8vKfzjMXYN"], "license": "GPL-3.0-only"}, "contracts/factories/ERC4626DebtTokenFactory.sol": {"keccak256": "0x7deeb7a40d26bc790112f29836da83050fa3554e471e1dce4dda6bf29ab9bf67", "urls": ["bzz-raw://5a46a4c8270e0b8a731259328b6c35c84de270a14f2f69ba04bc58d18400efc6", "dweb:/ipfs/QmQ56QbX6S9GjQinsFYtTMns6HgpcTXW1wnvQT6QgiuW1Z"], "license": "GPL-3.0-only"}, "contracts/factories/ERC4626DepositTokenFactory.sol": {"keccak256": "0xf84b75119f2680f8079bb9567b0c03c0ad49b71a8c00f968d03d5fca2a954035", "urls": ["bzz-raw://c3fc7a9e300a935991746d5be835418b09e6d2b20b65e3e297d4faf28516469b", "dweb:/ipfs/QmQMr9MA5a3UcZCiP3e2haYqzBsbE8Pe6rDq6j6RJ3ub4Z"], "license": "GPL-3.0-only"}, "contracts/factories/NewTokensFactory.sol": {"keccak256": "0x86cd420e1df8a59b11a4ab53a16971a44953f0a07741ef69d95baa4bd60126ac", "urls": ["bzz-raw://d8cdd98060f059705b9ae2b64ab3e74395c0f3a24e12f5ac11ca7e509c6a7aa0", "dweb:/ipfs/QmahgKkRzuWHpQ73DHGZ4Kvd2MQG7MpfPShayJDRJQYSVr"], "license": "GPL-3.0-only"}, "contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/ISaturationAndGeometricTWAPState.sol": {"keccak256": "0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c", "urls": ["bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20", "dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/IAmmalgamCallee.sol": {"keccak256": "0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339", "urls": ["bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d", "dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IAmmalgamFactory.sol": {"keccak256": "0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8", "urls": ["bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628", "dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IFactoryCallback.sol": {"keccak256": "0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52", "urls": ["bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b", "dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/INewTokensFactory.sol": {"keccak256": "0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9", "urls": ["bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b", "dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/ITokenFactory.sol": {"keccak256": "0xac23e5c0441599add526b0c308faa7787f90bf01603b6dbc231944c166ca32d6", "urls": ["bzz-raw://ac574b98b2c1034786581137a218277ec58e06e9612f76814f34960383083626", "dweb:/ipfs/QmZgZqVnshjzuHBXJTR9g87S15CyLwJUSErGEDoJpBd4kg"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IERC20DebtToken.sol": {"keccak256": "0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a", "urls": ["bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696", "dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IPluginRegistry.sol": {"keccak256": "0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf", "urls": ["bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d", "dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X"], "license": "MIT"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/GeometricTWAP.sol": {"keccak256": "0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e", "urls": ["bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa", "dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz"], "license": "GPL-3.0-only"}, "contracts/libraries/Interest.sol": {"keccak256": "0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646", "urls": ["bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194", "dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX"], "license": "GPL-3.0-only"}, "contracts/libraries/Liquidation.sol": {"keccak256": "0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63", "urls": ["bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877", "dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/Saturation.sol": {"keccak256": "0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860", "urls": ["bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20", "dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/TokenSymbol.sol": {"keccak256": "0x628df064fdbdacfe6783964d7bf38cdf1b34e1ad07caa3cea39bf7468cc19b43", "urls": ["bzz-raw://da6823ce0debaabe20f25281e81a4fc88de98d4df2942a5e276826ac381c227b", "dweb:/ipfs/QmNpEuQ25788xfcJwPk2xUB7fyP7fW5ENK2e9qgRqp1BcH"], "license": "GPL-3.0-only"}, "contracts/libraries/Uint16Set.sol": {"keccak256": "0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f", "urls": ["bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06", "dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy"], "license": "GPL-3.0-only"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/ERC20Base.sol": {"keccak256": "0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b", "urls": ["bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59", "dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL"], "license": "MIT"}, "contracts/tokens/ERC20DebtBase.sol": {"keccak256": "0xc0a59cd54fcd847b160d662aa45a5fe7d24ed90c8030fe17fd5f9def427ed19a", "urls": ["bzz-raw://365c7f18505af36b2806404b1b3f2d897de6ac18e255ecfbb4ccc491cac7e444", "dweb:/ipfs/QmUqx8EBwRb6W1YQPb9MjwAhEEHNpZTCopbGWb1vbyuUpp"], "license": "MIT"}, "contracts/tokens/ERC20DebtLiquidityToken.sol": {"keccak256": "0xf222ad5562ed41d74b0cfb5b4aad84ec9f4cb91b6d71928b30b018bab494efe8", "urls": ["bzz-raw://a8e8f3e7ded2eae04c63ce3ae7a86c051d48d6db697cb6929d7064a4ec9d7371", "dweb:/ipfs/QmU3EuwHU3xB1e6MxaRjSRJcDMK73wfZig9uGWqZPaHnTn"], "license": "MIT"}, "contracts/tokens/ERC20LiquidityToken.sol": {"keccak256": "0x2bb2429e551c031034c747749373d2e4c451580e9b203b689d6eaf03ad896358", "urls": ["bzz-raw://9ad5902756073578beee9068b74bd921e59a36b803cf34ef01570c670363689e", "dweb:/ipfs/QmTkT5K2XcB3ZbPDqd4ZAfnZMp2reCzu3Pv7JpRqhAtZHP"], "license": "MIT"}, "contracts/tokens/ERC4626DebtToken.sol": {"keccak256": "0xe69b1ed2fb7b2d7c24c6838462001988b8e51795d215cfa74b9874d17257509e", "urls": ["bzz-raw://c4f201e5f5621689046c58863ab9270cf770c68810d52269d1fc2ac93a7ccf96", "dweb:/ipfs/QmdtALf6LQdHhce3HsNdVtomQu8e5F5QcYU6S7H1PeBThZ"], "license": "MIT"}, "contracts/tokens/ERC4626DepositToken.sol": {"keccak256": "0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be", "urls": ["bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac", "dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM"], "license": "MIT"}, "contracts/tokens/PluginRegistry.sol": {"keccak256": "0x9263d71fc32da7d0ca4f8d272f8d75d565c1f06281952481322983bae9d7b488", "urls": ["bzz-raw://c9dcbc64172f4339547865b4f041826f0de5d464900f316edbe72e7d6bfb396d", "dweb:/ipfs/QmQykSWuY8xLJotWUPgG5JQDS5DmA2E5Hjb4c6Bz4YnbBQ"], "license": "MIT"}, "contracts/tokens/TokenController.sol": {"keccak256": "0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6", "urls": ["bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159", "dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn"], "license": "GPL-3.0-only"}, "contracts/utils/deployHelper.sol": {"keccak256": "0x9b9dd84e234bb2ffbf51da7e9ab42fe7b6329acf38de7f042d4f8abd146182f0", "urls": ["bzz-raw://d07ded7de8e48a25ac7b0442c6e455338bd16ee483a89ad7f37585ab91865a3b", "dweb:/ipfs/QmeBAuZgRJEXeuX6qsGt46sTLovKNC5Pue8xFxbHMPtiBR"], "license": "GPL-3.0-only"}, "lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol": {"keccak256": "0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e", "urls": ["bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22", "dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9"], "license": "MIT"}, "lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol": {"keccak256": "0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318", "urls": ["bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368", "dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/ERC20Hooks.sol": {"keccak256": "0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2", "urls": ["bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5", "dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol": {"keccak256": "0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875", "urls": ["bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8", "dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IHook.sol": {"keccak256": "0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80", "urls": ["bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d", "dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol": {"keccak256": "0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c", "urls": ["bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0", "dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z"], "license": "MIT"}, "lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol": {"keccak256": "0x7d9d432e8f02168bf3f790e3dabcf36402782acf7ffa476cabe86fc4d8962eb2", "urls": ["bzz-raw://1adc13e7f399f500ea5f81480ad149a50408fde7990a2c6347e6377486f389dc", "dweb:/ipfs/QmSvm5TUBJqknsqNJLLHqNS4MLSH5k3vNrbquVg6ZKSfx9"], "license": "MIT OR Apache-2.0"}, "lib/mangrove-core/lib/core/BitLib.sol": {"keccak256": "0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3", "urls": ["bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8", "dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr"], "license": "MIT"}, "lib/morpho-blue/src/libraries/MathLib.sol": {"keccak256": "0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a", "urls": ["bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08", "dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd"], "license": "GPL-2.0-or-later"}, "lib/openzeppelin-contracts/contracts/access/AccessControl.sol": {"keccak256": "0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2", "urls": ["bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d", "dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1", "urls": ["bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e", "dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol": {"keccak256": "0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548", "urls": ["bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38", "dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol": {"keccak256": "0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47", "urls": ["bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4", "dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol": {"keccak256": "0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d", "urls": ["bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007", "dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol": {"keccak256": "0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73", "urls": ["bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819", "dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol": {"keccak256": "0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541", "urls": ["bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e", "dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol": {"keccak256": "0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad", "urls": ["bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215", "dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol": {"keccak256": "0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe", "urls": ["bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051", "dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol": {"keccak256": "0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8", "urls": ["bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78", "dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9", "urls": ["bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318", "dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db", "urls": ["bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79", "dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"keccak256": "0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073", "urls": ["bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26", "dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol": {"keccak256": "0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74", "urls": ["bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9", "dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol": {"keccak256": "0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8", "urls": ["bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834", "dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol": {"keccak256": "0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5", "urls": ["bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92", "dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol": {"keccak256": "0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63", "urls": ["bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896", "dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Nonces.sol": {"keccak256": "0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f", "urls": ["bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e", "dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Pausable.sol": {"keccak256": "0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f", "urls": ["bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c", "dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol": {"keccak256": "0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402", "urls": ["bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35", "dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52", "urls": ["bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211", "dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5", "urls": ["bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4", "dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol": {"keccak256": "0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e", "urls": ["bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f", "dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631", "urls": ["bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59", "dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol": {"keccak256": "0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f", "urls": ["bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4", "dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol": {"keccak256": "0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a", "urls": ["bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b", "dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/types/Time.sol": {"keccak256": "0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc", "urls": ["bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6", "dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Base.sol": {"keccak256": "0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c", "urls": ["bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224", "dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol": {"keccak256": "0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b", "urls": ["bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1", "dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Test.sol": {"keccak256": "0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe", "urls": ["bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e", "dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol": {"keccak256": "0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354", "urls": ["bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c", "dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC"], "license": "MIT OR Apache-2.0"}, "lib/openzeppelin-contracts/lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "test/InterestTests/InterestFixture.sol": {"keccak256": "0x458f1f72b1417a73ecdea81c25b269592e95c1808ca6aaa6b60a25243e143ed3", "urls": ["bzz-raw://abeb9b791b75f44d48f898182c673d80ea1c0f513fffe48a6834fdebebc6fdbe", "dweb:/ipfs/QmU92joERfyZJaTonAknmtRBkTjs5Jb7S2zM8Zk1XAnhwj"], "license": "GPL-3.0-only"}, "test/PairTests/LoanToValueTests.sol": {"keccak256": "0x5984c38ab8d1c04d75f7d6ed655ec1767c764a6c301ca41ae78ee00be04b0502", "urls": ["bzz-raw://e4259a71d00590a01e59b528c1929b5335e07d62fff545c6f6ab0e3bab2689e0", "dweb:/ipfs/QmTkX7KPYUxujeJ3NqzF2wn2B4UaVicndbSmSTzXucGgXY"], "license": "GPL-3.0-only"}, "test/example/PeripheralDelegationContractExample.sol": {"keccak256": "0xf212fd0b2dd3a358c826623bd320e3aa0630892b9f6ba777b8126d3e2cfcfb14", "urls": ["bzz-raw://2ad79b2d7eb1b46c69383b9e9090bf2031ff779e46637d850b881db3dc84d797", "dweb:/ipfs/QmTPx8qw1zdYXRzjBnmuzMCt8yiErwFiLBk47xnbTm1erP"], "license": "GPL-3.0-only"}, "test/shared/FactoryPairTestFixture.sol": {"keccak256": "0x62487d7b3402461a61bd0c99be82302c8c1a94533c525a0ff6bcfe888af730a5", "urls": ["bzz-raw://dfd509aaec3469ed23d1cda8c6f603b7f0163fc29ec4ba6e4b06b60ca8fdc042", "dweb:/ipfs/QmTPDPM7kt77VNWr61MVZGmNZp67RG8jKzdmz7zwWep4GE"], "license": "GPL-3.0-only"}, "test/shared/StubErc20.sol": {"keccak256": "0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee", "urls": ["bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918", "dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn"], "license": "MIT"}, "test/shared/utilities.sol": {"keccak256": "0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8", "urls": ["bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416", "dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG"], "license": "GPL-3.0-only"}, "test/utils/DepletedAssetUtils.sol": {"keccak256": "0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42", "urls": ["bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e", "dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR"], "license": "GPL-3.0-only"}, "test/utils/constants.sol": {"keccak256": "0xe7d13ea4f26a2c43b7beed68c83a0e36555ead8f6bfd181430c74f853546fc34", "urls": ["bzz-raw://5098f47b615afa3d6489c2c8c2576f6202601fb15b1f32e6900639986e44f1fd", "dweb:/ipfs/QmPU1Ejtv4yY7eqjW1SpVgvS8vMqwyEjMeNGCLax3Mwk9d"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 152}