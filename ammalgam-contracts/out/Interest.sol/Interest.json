{"abi": [{"type": "function", "name": "accrueInterestWithAssets", "inputs": [{"name": "assets", "type": "uint128[6]", "internalType": "uint128[6]"}, {"name": "params", "type": "tuple", "internalType": "struct Interest.AccrueInterestParams", "components": [{"name": "duration", "type": "uint256", "internalType": "uint256"}, {"name": "lendingStateTick", "type": "int16", "internalType": "int16"}, {"name": "adjustedActiveLiquidity", "type": "uint256", "internalType": "uint256"}, {"name": "shares", "type": "uint112[6]", "internalType": "uint112[6]"}, {"name": "satPercentageInWads", "type": "uint256", "internalType": "uint256"}]}], "outputs": [{"name": "newAssets", "type": "uint128[6]", "internalType": "uint128[6]"}, {"name": "interestXPortionForLP", "type": "uint256", "internalType": "uint256"}, {"name": "interestYPortionForLP", "type": "uint256", "internalType": "uint256"}, {"name": "protocolFeeAssets", "type": "uint256[3]", "internalType": "uint256[3]"}], "stateMutability": "pure"}, {"type": "event", "name": "InterestAccrued", "inputs": [{"name": "depositLAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "depositXAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "depositYAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowLAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowXAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowYAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "error", "name": "TickOutOfBounds", "inputs": []}], "bytecode": {"object": "0x611053610034600b8282823980515f1a607314602857634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040526004361061003f575f3560e01c806310c333d1146100435780632c6024911461007a575b5f5ffd5b81801561004e575f5ffd5b5061006261005d366004610ded565b61009d565b60405161007193929190610e41565b60405180910390f35b61008d610088366004610e5c565b610236565b6040516100719493929190610edf565b5f5f6100a7610c89565b83511561022f576100b6610ca7565b6040805160c081019182905261011c918890600690825f855b82829054906101000a90046001600160801b03166001600160801b031681526020019060100190602082600f010492830192600103820291508084116100cf579050505050505086610236565b9196509450925090505f5b600681101561018d5781816006811061014257610142610f2d565b602002015187826006811061015957610159610f2d565b6002810490910180546001600160801b0393841660106001948516026101000a908102940219169290921790915501610127565b507f2da4667430fbf283ff4a93cac6c91c4e96bdce03518e72498086d1fa0c789586815f602002015182600160200201518360026020020151846003602002015185600460200201518660056020020151604051610225969594939291906001600160801b0396871681529486166020860152928516604085015290841660608401528316608083015290911660a082015260c00190565b60405180910390a1505b9250925092565b61023e610ca7565b5f5f610248610c89565b85610251610c89565b606082015182515f918291829161027f9161026c9190610f55565b6001600160801b03168b60200151610586565b915091505f6102948684848e608001516105df565b905060405180606001604052806102e38d5f0151845f600381106102ba576102ba610f2d565b602002015160608b01516001600160801b03168b5f5b60200201516001600160801b0316610722565b6102ee906005610f74565b81528c5160208481015160808b0151919093019261031892916001600160801b03168b60016102d0565b81528c51604084015160a08a01516020909301926103439291906001600160801b03168b60026102d0565b8152509450506040518060600160405280610378865f6003811061036957610369610f2d565b6020020151600a60645f610747565b8152602001610388866001610369565b8152602001610398866002610369565b9052604080516060808201835283518851038252602080850151818a0151039083015283830151978301519790970381830152958701518751918d01519298505f926103f892919091036001600160801b031690600160801b9084610747565b905061042086600360200201516001600160801b031682600160801b03600160801b5f610747565b9350610448856001602002015184808960015b60200201516001600160801b0316015f610747565b985061045f85600260200201518380896002610433565b97505f925050505b60058111610579575f80808061047e600386610f9f565b90508461048d578593506104db565b6001850361049d578a92506104db565b600285036104ad578992506104db565b60028511156104db578881600381106104c8576104c8610f2d565b60200201519150600385036104db578593505b61053b848987600681106104f1576104f1610f2d565b60200201516001600160801b03166105099190610fb2565b83858a856003811061051d5761051d610f2d565b602002015161052c9190610fb2565b6105369190610fc5565b61077b565b8c866006811061054d5761054d610f2d565b6001600160801b039092166020929092020152508392506105719150829050610fd8565b915050610467565b5050505092959194509250565b5f5f5f61059284610785565b90505f6105b0826901008040201008040201600160481b6001610747565b90506105c28683600160481b5f610747565b93506105d486600160481b835f610747565b925050509250929050565b6105e7610c89565b84516060860151602087015160808801515f9384936001600160801b039182169390821692838503929182169116111561063f5760208a015160808b015161063c916001600160801b0391031682028a61081c565b94505b60408a015160a08b01516001600160801b03918216911611156106805760408a015160a08b015161067d916001600160801b0391031682028961081c565b93505b50505060405180606001604052806106c16106bb61069e868661084f565b60608c01518c516001600160801b03918216929092019116610861565b8761087c565b81526020016106f389600460200201516001600160801b0316898b60015b60200201516001600160801b031601610861565b815260200161071589600560200201516001600160801b0316888b60026106df565b9052979650505050505050565b5f5f61072d85610917565b905061073b868585846109c9565b9150505b949350505050565b5f6107528486610f74565b905081610768576107638382610ff0565b610772565b610772818461081c565b95945050505050565b8181015b92915050565b5f614d8e19600183900b12806107a05750600182900b614d8e125b156107be57604051633e1f710360e21b815260040160405180910390fd5b600182900b5f8082126107d157816107da565b6107da82611003565b905060386107e782610a02565b901c92508360010b5f1215610815576108128371ffffffffffffffffffffffffffffffffffff610ff0565b92505b5050919050565b5f815f0361082e5761082e6012610bd7565b81600184038161084057610840610f8b565b04600101831515029392505050565b5f8282188284110282185b9392505050565b5f811561077f5761085a670de0b6b3a764000084028361081c565b5f670bcbce7f1b150000821161089357508161077f565b670d2f13f7789f000082106108b15750670c7d713b49da000061077f565b5f6108e86108c784670d2f13f7789f0000610fb2565b6108d986670c7d713b49da0000610fb2565b67016345785d8a00005f610747565b6108fa90670c7d713b49da0000610fb2565b905061073f610909828661084f565b670c7d713b49da0000610be8565b5f670b1a2bc2ec5000008211610940576109398267016345785d8a0000610bf7565b90506109bb565b670cd64299613c800082116109865767011c37937e08000061097c671bc16d674ec80000610976670b1a2bc2ec50000086610fb2565b90610bf7565b6109399190610fc5565b670494654067e100006109ae6801158e460913d00000610976670cd64299613c800086610fb2565b6109b89190610fc5565b90505b61077f6301e1338082610ff0565b5f6107726109ea6109da8488610c0b565b86670de0b6b3a76400005f610747565b6109f4858761084f565b6001600160801b0303610be8565b5f816001165f03610a1757600160801b610a1e565b6101ff60771b5b70ffffffffffffffffffffffffffffffffff1690506002821615610a48576203fc01606e1b0260801c5b6004821615610a5f57640fe017f801605c1b0260801c5b6008821615610a7e576ffc06f9045e406ff001000000000000000260801c5b6010821615610a9d576ff81dba7137fcc6d22fafcfde71ae81e00260801c5b6020821615610abc576ff0799caf21e927ea1252fa7400a1d8860260801c5b6040821615610adb576fe1e43f8ddd0922622788b108788fc1910260801c5b6080821615610afa576fc7530338a302e81d8229a7f1f67fa2650260801c5b610100821615610b1a576f9b3229ed2432991a2e021bb106f5feb60260801c5b610200821615610b3a576f5e15c89991553a6dc1c8a8a0931572d20260801c5b610400821615610b5a576f2294012b4d1cbe1865fe254cef6e40bc0260801c5b610800821615610b7a576f04aba5e0da8e29a77fabca56a012ae250260801c5b611000821615610b99576e15d0460cb40a7356d32b6966397c030260801c5b612000821615610bb7576d01dbd4effd593afec2694414e4f60260801c5b614000821615610bd2576a03746fe3b485b7be710a060260801c5b919050565b634e487b715f52806020526024601cfd5b5f82821882841002821861085a565b5f61085a8383670de0b6b3a7640000610c73565b5f80610c178385610f74565b90505f610c378280610c32670de0b6b3a76400006002610f74565b610c73565b90505f610c528284610c32670de0b6b3a76400006003610f74565b905080610c5f8385610fc5565b610c699190610fc5565b9695505050505050565b5f81610c7f8486610f74565b61073f9190610ff0565b60405180606001604052806003906020820280368337509192915050565b6040518060c001604052806006906020820280368337509192915050565b634e487b7160e01b5f52604160045260245ffd5b60405160a0810167ffffffffffffffff81118282101715610cfc57610cfc610cc5565b60405290565b604051601f8201601f1916810167ffffffffffffffff81118282101715610d2b57610d2b610cc5565b604052919050565b5f6101408284031215610d44575f5ffd5b610d4c610cd9565b8235815290506020820135600181900b8114610d66575f5ffd5b602082015260408281013590820152607f82018313610d83575f5ffd5b610d8d60c0610d02565b80610120840185811115610d9f575f5ffd5b606085015b81811015610dd75780356dffffffffffffffffffffffffffff81168114610dc9575f5ffd5b845260209384019301610da4565b5060608401919091523560808301525092915050565b5f5f6101608385031215610dff575f5ffd5b82359150610e108460208501610d33565b90509250929050565b805f5b6003811015610e3b578151845260209384019390910190600101610e1c565b50505050565b8381526020810183905260a0810161073f6040830184610e19565b5f5f6102008385031215610e6e575f5ffd5b83601f840112610e7c575f5ffd5b610e8660c0610d02565b8060c0850186811115610e97575f5ffd5b855b81811015610ec55780356001600160801b0381168114610eb7575f5ffd5b845260209384019301610e99565b50819450610ed38782610d33565b93505050509250929050565b610160810181865f5b6006811015610f105781516001600160801b0316835260209283019290910190600101610ee8565b5050508460c08301528360e0830152610772610100830184610e19565b634e487b7160e01b5f52603260045260245ffd5b634e487b7160e01b5f52601160045260245ffd5b6001600160801b03828116828216039081111561077f5761077f610f41565b808202811582820484141761077f5761077f610f41565b634e487b7160e01b5f52601260045260245ffd5b5f82610fad57610fad610f8b565b500690565b8181038181111561077f5761077f610f41565b8082018082111561077f5761077f610f41565b5f60018201610fe957610fe9610f41565b5060010190565b5f82610ffe57610ffe610f8b565b500490565b5f600160ff1b820161101757611017610f41565b505f039056fea26469706673582212202472b68e3a9108266feeea04c82c622c407a63a891acbe7d87294c0d3f0c4c1864736f6c634300081c0033", "sourceMap": "1057:14851:22:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;1057:14851:22;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1057:14851:22:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2595:895;;;;;;;;;;-1:-1:-1;2595:895:22;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;:::i;:::-;;;;;;;;7167:4448;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;:::i;2595:895::-;2763:22;2787;2811:35;;:::i;:::-;2862:29;;:33;2858:626;;2911:27;;:::i;:::-;3033:54;;;;;;;;;;;;3058:6;;3033:54;;3058:6;-1:-1:-1;3033:54:22;;;;;;;;;;;-1:-1:-1;;;;;3033:54:22;-1:-1:-1;;;;;3033:54:22;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3066:20;3033:24;:54::i;:::-;2952:135;;-1:-1:-1;2952:135:22;-1:-1:-1;2952:135:22;-1:-1:-1;2952:135:22;-1:-1:-1;3106:9:22;3101:100;3121:16;3117:1;:20;3101:100;;;3174:9;3184:1;3174:12;;;;;;;:::i;:::-;;;;;3162:6;3169:1;3162:9;;;;;;;:::i;:::-;;;;;;;:24;;-1:-1:-1;;;;;3162:24:22;;;:9;;;;;;:24;;;;;;;;;;;;;;;;3139:3;3101:100;;;-1:-1:-1;3219:254:22;3252:9;247:1:19;3252:20:22;;;;3290:9;279:1:19;3290:20:22;;;;3328:9;311:1:19;3328:20:22;;;;3366:9;342:1:19;3366:19:22;;;;3403:9;373:1:19;3403:19:22;;;;3440:9;404:1:19;3440:19:22;;;;3219:254;;;;;;;;;;-1:-1:-1;;;;;5256:47:195;;;5238:66;;5340:47;;;5335:2;5320:18;;5313:75;5424:47;;;5419:2;5404:18;;5397:75;5508:47;;;5503:2;5488:18;;5481:75;5593:47;;5587:3;5572:19;;5565:76;5678:47;;;5672:3;5657:19;;5650:76;5225:3;5210:19;;4951:781;3219:254:22;;;;;;;;2897:587;2858:626;2595:895;;;;;:::o;7167:4448::-;7343:27;;:::i;:::-;7384:29;7427;7470:35;;:::i;:::-;7565:6;7581:34;;:::i;:::-;7787:24;;;;7759:25;;7625:21;;;;;;7741:96;;7759:52;;7787:24;7759:52;:::i;:::-;-1:-1:-1;;;;;7741:96:22;7813:6;:23;;;7741:17;:96::i;:::-;7670:167;;;;7869:36;7908:139;7951:14;7967:16;7985;8003:6;:26;;;7908:21;:139::i;:::-;7869:178;;8133:783;;;;;;;;8342:176;8393:6;:15;;;8410:18;247:1:19;8410:29:22;;;;;;;:::i;:::-;;;;;8441:24;;;;-1:-1:-1;;;;;8342:176:22;8441:14;247:1:19;8467:25:22;;;;;-1:-1:-1;;;;;8342:176:22;:21;:176::i;:::-;8278:240;;5046:1:30;8278:240:22;:::i;:::-;8133:783;;8587:15;;8133:783;8604:29;;;;8635:24;;;;8133:783;;;;;8540:168;;8587:15;-1:-1:-1;;;;;8540:168:22;8635:14;279:1:19;8661:25:22;;8540:168;8133:783;;8777:15;;8794:29;;;;8825:24;;;;8133:783;;;;;8730:168;;8777:15;8794:29;-1:-1:-1;;;;;8730:168:22;8825:14;311:1:19;8851:25:22;;8730:168;8133:783;;;;;7851:1080;8973:323;;;;;;;;9015:73;9030:16;247:1:19;9030:27:22;;;;;;;:::i;:::-;;;;;1813:2;9077:3;9082:5;9015:14;:73::i;:::-;8973:323;;;;9110:73;9125:16;279:1:19;9125:27:22;;9110:73;8973:323;;;;9205:73;9220:16;311:1:19;9220:27:22;;9205:73;8973:323;;9315:277;;;;;;;;;9386:28;;9356:27;;:58;9315:277;;9386:28;9466;;;;9436:27;;;;:58;9315:277;;;;9546:28;;;;9516:27;;;;:58;;;;9315:277;;;;9751:24;;;;9723:25;;9783:30;;;;9386:28;;-1:-1:-1;;;9687:151:22;;9723:52;;;;-1:-1:-1;;;;;9687:151:22;;-1:-1:-1;;;2600:35:30;-1:-1:-1;9687:14:22;:151::i;:::-;9664:174;-1:-1:-1;9872:74:22;9887:14;342:1:19;9887:24:22;;;;-1:-1:-1;;;;;9872:74:22;9920:12;-1:-1:-1;;;9913:19:22;-1:-1:-1;;;9940:5:22;9872:14;:74::i;:::-;9856:90;-1:-1:-1;9989:152:22;10025:16;279:1:19;10025:27:22;;;;10054:16;;10072:14;279:1:19;10072:25:22;;;;;-1:-1:-1;;;;;10072:44:22;;10118:5;9989:14;:152::i;:::-;9965:176;-1:-1:-1;10183:152:22;10219:16;311:1:19;10219:27:22;;;;10248:16;;10266:14;311:1:19;10266:25:22;;10183:152;10159:176;-1:-1:-1;10441:9:22;;-1:-1:-1;;;10436:1173:22;404:1:19;10464::22;:13;10436:1173;;10498:35;;;;10649:20;443:1:19;10649::22;:20;:::i;:::-;10623:46;-1:-1:-1;10688:1:22;10684:545;;10752:13;10722:43;;10684:545;;;279:1:19;10790::22;:14;10786:443;;10847:21;10824:44;;10786:443;;;311:1:19;10893::22;:14;10889:340;;10950:21;10927:44;;10889:340;;;1923:1;10996;:16;10992:237;;;11100:17;11118:15;11100:34;;;;;;;:::i;:::-;;;;;11085:49;;342:1:19;11156::22;:13;11152:62;;11201:13;11171:43;;11152:62;11257:341;11314:27;11294:14;11309:1;11294:17;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;11294:47:22;;;;;:::i;:::-;11572:12;11480:20;11359:16;11376:15;11359:33;;;;;;;:::i;:::-;;;;;:141;;;;:::i;:::-;:225;;;;:::i;:::-;11257:19;:341::i;:::-;11242:9;11252:1;11242:12;;;;;;;:::i;:::-;-1:-1:-1;;;;;11242:356:22;;;:12;;;;;;:356;-1:-1:-1;10479:3:22;;-1:-1:-1;10479:3:22;;-1:-1:-1;10479:3:22;;-1:-1:-1;10479:3:22;:::i;:::-;;;;10436:1173;;;;7520:4095;;;7167:4448;;;;;;;:::o;4281:853::-;4408:22;4432;4518:43;4564:45;4592:16;4564:27;:45::i;:::-;4518:91;;4619:43;4677:72;4692:35;1283:21:30;-1:-1:-1;;;4744:4:22;4677:14;:72::i;:::-;4619:130;;4832:86;4847:21;4870:35;-1:-1:-1;;;4912:5:22;4832:14;:86::i;:::-;4815:103;;4981:86;4996:21;-1:-1:-1;;;5024:35:22;5061:5;4981:14;:86::i;:::-;4964:103;;5088:39;;4281:853;;;;;:::o;5140:2021::-;5345:35;;:::i;:::-;5607:25;;5676:24;;;;5607:25;5923;;;5896:24;;;;5392:23;;;;-1:-1:-1;;;;;5580:52:22;;;;5650:50;;;;5744:34;;;;5896:52;;;;;;5892:266;;;6056:25;;;;6029:24;;;;5990:149;;-1:-1:-1;;;;;6029:52:22;;6028:72;;;6102:15;5990:12;:149::i;:::-;5972:167;;5892:266;6206:25;;;;6179:24;;;;-1:-1:-1;;;;;6179:52:22;;;;;;6175:266;;;6339:25;;;;6312:24;;;;6273:149;;-1:-1:-1;;;;;6312:52:22;;6311:72;;;6385:15;6273:12;:149::i;:::-;6255:167;;6175:266;5562:893;;;6549:581;;;;;;;;6591:295;6647:172;6724:42;6733:15;6750;6724:8;:42::i;:::-;6697:24;;;;6768:25;;-1:-1:-1;;;;;6697:69:22;;;;;;;;6647:172;:20;:172::i;:::-;6845:19;6591:30;:295::i;:::-;6549:581;;;;6908:91;6929:14;373:1:19;6929:24:22;;;;-1:-1:-1;;;;;6908:91:22;6983:15;6955:14;279:1:19;6955:25:22;;;;;-1:-1:-1;;;;;6955:43:22;;6908:20;:91::i;:::-;6549:581;;;;7021:91;7042:14;404:1:19;7042:24:22;;;;-1:-1:-1;;;;;7021:91:22;7096:15;7068:14;311:1:19;7068:25:22;;7021:91;6549:581;;;5140:2021;-1:-1:-1;;;;;;;5140:2021:22:o;13656:386::-;13836:7;13855:22;13880:49;13917:11;13880:36;:49::i;:::-;13855:74;;13946:89;13977:8;13987:14;14003:15;14020:14;13946:30;:89::i;:::-;13939:96;;;13656:386;;;;;;;:::o;1908:204:20:-;1997:14;2032:5;2036:1;2032;:5;:::i;:::-;2023:14;;2056:10;:49;;2095:10;2104:1;2095:6;:10;:::i;:::-;2056:49;;;2069:23;2082:6;2090:1;2069:12;:23::i;:::-;2047:58;1908:204;-1:-1:-1;;;;;1908:204:20:o;14692:290:22:-;14943:21;;;14692:290;;;;;:::o;1452:464:26:-;1529:22;-1:-1:-1;;1567:15:26;;;;;;:34;;-1:-1:-1;1586:15:26;;;;1234:6;1586:15;1567:34;1563:64;;;1610:17;;-1:-1:-1;;;1610:17:26;;;;;;;;;;;1563:64;1655:12;;;;1638:14;1703:11;;;:32;;1728:7;1703:32;;;1717:8;1718:7;1717:8;:::i;:::-;1677:59;;1797:2;1764:29;1785:7;1764:20;:29::i;:::-;:35;;1747:52;;1835:4;1831:8;;:1;:8;1827:65;;;1858:34;1878:14;1858:17;:34;:::i;:::-;1841:51;;1827:65;1553:363;;1452:464;;;:::o;6215:704:89:-;6277:7;6300:1;6305;6300:6;6296:150;;6400:35;1035:4:79;6400:11:89;:35::i;:::-;6896:1;6891;6887;:5;6886:11;;;;;:::i;:::-;;6900:1;6886:15;6876:5;;;6860:42;;6215:704;-1:-1:-1;;;6215:704:89:o;5435:111::-;5493:7;5312:5;;;5527;;;5311:36;5306:42;;5519:20;5512:27;5435:111;-1:-1:-1;;;5435:111:89:o;11621:455:22:-;11755:19;11790:24;;11786:284;;11984:61;93:4:50;11997:19:22;:25;12024:20;11984:12;:61::i;12528:1122::-;12656:7;2180;12754:12;:49;12750:217;;-1:-1:-1;12826:11:22;12819:18;;12750:217;5211:7:30;12858:12:22;:45;12854:113;;-1:-1:-1;5342:6:30;12919:37:22;;12854:113;13241:27;13316:225;13348:44;13380:12;5211:7:30;13348:44:22;:::i;:::-;13410;13443:11;5342:6:30;13410:44:22;:::i;:::-;2359:6;13522:5;13316:14;:225::i;:::-;13271:270;;5342:6:30;13271:270:22;:::i;:::-;13241:300;;13559:84;13568:42;13577:19;13598:11;13568:8;:42::i;:::-;5342:6:30;13559:8:22;:84::i;15292:614::-;15402:20;1395:6;15438:40;;15434:425;;15509:34;:17;1520:6;15509:26;:34::i;:::-;15494:49;;15434:425;;;1462:8;15564:39;;15560:299;;1676:7;15634:58;1567:4;15635:39;1395:6;15635:17;:39;:::i;:::-;15634:50;;:58::i;:::-;:85;;;;:::i;15560:299::-;1747:7;15765:57;1612:5;15766:38;1462:8;15766:17;:38;:::i;15765:57::-;:83;;;;:::i;:::-;15750:98;;15560:299;15868:31;5393:8:30;15868:31:22;;:::i;14048:638::-;14236:7;14459:210;14485:91;14500:47;14526:10;14538:8;14500:25;:47::i;:::-;14549:14;93:4:50;14570:5:22;14485:14;:91::i;:::-;14614:41;14623:15;14640:14;14614:8;:41::i;:::-;-1:-1:-1;;;;;14594:61:22;14459:8;:210::i;6081:2078:26:-;6164:19;6233:7;6243:3;6233:13;6250:1;6233:18;:93;;-1:-1:-1;;;6233:93:26;;;-1:-1:-1;;;6233:93:26;6219:107;;;-1:-1:-1;6354:3:26;6344:13;;:18;6340:95;;-1:-1:-1;;;6379:48:26;6432:3;6378:57;6340:95;6463:3;6453:13;;:18;6449:95;;-1:-1:-1;;;6488:48:26;6541:3;6487:57;6449:95;6572:3;6562:13;;:18;6558:95;;6611:34;6597:48;6650:3;6596:57;6558:95;6681:4;6671:14;;:19;6667:129;;6739:34;6725:48;6778:3;6724:57;6667:129;6823:4;6813:14;;:19;6809:129;;6881:34;6867:48;6920:3;6866:57;6809:129;6965:4;6955:14;;:19;6951:129;;7023:34;7009:48;7062:3;7008:57;6951:129;7107:4;7097:14;;:19;7093:129;;7165:34;7151:48;7204:3;7150:57;7093:129;7249:5;7239:15;;:20;7235:130;;7308:34;7294:48;7347:3;7293:57;7235:130;7392:5;7382:15;;:20;7378:130;;7451:34;7437:48;7490:3;7436:57;7378:130;7535:5;7525:15;;:20;7521:130;;7594:34;7580:48;7633:3;7579:57;7521:130;7678:5;7668:15;;:20;7664:129;;7737:33;7723:47;7775:3;7722:56;7664:129;7820:6;7810:16;;:21;7806:129;;7880:32;7866:46;7917:3;7865:55;7806:129;7962:6;7952:16;;:21;7948:93;;8004:29;7990:43;8038:3;7989:52;7948:93;8069:6;8059:16;;:21;8055:87;;8111:23;8097:37;8139:3;8096:46;8055:87;6081:2078;;;:::o;1776:194:79:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;5617:111:89;5675:7;5312:5;;;5709;;;5311:36;5306:42;;5701:20;5071:294;314:117:50;377:7;403:21;414:1;417;93:4;403:10;:21::i;1311:319::-;1383:7;;1422:5;1426:1;1422;:5;:::i;:::-;1402:25;-1:-1:-1;1437:18:50;1458:41;1402:25;;1491:7;93:4;1491:1;:7;:::i;:::-;1458:10;:41::i;:::-;1437:62;-1:-1:-1;1509:17:50;1529:42;1437:62;1552:9;1563:7;93:4;1563:1;:7;:::i;1529:42::-;1509:62;-1:-1:-1;1509:62:50;1589:22;1601:10;1589:9;:22;:::i;:::-;:34;;;;:::i;:::-;1582:41;1311:319;-1:-1:-1;;;;;;1311:319:50:o;840:120::-;916:7;952:1;943:5;947:1;943;:5;:::i;:::-;942:11;;;;:::i;-1:-1:-1:-;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;:::o;14:127:195:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:253;218:2;212:9;260:4;248:17;;295:18;280:34;;316:22;;;277:62;274:88;;;342:18;;:::i;:::-;378:2;371:22;146:253;:::o;404:275::-;475:2;469:9;540:2;521:13;;-1:-1:-1;;517:27:195;505:40;;575:18;560:34;;596:22;;;557:62;554:88;;;622:18;;:::i;:::-;658:2;651:22;404:275;;-1:-1:-1;404:275:195:o;684:1331::-;751:5;799:6;787:9;782:3;778:19;774:32;771:52;;;819:1;816;809:12;771:52;841:22;;:::i;:::-;908:23;;940:22;;832:31;-1:-1:-1;1014:2:195;999:18;;986:32;1060:1;1049:22;;;1037:35;;1027:63;;1086:1;1083;1076:12;1027:63;1117:2;1106:14;;1099:31;1203:2;1188:18;;;1175:32;1223:14;;;1216:31;1285:3;1270:19;;1266:29;-1:-1:-1;1256:57:195;;1309:1;1306;1299:12;1256:57;1411:20;1388:3;1411:20;:::i;:::-;1453:3;1494;1483:9;1479:19;1521:3;1513:6;1510:15;1507:35;;;1538:1;1535;1528:12;1507:35;1577:2;1566:9;1562:18;1589:275;1605:6;1600:3;1597:15;1589:275;;;1687:3;1674:17;1739:30;1730:7;1726:44;1717:7;1714:57;1704:85;;1785:1;1782;1775:12;1704:85;1802:20;;1851:2;1842:12;;;;1622;1589:275;;;-1:-1:-1;1891:2:195;1880:14;;1873:29;;;;1947:20;1994:4;1983:16;;1976:33;-1:-1:-1;1884:5:195;684:1331;-1:-1:-1;;684:1331:195:o;2020:346::-;2150:6;2158;2211:3;2199:9;2190:7;2186:23;2182:33;2179:53;;;2228:1;2225;2218:12;2179:53;2264:9;2251:23;2241:33;;2293:67;2352:7;2347:2;2336:9;2332:18;2293:67;:::i;:::-;2283:77;;2020:346;;;;;:::o;2371:303::-;2464:5;2487:1;2497:171;2511:4;2508:1;2505:11;2497:171;;;2570:13;;2558:26;;2613:4;2604:14;;;;2641:17;;;;2531:1;2524:9;2497:171;;;2501:3;;2371:303;;:::o;2679:392::-;2936:25;;;2992:2;2977:18;;2970:34;;;2923:3;2908:19;;3013:52;3061:2;3046:18;;3038:6;3013:52;:::i;3076:932::-;3205:6;3213;3266:3;3254:9;3245:7;3241:23;3237:33;3234:53;;;3283:1;3280;3273:12;3234:53;3332:7;3325:4;3314:9;3310:20;3306:34;3296:62;;3354:1;3351;3344:12;3296:62;3456:20;3433:3;3456:20;:::i;:::-;3498:3;3539;3528:9;3524:19;3566:7;3558:6;3555:19;3552:39;;;3587:1;3584;3577:12;3552:39;3611:9;3629:275;3645:6;3640:3;3637:15;3629:275;;;3727:3;3714:17;-1:-1:-1;;;;;3768:5:195;3764:46;3757:5;3754:57;3744:85;;3825:1;3822;3815:12;3744:85;3842:18;;3889:4;3880:14;;;;3662;3629:275;;;3633:3;3923:5;3913:15;;3947:55;3994:7;3986:6;3947:55;:::i;:::-;3937:65;;;;;3076:932;;;;;:::o;4013:801::-;4331:3;4316:19;;4320:9;4412:6;4289:4;4446:212;4460:4;4457:1;4454:11;4446:212;;;4523:13;;-1:-1:-1;;;;;4519:54:195;4507:67;;4603:4;4594:14;;;;4631:17;;;;4480:1;4473:9;4446:212;;;4450:3;;;4695:6;4689:3;4678:9;4674:19;4667:35;4739:6;4733:3;4722:9;4718:19;4711:35;4755:53;4803:3;4792:9;4788:19;4780:6;4755:53;:::i;4819:127::-;4880:10;4875:3;4871:20;4868:1;4861:31;4911:4;4908:1;4901:15;4935:4;4932:1;4925:15;5737:127;5798:10;5793:3;5789:20;5786:1;5779:31;5829:4;5826:1;5819:15;5853:4;5850:1;5843:15;5869:243;-1:-1:-1;;;;;5984:42:195;;;5940;;;5936:91;;6039:44;;6036:70;;;6086:18;;:::i;6117:168::-;6190:9;;;6221;;6238:15;;;6232:22;;6218:37;6208:71;;6259:18;;:::i;6290:127::-;6351:10;6346:3;6342:20;6339:1;6332:31;6382:4;6379:1;6372:15;6406:4;6403:1;6396:15;6422:112;6454:1;6480;6470:35;;6485:18;;:::i;:::-;-1:-1:-1;6519:9:195;;6422:112::o;6539:128::-;6606:9;;;6627:11;;;6624:37;;;6641:18;;:::i;6672:125::-;6737:9;;;6758:10;;;6755:36;;;6771:18;;:::i;6802:135::-;6841:3;6862:17;;;6859:43;;6882:18;;:::i;:::-;-1:-1:-1;6929:1:195;6918:13;;6802:135::o;6942:120::-;6982:1;7008;6998:35;;7013:18;;:::i;:::-;-1:-1:-1;7047:9:195;;6942:120::o;7067:136::-;7102:3;-1:-1:-1;;;7123:22:195;;7120:48;;7148:18;;:::i;:::-;-1:-1:-1;7188:1:195;7184:13;;7067:136::o", "linkReferences": {}}, "methodIdentifiers": {"accrueInterestAndUpdateReservesWithAssets(uint128[6] storage,Interest.AccrueInterestParams)": "10c333d1", "accrueInterestWithAssets(uint128[6],Interest.AccrueInterestParams)": "2c602491"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"TickOutOfBounds\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositLAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositXAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositYAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowLAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowXAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowYAssets\",\"type\":\"uint128\"}],\"name\":\"InterestAccrued\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"uint128[6]\",\"name\":\"assets\",\"type\":\"uint128[6]\"},{\"components\":[{\"internalType\":\"uint256\",\"name\":\"duration\",\"type\":\"uint256\"},{\"internalType\":\"int16\",\"name\":\"lendingStateTick\",\"type\":\"int16\"},{\"internalType\":\"uint256\",\"name\":\"adjustedActiveLiquidity\",\"type\":\"uint256\"},{\"internalType\":\"uint112[6]\",\"name\":\"shares\",\"type\":\"uint112[6]\"},{\"internalType\":\"uint256\",\"name\":\"satPercentageInWads\",\"type\":\"uint256\"}],\"internalType\":\"struct Interest.AccrueInterestParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"accrueInterestWithAssets\",\"outputs\":[{\"internalType\":\"uint128[6]\",\"name\":\"newAssets\",\"type\":\"uint128[6]\"},{\"internalType\":\"uint256\",\"name\":\"interestXPortionForLP\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"interestYPortionForLP\",\"type\":\"uint256\"},{\"internalType\":\"uint256[3]\",\"name\":\"protocolFeeAssets\",\"type\":\"uint256[3]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"many calculations are unchecked because we asset values are stored as uint128. We also limit      the max amount amount of interest to ensure that it can not overflow when added to the      current assets.\",\"kind\":\"dev\",\"methods\":{},\"stateVariables\":{\"PENALTY_SATURATION_PERCENT_IN_WAD\":{\"details\":\"Maximum percentage for the penalty saturation allowed. This is used to prevent excessive penalties in case of high utilization.\"},\"SATURATION_PENALTY_BUFFER_IN_WAD\":{\"details\":\"`MAX_SATURATION_PERCENT_IN_WAD` - `PENALTY_SATURATION_PERCENT_IN_WAD`\"}},\"title\":\"Interest Library\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"This library is used for calculating and accruing interest.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/Interest.sol\":\"Interest\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/Interest.sol\":{\"keccak256\":\"0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194\",\"dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"lib/morpho-blue/src/libraries/MathLib.sol\":{\"keccak256\":\"0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08\",\"dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "TickOutOfBounds"}, {"inputs": [{"internalType": "uint128", "name": "depositLAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "depositXAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "depositYAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowLAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowXAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowYAssets", "type": "uint128", "indexed": false}], "type": "event", "name": "InterestAccrued", "anonymous": false}, {"inputs": [{"internalType": "uint128[6]", "name": "assets", "type": "uint128[6]"}, {"internalType": "struct Interest.AccrueInterestParams", "name": "params", "type": "tuple", "components": [{"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "int16", "name": "lendingStateTick", "type": "int16"}, {"internalType": "uint256", "name": "adjustedActiveLiquidity", "type": "uint256"}, {"internalType": "uint112[6]", "name": "shares", "type": "uint112[6]"}, {"internalType": "uint256", "name": "satPercentageInWads", "type": "uint256"}]}], "stateMutability": "pure", "type": "function", "name": "accrueInterestWithAssets", "outputs": [{"internalType": "uint128[6]", "name": "newAssets", "type": "uint128[6]"}, {"internalType": "uint256", "name": "interestXPortionForLP", "type": "uint256"}, {"internalType": "uint256", "name": "interestYPortionForLP", "type": "uint256"}, {"internalType": "uint256[3]", "name": "protocolFeeAssets", "type": "uint256[3]"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/Interest.sol": "Interest"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/Interest.sol": {"keccak256": "0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646", "urls": ["bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194", "dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "lib/morpho-blue/src/libraries/MathLib.sol": {"keccak256": "0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a", "urls": ["bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08", "dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd"], "license": "GPL-2.0-or-later"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}}, "version": 1}, "id": 22}