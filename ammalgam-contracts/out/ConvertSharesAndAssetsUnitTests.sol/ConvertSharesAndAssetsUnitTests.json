{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "testToAssets_Fuzz", "inputs": [{"name": "shares", "type": "uint112", "internalType": "uint112"}, {"name": "totalAssets", "type": "uint128", "internalType": "uint128"}, {"name": "totalShares", "type": "uint112", "internalType": "uint112"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "testToAssets_ZeroTotalSharesAndAssets", "inputs": [], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "testToShares_Fuzz", "inputs": [{"name": "assets", "type": "uint112", "internalType": "uint112"}, {"name": "totalAssets", "type": "uint128", "internalType": "uint128"}, {"name": "totalShares", "type": "uint112", "internalType": "uint112"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "testToShares_ZeroTotalAssetsAndShares", "inputs": [], "outputs": [], "stateMutability": "pure"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "355:2741:116:-:0;;;3126:44:97;;;3166:4;-1:-1:-1;;3126:44:97;;;;;;;;1016:26:107;;;;;;;;;;;355:2741:116;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "355:2741:116:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1349:933;;;;;;:::i;:::-;;:::i;:::-;;2907:134:100;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3823:151;;;:::i;:::-;;;;;;;:::i;3684:133::-;;;:::i;3385:141::-;;;:::i;2694:400:116:-;;;:::i;3193:186:100:-;;;:::i;:::-;;;;;;;:::i;2288:400:116:-;;;:::i;3047:140:100:-;;;:::i;:::-;;;;;;;:::i;3532:146::-;;;:::i;:::-;;;;;;;:::i;2754:147::-;;;:::i;2459:141::-;;;:::i;1243:204:96:-;;;:::i;:::-;;;6817:14:195;;6810:22;6792:41;;6780:2;6765:18;1243:204:96;6652:187:195;410:933:116;;;;;;:::i;:::-;;:::i;2606:142:100:-;;;:::i;1016:26:107:-;;;;;;;;;1349:933:116;1456:26;;-1:-1:-1;;;1456:26:116;;-1:-1:-1;;;;;1466:15:116;;;;1456:26;;;6792:41:195;1456:9:116;;;;6765:18:195;;1456:26:116;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1492:32:116;;-1:-1:-1;;;1492:32:116;;-1:-1:-1;;;;;1502:21:116;;-1:-1:-1;;;;;1502:21:116;;;;1492:32;;;6792:41:195;1492:9:116;;-1:-1:-1;1492:9:116;;-1:-1:-1;6765:18:195;;1492:32:116;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1565:15:116;;-1:-1:-1;1565:15:116;;-1:-1:-1;;;;;;;1627:43:116;;:29;-1:-1:-1;;;;;1627:29:116;;;;:15;;:29;:::i;:::-;:43;;;;:::i;:::-;1598:72;;1680:28;1711:62;1728:6;-1:-1:-1;;;;;1711:62:116;1736:11;-1:-1:-1;;;;;1711:62:116;1749:11;-1:-1:-1;;;;;1711:62:116;1762:10;1711:16;:62::i;:::-;1680:93;;1783:104;1792:20;1814:18;1783:104;;;;;;;;;;;;;;;;;:8;:104::i;:::-;1939:4;;-1:-1:-1;1953:24:116;-1:-1:-1;;;;;1980:63:116;;1939:4;1980:63;1981:29;-1:-1:-1;;;;;1981:29:116;;;;:15;;:29;:::i;:::-;:43;;;;:::i;:::-;:47;;;;:::i;:::-;1980:63;;;;:::i;:::-;1953:90;;2076:26;2105:62;2122:6;-1:-1:-1;;;;;2105:62:116;2130:11;-1:-1:-1;;;;;2105:62:116;2143:11;-1:-1:-1;;;;;2105:62:116;2156:10;2105:16;:62::i;:::-;2076:91;;2177:98;2186:18;2206:16;2177:98;;;;;;;;;;;;;;;;;:8;:98::i;:::-;1446:836;;;;;1349:933;;;:::o;2907:134:100:-;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2999:35:100;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;3823:151::-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3926:41:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;3684:133::-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3775:35:100;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3480:39:100;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;2694:400:116:-;2782:3;2765:14;;;;2922:62;2782:3;2765:14;;;2922:16;:62::i;:::-;2895:89;;2995:92;3004:16;3022:6;2995:92;;;;;;;;;;;;;;;;;:8;:92::i;:::-;2755:339;;;;;2694:400::o;3193:186:100:-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3317:55:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2288:400:116;2376:3;2359:14;;;;2516:62;2376:3;2359:14;;;2516:16;:62::i;:::-;2489:89;;2589:92;2598:16;2616:6;2589:92;;;;;;;;;;;;;;;;;:8;:92::i;3047:140:100:-;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3532:146;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2754:147;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2459:141;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1243:204:96;1302:7;;1282:4;;1302:7;;1298:143;;;-1:-1:-1;1332:7:96;;;;;1243:204::o;1298:143::-;1377:39;;-1:-1:-1;;;1377:39:96;;:7;:39;;;8325:51:195;;;-1:-1:-1;;;8392:18:195;;;8385:34;1428:1:96;;1377:7;;8298:18:195;;1377:39:96;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;1370:60;;1243:204;:::o;410:933:116:-;517:26;;-1:-1:-1;;;517:26:116;;-1:-1:-1;;;;;527:15:116;;;;517:26;;;6792:41:195;517:9:116;;;;6765:18:195;;517:26:116;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;553:32:116;;-1:-1:-1;;;553:32:116;;-1:-1:-1;;;;;563:21:116;;;;;;;;553:32;;;6792:41:195;553:9:116;;-1:-1:-1;553:9:116;;-1:-1:-1;6765:18:195;;553:32:116;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;626:15;644:5;626:23;;659:26;720:11;-1:-1:-1;;;;;688:43:116;706:11;-1:-1:-1;;;;;688:29:116;696:6;-1:-1:-1;;;;;688:15:116;:29;;;;:::i;:::-;:43;;;;:::i;:::-;659:72;;741:28;772:62;789:6;-1:-1:-1;;;;;772:62:116;797:11;-1:-1:-1;;;;;772:62:116;810:11;-1:-1:-1;;;;;772:62:116;823:10;772:16;:62::i;:::-;741:93;;844:104;853:20;875:18;844:104;;;;;;;;;;;;;;;;;:8;:104::i;:::-;1000:4;987:17;;1014:24;1093:11;-1:-1:-1;;;;;1041:63:116;1088:1;1074:11;-1:-1:-1;;;;;1042:43:116;1060:11;-1:-1:-1;;;;;1042:29:116;1050:6;-1:-1:-1;;;;;1042:15:116;:29;;;;:::i;:::-;:43;;;;:::i;:::-;:47;;;;:::i;:::-;1041:63;;;;:::i;:::-;1014:90;;1137:26;1166:62;1183:6;-1:-1:-1;;;;;1166:62:116;1191:11;-1:-1:-1;;;;;1166:62:116;1204:11;-1:-1:-1;;;;;1166:62:116;1217:10;1166:16;:62::i;:::-;1137:91;;1238:98;1247:18;1267:16;1238:98;;;;;;;;;;;;;;;;;:8;:98::i;2606:142:100:-;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2702:39:100;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;1541:361:20:-;1695:15;1726:11;1741:1;1726:16;1722:105;;-1:-1:-1;1765:6:20;1758:13;;1722:105;1843:52;1850:6;1858:11;1871;1884:10;1843:6;:52::i;:::-;1836:59;;1541:361;;;;;;;:::o;2386:134:96:-;2484:29;;-1:-1:-1;;;2484:29:96;;:11;;;;:29;;2496:4;;2502:5;;2509:3;;2484:29;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2386:134;;;:::o;1174:361:20:-;1328:15;1359:11;1374:1;1359:16;1355:105;;-1:-1:-1;1398:6:20;1391:13;;1355:105;1476:52;1483:6;1491:11;1504;1517:10;1908:204;1997:14;2032:5;2036:1;2032;:5;:::i;:::-;2023:14;;2056:10;:49;;2095:10;2104:1;2095:6;:10;:::i;:::-;2056:49;;;2069:23;2082:6;2090:1;6277:7:89;6300:1;6305;6300:6;6296:150;;6400:35;1035:4:79;6400:11:89;:35::i;:::-;6896:1;6891;6887;:5;6886:11;;;;;:::i;:::-;;6900:1;6886:15;6876:5;;;6860:42;6853:49;;6215:704;;;;;:::o;1776:194:79:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;14:184:195;82:20;;-1:-1:-1;;;;;131:42:195;;121:53;;111:81;;188:1;185;178:12;111:81;14:184;;;:::o;203:449::-;280:6;288;296;349:2;337:9;328:7;324:23;320:32;317:52;;;365:1;362;355:12;317:52;388:29;407:9;388:29;:::i;:::-;378:39;;467:2;456:9;452:18;439:32;-1:-1:-1;;;;;504:5:195;500:46;493:5;490:57;480:85;;561:1;558;551:12;480:85;584:5;-1:-1:-1;608:38:195;642:2;627:18;;608:38;:::i;:::-;598:48;;203:449;;;;;:::o;657:637::-;847:2;859:21;;;929:13;;832:18;;;951:22;;;799:4;;1030:15;;;1004:2;989:18;;;799:4;1073:195;1087:6;1084:1;1081:13;1073:195;;;1152:13;;-1:-1:-1;;;;;1148:39:195;1136:52;;1217:2;1243:15;;;;1208:12;;;;1184:1;1102:9;1073:195;;;-1:-1:-1;1285:3:195;;657:637;-1:-1:-1;;;;;657:637:195:o;1299:289::-;1341:3;1379:5;1373:12;1406:6;1401:3;1394:19;1462:6;1455:4;1448:5;1444:16;1437:4;1432:3;1428:14;1422:47;1514:1;1507:4;1498:6;1493:3;1489:16;1485:27;1478:38;1577:4;1570:2;1566:7;1561:2;1553:6;1549:15;1545:29;1540:3;1536:39;1532:50;1525:57;;;1299:289;;;;:::o;1593:1628::-;1799:4;1847:2;1836:9;1832:18;1877:2;1866:9;1859:21;1900:6;1935;1929:13;1966:6;1958;1951:22;2004:2;1993:9;1989:18;1982:25;;2066:2;2056:6;2053:1;2049:14;2038:9;2034:30;2030:39;2016:53;;2104:2;2096:6;2092:15;2125:1;2135:1057;2149:6;2146:1;2143:13;2135:1057;;;-1:-1:-1;;2214:22:195;;;2210:36;2198:49;;2270:13;;2357:9;;-1:-1:-1;;;;;2353:35:195;2338:51;;2436:2;2428:11;;;2422:18;2322:2;2460:15;;;2453:27;;;2541:19;;2310:15;;;2573:24;;;2728:21;;;2631:2;2681:1;2677:16;;;2665:29;;2661:38;;;2619:15;;;;-1:-1:-1;2787:296:195;2803:8;2798:3;2795:17;2787:296;;;2909:2;2905:7;2896:6;2888;2884:19;2880:33;2873:5;2866:48;2941:42;2976:6;2965:8;2959:15;2941:42;:::i;:::-;3026:2;3012:17;;;;2931:52;;-1:-1:-1;3055:14:195;;;;;2831:1;2822:11;2787:296;;;-1:-1:-1;3106:6:195;;-1:-1:-1;;;3147:2:195;3170:12;;;;3135:15;;;;;-1:-1:-1;2171:1:195;2164:9;2135:1057;;;-1:-1:-1;3209:6:195;;1593:1628;-1:-1:-1;;;;;;1593:1628:195:o;3226:446::-;3278:3;3316:5;3310:12;3343:6;3338:3;3331:19;3375:4;3370:3;3366:14;3359:21;;3414:4;3407:5;3403:16;3437:1;3447:200;3461:6;3458:1;3455:13;3447:200;;;3526:13;;-1:-1:-1;;;;;;3522:40:195;3510:53;;3592:4;3583:14;;;;3620:17;;;;3483:1;3476:9;3447:200;;;-1:-1:-1;3663:3:195;;3226:446;-1:-1:-1;;;;3226:446:195:o;3677:1145::-;3897:4;3945:2;3934:9;3930:18;3975:2;3964:9;3957:21;3998:6;4033;4027:13;4064:6;4056;4049:22;4102:2;4091:9;4087:18;4080:25;;4164:2;4154:6;4151:1;4147:14;4136:9;4132:30;4128:39;4114:53;;4202:2;4194:6;4190:15;4223:1;4233:560;4247:6;4244:1;4241:13;4233:560;;;4340:2;4336:7;4324:9;4316:6;4312:22;4308:36;4303:3;4296:49;4374:6;4368:13;4420:2;4414:9;4451:2;4443:6;4436:18;4481:48;4525:2;4517:6;4513:15;4499:12;4481:48;:::i;:::-;4467:62;;4578:2;4574;4570:11;4564:18;4542:40;;4631:6;4623;4619:19;4614:2;4606:6;4602:15;4595:44;4662:51;4706:6;4690:14;4662:51;:::i;:::-;4652:61;-1:-1:-1;;;4748:2:195;4771:12;;;;4736:15;;;;;4269:1;4262:9;4233:560;;4827:782;4989:4;5037:2;5026:9;5022:18;5067:2;5056:9;5049:21;5090:6;5125;5119:13;5156:6;5148;5141:22;5194:2;5183:9;5179:18;5172:25;;5256:2;5246:6;5243:1;5239:14;5228:9;5224:30;5220:39;5206:53;;5294:2;5286:6;5282:15;5315:1;5325:255;5339:6;5336:1;5333:13;5325:255;;;5432:2;5428:7;5416:9;5408:6;5404:22;5400:36;5395:3;5388:49;5460:40;5493:6;5484;5478:13;5460:40;:::i;:::-;5450:50;-1:-1:-1;5535:2:195;5558:12;;;;5523:15;;;;;5361:1;5354:9;5325:255;;5614:1033;5818:4;5866:2;5855:9;5851:18;5896:2;5885:9;5878:21;5919:6;5954;5948:13;5985:6;5977;5970:22;6023:2;6012:9;6008:18;6001:25;;6085:2;6075:6;6072:1;6068:14;6057:9;6053:30;6049:39;6035:53;;6123:2;6115:6;6111:15;6144:1;6154:464;6168:6;6165:1;6162:13;6154:464;;;6233:22;;;-1:-1:-1;;6229:36:195;6217:49;;6289:13;;6334:9;;-1:-1:-1;;;;;6330:35:195;6315:51;;6413:2;6405:11;;;6399:18;6454:2;6437:15;;;6430:27;;;6399:18;6480:58;;6522:15;;6399:18;6480:58;:::i;:::-;6470:68;-1:-1:-1;;6573:2:195;6596:12;;;;6561:15;;;;;6190:1;6183:9;6154:464;;6844:127;6905:10;6900:3;6896:20;6893:1;6886:31;6936:4;6933:1;6926:15;6960:4;6957:1;6950:15;6976:168;7049:9;;;7080;;7097:15;;;7091:22;;7077:37;7067:71;;7118:18;;:::i;7149:127::-;7210:10;7205:3;7201:20;7198:1;7191:31;7241:4;7238:1;7231:15;7265:4;7262:1;7255:15;7281:217;7321:1;7347;7337:132;;7391:10;7386:3;7382:20;7379:1;7372:31;7426:4;7423:1;7416:15;7454:4;7451:1;7444:15;7337:132;-1:-1:-1;7483:9:195;;7281:217::o;7503:125::-;7568:9;;;7589:10;;;7586:36;;;7602:18;;:::i;7633:128::-;7700:9;;;7721:11;;;7718:37;;;7735:18;;:::i;7766:380::-;7845:1;7841:12;;;;7888;;;7909:61;;7963:4;7955:6;7951:17;7941:27;;7909:61;8016:2;8008:6;8005:14;7985:18;7982:38;7979:161;;8062:10;8057:3;8053:20;8050:1;8043:31;8097:4;8094:1;8087:15;8125:4;8122:1;8115:15;7979:161;;7766:380;;;:::o;8430:184::-;8500:6;8553:2;8541:9;8532:7;8528:23;8524:32;8521:52;;;8569:1;8566;8559:12;8521:52;-1:-1:-1;8592:16:195;;8430:184;-1:-1:-1;8430:184:195:o;8619:362::-;8824:6;8813:9;8806:25;8867:6;8862:2;8851:9;8847:18;8840:34;8910:2;8905;8894:9;8890:18;8883:30;8787:4;8930:45;8971:2;8960:9;8956:18;8948:6;8930:45;:::i", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "testToAssets_Fuzz(uint112,uint128,uint112)": "c6ac679c", "testToAssets_ZeroTotalSharesAndAssets()": "7cdc02de", "testToShares_Fuzz(uint112,uint128,uint112)": "1af60a7f", "testToShares_ZeroTotalAssetsAndShares()": "416f4894"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint112\",\"name\":\"shares\",\"type\":\"uint112\"},{\"internalType\":\"uint128\",\"name\":\"totalAssets\",\"type\":\"uint128\"},{\"internalType\":\"uint112\",\"name\":\"totalShares\",\"type\":\"uint112\"}],\"name\":\"testToAssets_Fuzz\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testToAssets_ZeroTotalSharesAndAssets\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint112\",\"name\":\"assets\",\"type\":\"uint112\"},{\"internalType\":\"uint128\",\"name\":\"totalAssets\",\"type\":\"uint128\"},{\"internalType\":\"uint112\",\"name\":\"totalShares\",\"type\":\"uint112\"}],\"name\":\"testToShares_Fuzz\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testToShares_ZeroTotalAssetsAndShares\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/ConvertSharesAndAssetsUnitTests.sol\":\"ConvertSharesAndAssetsUnitTests\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Base.sol\":{\"keccak256\":\"0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224\",\"dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1\",\"dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e\",\"dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c\",\"dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"test/ConvertSharesAndAssetsUnitTests.sol\":{\"keccak256\":\"0xc0dc7e0e634259a9e3fe8fbbc797ee91d908b4404ad15aa81cb9198e514e692d\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://f976e010ce9afdd316a1138c595e11d16a74faaa90db65e267eddda68de59c4c\",\"dweb:/ipfs/Qmcw4QZYry3nxfd8ts4tJFRjdTmY4y1JvTtdmUjhTSPtGh\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [{"internalType": "uint112", "name": "shares", "type": "uint112"}, {"internalType": "uint128", "name": "totalAssets", "type": "uint128"}, {"internalType": "uint112", "name": "totalShares", "type": "uint112"}], "stateMutability": "pure", "type": "function", "name": "testToAssets_Fuzz"}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "testToAssets_ZeroTotalSharesAndAssets"}, {"inputs": [{"internalType": "uint112", "name": "assets", "type": "uint112"}, {"internalType": "uint128", "name": "totalAssets", "type": "uint128"}, {"internalType": "uint112", "name": "totalShares", "type": "uint112"}], "stateMutability": "pure", "type": "function", "name": "testToShares_Fuzz"}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "testToShares_ZeroTotalAssetsAndShares"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/ConvertSharesAndAssetsUnitTests.sol": "ConvertSharesAndAssetsUnitTests"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Base.sol": {"keccak256": "0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c", "urls": ["bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224", "dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol": {"keccak256": "0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b", "urls": ["bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1", "dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Test.sol": {"keccak256": "0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe", "urls": ["bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e", "dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol": {"keccak256": "0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354", "urls": ["bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c", "dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC"], "license": "MIT OR Apache-2.0"}, "lib/openzeppelin-contracts/lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "test/ConvertSharesAndAssetsUnitTests.sol": {"keccak256": "0xc0dc7e0e634259a9e3fe8fbbc797ee91d908b4404ad15aa81cb9198e514e692d", "urls": ["bzz-raw://f976e010ce9afdd316a1138c595e11d16a74faaa90db65e267eddda68de59c4c", "dweb:/ipfs/Qmcw4QZYry3nxfd8ts4tJFRjdTmY4y1JvTtdmUjhTSPtGh"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 116}