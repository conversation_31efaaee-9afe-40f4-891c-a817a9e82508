{"abi": [{"type": "function", "name": "borrow", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amountXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "amountYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrowLiquidity", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "borrowAmountLAssets", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "burn", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [{"name": "amountXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "amountYAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "deposit", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "externalLiquidity", "inputs": [], "outputs": [{"name": "", "type": "uint112", "internalType": "uint112"}], "stateMutability": "view"}, {"type": "function", "name": "getReserves", "inputs": [], "outputs": [{"name": "_reserveXAssets", "type": "uint112", "internalType": "uint112"}, {"name": "_reserveYAssets", "type": "uint112", "internalType": "uint112"}, {"name": "_lastTimestamp", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "getTickRange", "inputs": [], "outputs": [{"name": "minTick", "type": "int16", "internalType": "int16"}, {"name": "maxTick", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "liquidate", "inputs": [{"name": "borrower", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "depositLToBeTransferredInLAssets", "type": "uint256", "internalType": "uint256"}, {"name": "depositXToBeTransferredInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "depositYToBeTransferredInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayLXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayLYInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayYInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "liquidationType", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mint", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [{"name": "liquidityShares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "referenceReserves", "inputs": [], "outputs": [{"name": "", "type": "uint112", "internalType": "uint112"}, {"name": "", "type": "uint112", "internalType": "uint112"}], "stateMutability": "view"}, {"type": "function", "name": "repay", "inputs": [{"name": "onBehalfOf", "type": "address", "internalType": "address"}], "outputs": [{"name": "repayXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayYInYAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "repayLiquidity", "inputs": [{"name": "onBehalfOf", "type": "address", "internalType": "address"}], "outputs": [{"name": "repaidLXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repaidLYInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayLiquidityAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "skim", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "swap", "inputs": [{"name": "amountXOut", "type": "uint256", "internalType": "uint256"}, {"name": "amountYOut", "type": "uint256", "internalType": "uint256"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "sync", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "tokens", "inputs": [{"name": "tokenType", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "contract IAmmalgamERC20"}], "stateMutability": "view"}, {"type": "function", "name": "totalAssets", "inputs": [], "outputs": [{"name": "", "type": "uint128[6]", "internalType": "uint128[6]"}], "stateMutability": "view"}, {"type": "function", "name": "underlyingTokens", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IERC20"}, {"name": "", "type": "address", "internalType": "contract IERC20"}], "stateMutability": "view"}, {"type": "function", "name": "updateExternalLiquidity", "inputs": [{"name": "_externalLiquidity", "type": "uint112", "internalType": "uint112"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "validateOnUpdate", "inputs": [{"name": "validate", "type": "address", "internalType": "address"}, {"name": "update", "type": "address", "internalType": "address"}, {"name": "isBorrow", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "BurnBadDebt", "inputs": [{"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenType", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "badDebtAssets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "badDebtShares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "InterestAccrued", "inputs": [{"name": "depositLAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "depositXAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "depositYAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowLAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowXAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowYAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "Liquidate", "inputs": [{"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "depositL", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "depositX", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "depositY", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "repayLX", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "repayLY", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "repayX", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "repayY", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "liquidationType", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amountXIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountYIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountXOut", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountYOut", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Sync", "inputs": [{"name": "reserveXAssets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "reserveYAssets", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "UpdateExternalLiquidity", "inputs": [{"name": "externalLiquidity", "type": "uint112", "indexed": false, "internalType": "uint112"}], "anonymous": false}, {"type": "error", "name": "AmmalgamCannotBorrowAgainstSameCollateral", "inputs": []}, {"type": "error", "name": "AmmalgamDepositIsNotStrictlyBigger", "inputs": []}, {"type": "error", "name": "AmmalgamLTV", "inputs": []}, {"type": "error", "name": "AmmalgamMaxBorrowReached", "inputs": []}, {"type": "error", "name": "AmmalgamMaxSlippage", "inputs": []}, {"type": "error", "name": "AmmalgamTooMuchLeverage", "inputs": []}, {"type": "error", "name": "Forbidden", "inputs": []}, {"type": "error", "name": "InsufficientInputAmount", "inputs": []}, {"type": "error", "name": "InsufficientLiquidity", "inputs": []}, {"type": "error", "name": "InsufficientLiquidity", "inputs": []}, {"type": "error", "name": "InsufficientLiquidityBurned", "inputs": []}, {"type": "error", "name": "InsufficientLiquidityMinted", "inputs": []}, {"type": "error", "name": "InsufficientOutputAmount", "inputs": []}, {"type": "error", "name": "InsufficientRepayLiquidity", "inputs": []}, {"type": "error", "name": "InvalidToAddress", "inputs": []}, {"type": "error", "name": "K", "inputs": []}, {"type": "error", "name": "Locked", "inputs": []}, {"type": "error", "name": "NotEnoughRepaidForLiquidation", "inputs": []}, {"type": "error", "name": "Overflow", "inputs": []}, {"type": "error", "name": "PriceOutOfBounds", "inputs": []}, {"type": "error", "name": "SafeCastOverflowedUintDowncast", "inputs": [{"name": "bits", "type": "uint8", "internalType": "uint8"}, {"name": "value", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "TickOutOfBounds", "inputs": []}], "bytecode": {"object": "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__$0148cd7411c566e8e3abb1476dee2c2502$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$18b16c1511582993d32b10789a400df1a7$__90631eaa67839061334f907f0000000000000000000000000000000000000000000000000000000000000000908a908a908990899089906004016159cc565b5f6040518083038186803b158015613365575f5ffd5b505af4158015613377573d5f5f3e3d5ffd5b50505050610ac585858585856144fb565b6040516325c084e360e11b81525f9073__$18b16c1511582993d32b10789a400df1a7$__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__$0148cd7411c566e8e3abb1476dee2c2502$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", "sourceMap": "1385:49015:0:-:0;;;2596:45:38;;;-1:-1:-1;;;;;;2596:45:38;;;;1651:28:0;;1385:49015;;;;;;;;;;2836:34:38;;:::i;:::-;2907:10;2880:38;;;;2958:37;;;-1:-1:-1;;;2958:37:38;;;;:35;;:37;;;;;;;;;;;;;;;;2907:10;2958:37;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;2928:67:38;;;;;;;;;;;;;;3023:20;;3006:37;;;;-1:-1:-1;3070:20:38;;;;3053:37;;;;3117:20;;;;;3100:37;;;;3163:19;;;;3147:35;;;;3208:19;;;;3192:35;;;;3253:19;;;;3237:35;;;;3350:7;;:41;;-1:-1:-1;;;3350:41:38;;;;2928:67;;-1:-1:-1;3350:39:38;;;;;;373:1:19;3350:41:38;;;;;;;;;;:39;:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;3283:109:38;;;-1:-1:-1;1385:49015:0;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1385:49015:0;;;-1:-1:-1;;1385:49015:0:o;14:139:195:-;-1:-1:-1;;;;;97:31:195;;87:42;;77:70;;143:1;140;133:12;77:70;14:139;:::o;158:127::-;219:10;214:3;210:20;207:1;200:31;250:4;247:1;240:15;274:4;271:1;264:15;290:162;385:13;;407:39;385:13;407:39;:::i;:::-;290:162;;;:::o;457:1132::-;623:6;631;639;692:3;680:9;671:7;667:23;663:33;660:53;;;709:1;706;699:12;660:53;741:9;735:16;760:39;793:5;760:39;:::i;:::-;868:2;853:18;;847:25;818:5;;-1:-1:-1;881:41:195;847:25;881:41;:::i;:::-;941:7;-1:-1:-1;986:2:195;971:18;;967:32;-1:-1:-1;957:60:195;;1013:1;1010;1003:12;957:60;1046:2;1040:9;1088:3;1076:16;;-1:-1:-1;;;;;1107:34:195;;1143:22;;;1104:62;1101:88;;;1169:18;;:::i;:::-;1205:2;1198:22;1240:6;1284:3;1269:19;;1300;;;1297:39;;;1332:1;1329;1322:12;1297:39;1371:2;1360:9;1356:18;1383:175;1399:6;1394:3;1391:15;1383:175;;;1465:50;1511:3;1465:50;:::i;:::-;1453:63;;1545:2;1536:12;;;;1416;1383:175;;;1387:3;;;1577:6;1567:16;;;457:1132;;;;;:::o;1726:300::-;1837:6;1890:2;1878:9;1869:7;1865:23;1861:32;1858:52;;;1906:1;1903;1896:12;1858:52;1938:9;1932:16;1957:39;1990:5;1957:39;:::i;:::-;2015:5;1726:300;-1:-1:-1;;;1726:300:195:o;:::-;1385:49015:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {"contracts/libraries/Interest.sol": {"Interest": [{"start": 2563, "length": 20}, {"start": 16454, "length": 20}]}, "contracts/libraries/Liquidation.sol": {"Liquidation": [{"start": 13916, "length": 20}, {"start": 14072, "length": 20}]}}}, "deployedBytecode": {"object": "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__$0148cd7411c566e8e3abb1476dee2c2502$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$18b16c1511582993d32b10789a400df1a7$__90631eaa67839061334f907f0000000000000000000000000000000000000000000000000000000000000000908a908a908990899089906004016159cc565b5f6040518083038186803b158015613365575f5ffd5b505af4158015613377573d5f5f3e3d5ffd5b50505050610ac585858585856144fb565b6040516325c084e360e11b81525f9073__$18b16c1511582993d32b10789a400df1a7$__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__$0148cd7411c566e8e3abb1476dee2c2502$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", "sourceMap": "1385:49015:0:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8309:2094:38;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;7682:2760:0;;;;;;:::i;:::-;;:::i;:::-;;45835:752;;;;;;:::i;:::-;;:::i;6717:282:38:-;;;:::i;:::-;;;;-1:-1:-1;;;;;2679:43:195;;;2661:62;;2759:43;;;;2754:2;2739:18;;2732:71;2851:10;2839:23;2819:18;;;2812:51;2649:2;2634:18;6717:282:38;2461:408:195;7438:130:38;7524:17;;7438:130;;;-1:-1:-1;;;;;7524:17:38;;;3048:62:195;;-1:-1:-1;;;7543:17:38;;;;;;3141:2:195;3126:18;;3119:71;3021:18;7438:130:38;2874:322:195;7005:427:38;;;:::i;:::-;;;;3396:1:195;3385:21;;;3367:40;;3443:21;;;;3438:2;3423:18;;3416:49;3340:18;7005:427:38;3201:270:195;5867:314:38;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;3894:32:195;;;3876:51;;3864:2;3849:18;5867:314:38;3707:226:195;15539:631:0;;;;;;:::i;:::-;;:::i;4516:213:38:-;;;;;;:::i;:::-;;:::i;2321:2731:0:-;;;;;;:::i;:::-;;:::i;:::-;;;4735:25:195;;;4723:2;4708:18;2321:2731:0;4589:177:195;2596:45:38;;;;;-1:-1:-1;;;;;2596:45:38;;;;;;-1:-1:-1;;;;;4935:43:195;;;4917:62;;4905:2;4890:18;2596:45:38;4771:214:195;19795:2670:0;;;;;;:::i;:::-;;:::i;:::-;;;;5833:25:195;;;5889:2;5874:18;;5867:34;;;;5806:18;19795:2670:0;5659:248:195;5161:2412:0;;;;;;:::i;:::-;;:::i;17371:1200::-;;;;;;:::i;:::-;;:::i;22471:164::-;;;;;;:::i;:::-;;:::i;41555:225::-;;;;;;:::i;:::-;;:::i;3631:114:38:-;;;:::i;:::-;;;;-1:-1:-1;;;;;6926:32:195;;;6908:51;;6995:32;;;;6990:2;6975:18;;6968:60;6881:18;3631:114:38;6702:332:195;24716:216:0;;;;;;:::i;:::-;;:::i;:::-;;;;7241:25:195;;;7297:2;7282:18;;7275:34;;;;7325:18;;;7318:34;7229:2;7214:18;24716:216:0;7039:319:195;28422:2410:0;;;;;;:::i;:::-;;:::i;13566:1346::-;;;;;;:::i;:::-;;:::i;41889:54::-;;;:::i;8309:2094:38:-;8353:17;;:::i;:::-;8386:19;;:24;8382:349;;-1:-1:-1;8426:294:38;;;;;;;;-1:-1:-1;;;;;;8459:19:38;8426:294;;;;8505:19;;8426:294;;;;;;8551:19;;8426:294;;;;;;;;;8597:18;;8426:294;;;;;;8642:18;;8426:294;;;;;;8687:18;;8426:294;;;;;;;;8309:2094::o;8382:349::-;8957:19;;9033:20;;26839:21:21;-1:-1:-1;;;8957:19:38;;;;;;26839:15:21;:21;;8938:38:38;;;;9033:20;;;;;9014:39;;;;9133:26;;8741:27;9133:26;9129:73;;9175:16;;;;;;;;;;;9182:9;;9175:16;;9182:9;-1:-1:-1;9175:16:38;;;;;;;;;;;-1:-1:-1;;;;;9175:16:38;-1:-1:-1;;;;;9175:16:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8309:2094;:::o;9129:73::-;9213:23;9238;9266:13;:11;:13::i;:::-;9212:67;-1:-1:-1;;;;;9212:67:38;;;-1:-1:-1;;;;;9212:67:38;;;9289:13;9305:31;-1:-1:-1;;;;;9305:41:38;;9360:86;9384:61;9399:15;-1:-1:-1;;;9422:15:38;9439:5;9384:14;:61::i;:::-;9360:23;:86::i;:::-;9305:151;;-1:-1:-1;;;;;;9305:151:38;;;;;;;8894:1:195;8883:21;;;;9305:151:38;;;8865:40:195;8838:18;;9305:151:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9692:135;;-1:-1:-1;;;9692:135:38;;9517:1:195;9506:21;;;9692:135:38;;;9488:40:195;9692:135:38;9564:23:195;;;9544:18;;;9537:51;9624:23;;9604:18;;;9597:51;9289:167:38;;-1:-1:-1;9467:22:38;;;;-1:-1:-1;;;;;9692:31:38;:51;;;;9461:18:195;;9692:135:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9650:177;;;;;;;;9848:43;9894:318;;;;;;;;9948:21;9894:318;;;;;;10001:16;9894:318;;;;;;10056:60;10083:15;10100;10056:26;:60::i;:::-;9894:318;;;;;;;;;;;;;;;;;;-1:-1:-1;;9894:318:38;;-1:-1:-1;;9894:318:38;;;;;;;;;;;-1:-1:-1;;;;;9894:318:38;-1:-1:-1;;;;;9894:318:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;9894:318:38;;;-1:-1:-1;;;9894:318:38;;;;;10313:52;;-1:-1:-1;;;10313:52:38;;9848:364;;-1:-1:-1;;;10313:8:38;;:33;;:52;;10347:9;;9848:364;;10313:52;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;10274:91:38;;8309:2094;-1:-1:-1;;;;;;;;;;;;8309:2094:38:o;7682:2760:0:-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;7797:15;;:34;::::1;;;-1:-1:-1::0;7816:15:0;;7797:34:::1;7793:73;;;7840:26;;-1:-1:-1::0;;;7840:26:0::1;;;;;;;;;;;7793:73;7878:23;7903;7931:13;:11;:13::i;:::-;7877:67;-1:-1:-1::0;;;;;7877:67:0::1;;;-1:-1:-1::0;;;;;7877:67:0::1;;;8068:51;8086:15;8103;8068:17;:51::i;:::-;8182:17;8209;8303:23;8328;8355:15;18479:14:38::0;;-1:-1:-1;;;;;18479:14:38;;;;-1:-1:-1;;;18495:14:38;;;;;18395:122;8355:15:0::1;-1:-1:-1::0;;;;;8302:68:0;;::::1;::::0;-1:-1:-1;8302:68:0::1;::::0;-1:-1:-1;8403:33:0::1;8302:68:::0;8403:15;:33:::1;:::i;:::-;8389:10;:47;;:98;;;-1:-1:-1::0;8454:33:0::1;8472:15:::0;8454;:33:::1;:::i;:::-;8440:10;:47;;8389:98;8385:167;;;8514:23;;-1:-1:-1::0;;;8514:23:0::1;;;;;;;;;;;8385:167;8736:42;8751:2;8755:10;8767;8736:14;:42::i;:::-;8796:15:::0;;8792:128:::1;;8831:74;::::0;-1:-1:-1;;;8831:74:0;;-1:-1:-1;;;;;8831:32:0;::::1;::::0;::::1;::::0;:74:::1;::::0;8864:10:::1;::::0;8876;;8888;;8900:4;;;;8831:74:::1;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8792:128;9005:24;9031;9059:20;9074:1;9077;9059:14;:20::i;:::-;9004:75;;;;9106:64;9124:10;9136:16;9154:15;9106:17;:64::i;:::-;9094:76;;9196:64;9214:10;9226:16;9244:15;9196:17;:64::i;:::-;9184:76:::0;-1:-1:-1;9339:14:0;;:32;::::1;;;-1:-1:-1::0;9357:14:0;;9339:32:::1;9335:70;;;9380:25;;-1:-1:-1::0;;;9380:25:0::1;;;;;;;;;;;9335:70;9421:26;9449;9479:19;7524:17:38::0;;-1:-1:-1;;;;;7524:17:38;;;;-1:-1:-1;;;7543:17:38;;;;;7438:130;9479:19:0::1;-1:-1:-1::0;;;;;9420:78:0::1;;;-1:-1:-1::0;;;;;9420:78:0::1;;;9983:77;10027:15;10044;9983:43;:77::i;:::-;9879;9923:15;9940;9879:43;:77::i;:::-;:181;;;;:::i;:::-;9702:154;9753:9;9764:16;9782:15;9799:18;9819:15;9702:25;:154::i;:::-;9533:146;9580:9;9591:16;9609:15;9626:18;9646:15;9533:25;:146::i;:::-;:323;;;;:::i;:::-;:527;9512:606;;;10100:3;;-1:-1:-1::0;;;10100:3:0::1;;;;;;;;;;;9512:606;-1:-1:-1::0;;10260:66:0::1;::::0;;15395:25:195;;;15451:2;15436:18;;15429:34;;;15479:18;;;15472:34;;;15537:2;15522:18;;15515:34;;;-1:-1:-1;;;;;10260:66:0;::::1;::::0;-1:-1:-1;10265:10:0::1;::::0;-1:-1:-1;10260:66:0::1;::::0;-1:-1:-1;15382:3:195;15367:19;;-1:-1:-1;10260:66:0::1;;;;;;;10337:98;10382:10:::0;10352:27:::1;10370:9:::0;10352:15;:27:::1;:::i;:::-;:40;;;;:::i;:::-;10424:10:::0;10394:27:::1;10412:9:::0;10394:15;:27:::1;:::i;:::-;:40;;;;:::i;:::-;10337:14;:98::i;:::-;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;;;;;;;7682:2760:0:o;45835:752::-;46109:20;;26839:21:21;-1:-1:-1;;;46109:20:0;;;;;26839:15:21;:21;;;46090:39:0;46149:46;46163:8;46090:39;46149:13;:46::i;:::-;46205:36;46222:8;46232;46205:16;:36::i;:::-;46317:4;-1:-1:-1;;;;;46309:23:0;;;46305:276;;46349:41;46392:14;46410:28;46425:6;46433:4;46410:14;:28::i;:::-;46348:90;;;;46456:9;:21;;;;46469:8;46456:21;46452:119;;;46497:59;;-1:-1:-1;;;46497:59:0;;-1:-1:-1;;;;;46497:31:0;:38;;;;:59;;46536:11;;46549:6;;46497:59;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;46452:119;46334:247;;46305:276;45919:668;45835:752;;;:::o;6717:282:38:-;6890:14;;-1:-1:-1;;;;;6890:14:38;;;;-1:-1:-1;;;6932:14:38;;;;;;-1:-1:-1;;;6973:19:38;;;;;6717:282::o;7005:427::-;7050:13;7065;7091:23;7116;7144:13;:11;:13::i;:::-;7090:67;;;;;7167:19;7189:61;7204:15;-1:-1:-1;;;;;7189:61:38;-1:-1:-1;;;7227:15:38;-1:-1:-1;;;;;7189:61:38;7244:5;7189:14;:61::i;:::-;7167:83;;7260:17;7280:36;7304:11;7280:23;:36::i;:::-;7347:78;;-1:-1:-1;;;7347:78:38;;7400:4;7347:78;;;16964:51:195;7420:4:38;17051:21:195;;;17031:18;;;17024:49;17089:18;;;17082:50;7260:56:38;;-1:-1:-1;7347:31:38;-1:-1:-1;;;;;7347:44:38;;;;16937:18:195;;7347:78:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;7326:99;;;;-1:-1:-1;7005:427:38;-1:-1:-1;;;;;7005:427:38:o;5867:314::-;6070:104;;;;;;;;-1:-1:-1;;;;;6071:14:38;6070:104;;;;6087:14;6070:104;;;;;;6103:14;6070:104;;;;;;;;;6119:13;6070:104;;;;;;6134:13;6070:104;;;;;;6149:13;6070:104;;;;;;;5946:14;;6164:9;6070:104;;;;;;;:::i;:::-;;;;;;5867:314;-1:-1:-1;;5867:314:38:o;15539:631:0:-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;;;2171:1;15873:50:::1;2171:1:::0;15873:36:::1;:50::i;:::-;15818:105;;;;;;15934:15;15952:52;15973:2;279:1:19;15988:15:0;15952:20;:52::i;:::-;15934:70;;16014:15;16032:52;16053:2;311:1:19;16068:15:0;16032:20;:52::i;:::-;16014:70;;16095:36;16110:2;16114:7;16123;16095:14;:36::i;:::-;16142:21;:19;:21::i;:::-;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;;;15539:631:0:o;4516:213:38:-;3442:18;:16;:18::i;:::-;4631:43:::1;::::0;-1:-1:-1;;;;;4935:43:195;;4917:62;;4631:43:38::1;::::0;4905:2:195;4890:18;4631:43:38::1;;;;;;;4684:17;:38:::0;;-1:-1:-1;;;;;;4684:38:38::1;-1:-1:-1::0;;;;;4684:38:38;;;::::1;::::0;;;::::1;::::0;;4516:213::o;2321:2731:0:-;2384:23;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;;;2784:20:::1;::::0;2171:1;;;;;;;;;;-1:-1:-1;;;;;2784:20:0::1;:25:::0;;2780:1704:::1;;2862:20;2877:1;2880;2862:14;:20::i;:::-;2825:57:::0;;-1:-1:-1;2825:57:0;-1:-1:-1;2896:29:0::1;3003:44;3013:33;2825:57:::0;;3013:33:::1;:::i;:::-;3003:9;:44::i;:::-;-1:-1:-1::0;;;;;2967:81:0::1;-1:-1:-1::0;;;2967:81:0;::::1;-1:-1:-1::0;;;;;;2944:104:0;;::::1;2967:25;2944:104:::0;2967:81;-1:-1:-1;3098:41:0::1;3108:4:30;2967:81:0::0;3098:41:::1;:::i;:::-;3080:59;;;3062:77;;3194:47;3208:15;3225;3194:13;:47::i;:::-;3154:17;3153:88:::0;;-1:-1:-1;;;;;;3153:88:0;-1:-1:-1;;;;;;;;3153:88:0;;::::1;;-1:-1:-1::0;;;;;;3153:88:0;;;;;::::1;::::0;;;::::1;::::0;;3256:85:::1;-1:-1:-1::0;3274:10:0::1;3294:7;3108:4:30;::::0;3256:6:0::1;:85::i;:::-;3410:15;3428:86;3452:61;3467:15;-1:-1:-1::0;;;3490:15:0::1;3507:5;3452:14;:61::i;3428:86::-;3528:47;::::0;-1:-1:-1;;;3528:47:0;;8894:1:195;8883:21;;;3528:47:0::1;::::0;::::1;8865:40:195::0;3410:104:0;;-1:-1:-1;3528:31:0::1;-1:-1:-1::0;;;;;3528:36:0::1;::::0;::::1;::::0;8838:18:195;;3528:47:0::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3589:23;3615:35;26839:21:21::0;:15;:21;;26678:200;3615:35:0::1;3664:19;:38:::0;;::::1;::::0;;::::1;-1:-1:-1::0;;;3664:38:0::1;-1:-1:-1::0;;;;;3664:38:0;;::::1;::::0;::::1;::::0;;;3716:20:::1;:39:::0;;;::::1;::::0;::::1;::::0;;3769:20:::1;:39:::0;;;;::::1;;::::0;;-1:-1:-1;2780:1704:0::1;::::0;-1:-1:-1;;2780:1704:0::1;;3906:40;3943:2;3906:36;:40::i;:::-;3839:107:::0;;-1:-1:-1;3839:107:0;;-1:-1:-1;3839:107:0;-1:-1:-1;3839:107:0;-1:-1:-1;3961:28:0::1;4052:45;:43;:45::i;:::-;4003:94:::0;-1:-1:-1;4003:94:0;;-1:-1:-1;4148:325:0::1;::::0;-1:-1:-1;4202:13:0;4233;4264:15;4297;4003:94;;4407:22:::1;247:1:19;4407:11:0;:22::i;:::-;4447:12:::0;4148:36:::1;:325::i;:::-;4111:362:::0;-1:-1:-1;4111:362:0;-1:-1:-1;;2780:1704:0::1;4554:15;4573:1;4554:20:::0;4550:62:::1;;4583:29;;-1:-1:-1::0;;;4583:29:0::1;;;;;;;;;;;4550:62;4623:67;247:1:19;4641:10:0;4653:2;4657:15;4674;4623:6;:67::i;:::-;4782:148;4822:15:::0;4839;4856:31:::1;4874:13:::0;4822:15;4856:31:::1;:::i;:::-;4889;4907:13:::0;4889:15;:31:::1;:::i;:::-;4782:26;:148::i;:::-;5001:44;5035:2;5039:5;5001:33;:44::i;:::-;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;2321:2731:0;;;-1:-1:-1;;;;2321:2731:0:o;19795:2670::-;19935:7;19944;2143;:5;:7::i;:::-;2171:1;2160:8;:12;;;2171:1;20018:40:::1;20055:2:::0;20018:36:::1;:40::i;:::-;19963:95;;;;;;20069:27;20106:24;20140;20188:27;20218:24;342:1:19;20218:14:0;:24::i;:::-;-1:-1:-1::0;;;;;20188:54:0::1;;;20257:634;20302:575;;;;;;;;;;;;;;;;20397:25;247:1:19;20397:14:0;:25::i;:::-;-1:-1:-1::0;;;;;20302:575:0::1;;;;;20448:25;279:1:19;20448:14:0;:25::i;:::-;-1:-1:-1::0;;;;;20302:575:0::1;;;;;20499:25;311:1:19;20499:14:0;:25::i;:::-;-1:-1:-1::0;;;;;20302:575:0::1;;;;;20550:19;20302:575;;;;20595:24;373:1:19;20595:14:0;:24::i;:::-;-1:-1:-1::0;;;;;20302:575:0::1;;;;;20645:24;404:1:19;20645:14:0;:24::i;:::-;-1:-1:-1::0;;;;;20302:575:0::1;;;::::0;::::1;;;;20733:19;20302:575;;;;20790:15;20302:575;;;;20843:15;20302:575;;::::0;20257:27:::1;:634::i;:::-;20980:31;21014:60;21041:15;21058;21014:26;:60::i;:::-;20980:94;;21111:84;21126:19;21147:15;21164:23;21189:5;21111:14;:84::i;:::-;21092:103;;21232:84;21247:19;21268:15;21285:23;21310:5;21232:14;:84::i;:::-;21213:103;;20906:425;21383:94;21400:19;21421;21442:21;342:1:19;21442:11:0;:21::i;:::-;475:4:19;21383:16:0;:94::i;:::-;21345:132;;21552:74;342:1:19;21569:10:0;21581:2;21585:19;21606;21552:6;:74::i;:::-;20174:1463;21647:54;21662:2;21666:16;21684;21647:14;:54::i;:::-;21948:154;21988:15:::0;22005;22022:34:::1;22040:16:::0;21988:15;22022:34:::1;:::i;:::-;22058;22076:16:::0;22058:15;:34:::1;:::i;21948:154::-;22117:15:::0;;22113:246:::1;;22159:1;22148:8;:12;;;;22184:2;-1:-1:-1::0;;;;;22174:43:0::1;;22235:10;22247:16;22265;22283:19;22304:4;;22174:148;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;22347:1:0::1;22336:8;:12:::0;-1:-1:-1;;22113:246:0::1;22369:35;22386:10;22398:5;22369:16;:35::i;:::-;2204:1:::0;2193:8;:12;22423:16;;;;-1:-1:-1;19795:2670:0;-1:-1:-1;;;;;;;;19795:2670:0:o;5161:2412::-;5224:21;5247;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;;;2171:1;5482:50:::1;2171:1:::0;5482:36:::1;:50::i;:::-;5427:105;;;;;;5543:23;5569:35;5587:4;247:1:19;5569:9:0;:35::i;:::-;5543:61;;5615:30;5648:29;5681:45;:43;:45::i;:::-;5614:112;;;;;5736:30;5769:22;247:1:19;5769:11:0;:22::i;:::-;5736:55:::0;-1:-1:-1;5802:29:0::1;5846:95;5863:15:::0;5880:22;5736:55;5802:29;5846:16:::1;:95::i;:::-;5802:139;;5968:152;6007:15;6024;6041:21;6064:22;6088;5968:25;:152::i;:::-;5952:168;;6146:152;6185:15;6202;6219:21;6242:22;6266;6146:25;:152::i;:::-;6130:168:::0;-1:-1:-1;6369:18:0;;;:40:::1;;-1:-1:-1::0;6391:18:0;;6369:40:::1;6365:107;;;6432:29;;-1:-1:-1::0;;;6432:29:0::1;;;;;;;;;;;6365:107;6522:19;6544:31;6562:13:::0;6544:15;:31:::1;:::i;:::-;6522:53:::0;-1:-1:-1;6585:19:0::1;6607:31;6625:13:::0;6607:15;:31:::1;:::i;:::-;6690:527;::::0;;;;;;;;6585:53;;-1:-1:-1;6649:578:0::1;::::0;6690:527;::::1;::::0;::::1;::::0;6777:46:::1;6802:21:::0;6777:22;:46:::1;:::i;:::-;6690:527;;;;6845:25;279:1:19;6845:14:0;:25::i;:::-;-1:-1:-1::0;;;;;6690:527:0::1;;;;;6892:25;311:1:19;6892:14:0;:25::i;:::-;-1:-1:-1::0;;;;;6690:527:0::1;;;;;6939:24;342:1:19;6939:14:0;:24::i;:::-;-1:-1:-1::0;;;;;6690:527:0::1;;;;;6985:24;373:1:19;6985:14:0;:24::i;:::-;-1:-1:-1::0;;;;;6690:527:0::1;;;;;7031:24;404:1:19;7031:14:0;:24::i;:::-;-1:-1:-1::0;;;;;6690:527:0::1;::::0;;;;7111:1:::1;6690:527;::::0;::::1;::::0;;;;;;;;;;;;6649:27:::1;:578::i;:::-;7238:73;247:1:19;7256:10:0;7268:2;7272:21;7295:15;7238:6;:73::i;:::-;7322:48;7337:2;7341:13;7356;7322:14;:48::i;:::-;7480:86;7507:15;7524;7541:11;7554;7480:26;:86::i;:::-;5270:2303;;;;;;;;;2204:1:::0;2193:8;:12;;;;5161:2412;;;:::o;17371:1200::-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;;;2171:1;17545:40:::1;17582:2:::0;17545:36:::1;:40::i;:::-;17490:95;;;;;;17596:57;17656:280;;;;;;;;17713:1;17656:280;;;;17745:1;17656:280;;;;17776:1;17656:280;;;;17800:1;17656:280;;;;17837:25;247:1:19;17837:14:0;:25::i;:::-;-1:-1:-1::0;;;;;17656:280:0::1;;;;;17901:24;342:1:19;17901:14:0;:24::i;:::-;-1:-1:-1::0;;;;;17656:280:0::1;::::0;;17596:340;-1:-1:-1;17947:21:0::1;17971:86;17596:340:::0;18001:2;18005:13;18020:15;373:1:19::1;279;17971:12:0;:86::i;:::-;17947:110;;18067:21;18091:86;18104:15;18121:2;18125:13;18140:15;404:1:19;311;18091:12:0;:86::i;:::-;18067:110;;18188:48;18203:2;18207:13;18222;18188:14;:48::i;:::-;18247:21;:19;:21::i;:::-;18283:15:::0;;18279:240:::1;;18325:1;18314:8;:12:::0;18340:142:::1;::::0;-1:-1:-1;;;18340:142:0;;-1:-1:-1;;;;;18340:34:0;::::1;::::0;::::1;::::0;:142:::1;::::0;18392:10:::1;::::0;18404:13;;18419;;18434;;18449;;18464:4;;;;18340:142:::1;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;18507:1:0::1;18496:8;:12:::0;-1:-1:-1;;18279:240:0::1;18529:35;18546:10;18558:5;18529:16;:35::i;:::-;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;;;;;;;;17371:1200:0:o;22471:164::-;22543:23;22568;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;22610:18:::1;22617:10:::0;22610:6:::1;:18::i;:::-;2204:1:::0;2193:8;:12;22603:25;;;;-1:-1:-1;22471:164:0;-1:-1:-1;;22471:164:0:o;41555:225::-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;;;2171:1;41673:40:::1;41710:2:::0;41673:36:::1;:40::i;:::-;41619:94;;;;;;41723:50;41738:2;41742:14;41758;41723;:50::i;:::-;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;41555:225:0:o;3631:114:38:-;3723:6;;3731;;3631:114::o;24716:216:0:-;24797:25;24824;24851:28;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;24898:27:::1;24914:10:::0;24898:15:::1;:27::i;:::-;24891:34;;;;;;2204:1:::0;2193:8;:12;24716:216;;;;-1:-1:-1;24716:216:0:o;28422:2410::-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;28834:46:::1;28871:8:::0;28834:36:::1;:46::i;:::-;;;;;28928:41;28971:14;28989:31;29004:8;29014:5;28989:14;:31::i;:::-;28927:93;;;;29034:9;29030:1796;;;29063:15:::0;29059:1331:::1;;29118:709;29153:8;29183:2;29207:11;29240:569;;;;;;;;29334:32;29240:569;;;;29426:32;29240:569;;;;29518:32;29240:569;;;;29594:16;29240:569;;;;29654:16;29240:569;;;;29713:15;29240:569;;;;29771:15;29240:569;;::::0;29118:13:::1;:709::i;:::-;29059:1331;;;1451:1:23;29852:15:0;:35:::0;29848:542:::1;;29907:280;29942:11;29975:8;30005:2;30029:32;30083;30137;29907:13;:280::i;29848:542::-;1495:1:23;30212:15:0;:39:::0;30208:182:::1;;30271:104;30289:11;30302:8;30312:2;30320:32;30316:1;:36;30358:16;30354:1;:20;30271:17;:104::i;:::-;30429:386;::::0;;19104:25:195;;;19160:2;19145:18;;19138:34;;;19188:18;;;19181:34;;;19246:2;19231:18;;19224:34;;;19289:3;19274:19;;19267:35;;;19333:3;19318:19;;19311:35;;;19377:3;19362:19;;19355:35;;;19421:3;19406:19;;19399:35;;;-1:-1:-1;;;;;30429:386:0;;::::1;::::0;;;::::1;::::0;::::1;::::0;19091:3:195;19076:19;30429:386:0::1;;;;;;;29030:1796;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;;;;;;;;;;28422:2410:0:o;13566:1346::-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;;;2171:1;;;13744:40:::1;13781:2:::0;13744:36:::1;:40::i;:::-;13633:151;;;;;;;;-1:-1:-1::0;;;;;13799:33:0;::::1;:13;:33;:70;;;-1:-1:-1::0;;;;;;13836:33:0;::::1;13799:70;13795:118;;;13892:10;;-1:-1:-1::0;;;13892:10:0::1;;;;;;;;;;;13795:118;13974:21;13998:23;14008:2;373:1:19;13998:9:0;:23::i;:::-;13974:47;;14031:21;14055:23;14065:2;404:1:19;14055:9:0;:23::i;:::-;14031:47;;14089:109;14139:13;14154;14169;14184;14089:49;:109::i;:::-;14209:23;14234;14261:15;18479:14:38::0;;-1:-1:-1;;;;;18479:14:38;;;;-1:-1:-1;;;18495:14:38;;;;;18395:122;14261:15:0::1;-1:-1:-1::0;;;;;14208:68:0::1;;;-1:-1:-1::0;;;;;14208:68:0::1;;;14286:23;14312:83;279:1:19;14343:13:0;14358:15;14375;14392:2;14312:19;:83::i;:::-;14286:109;;14405:23;14431:83;311:1:19;14462:13:0;14477:15;14494;14511:2;14431:19;:83::i;:::-;14405:109;;14546:1;14528:15;:19;:42;;;;14569:1;14551:15;:19;14528:42;14524:233;;;14586:160;14630:15:::0;14647;14664:33:::1;14682:15:::0;14630;14664:33:::1;:::i;:::-;14699;14717:15:::0;14699;:33:::1;:::i;14586:160::-;14766:21;:19;:21::i;:::-;14861:44;14895:2;14899:5;14861:33;:44::i;:::-;-1:-1:-1::0;;2204:1:0;2193:8;:12;-1:-1:-1;;;;;;;;;13566:1346:0:o;41889:54::-;2143:7;:5;:7::i;:::-;2171:1;2160:8;:12;41929:7:::1;:5;:7::i;:::-;2204:1:::0;2193:8;:12;41889:54::o;1908:204:20:-;1997:14;2032:5;2036:1;2032;:5;:::i;:::-;2023:14;;2056:10;:49;;2095:10;2104:1;2095:6;:10;:::i;:::-;2056:49;;;2069:23;2082:6;2090:1;2069:12;:23::i;:::-;2047:58;1908:204;-1:-1:-1;;;;;1908:204:20:o;1966:3501:26:-;2048:5;821:7;2069:11;:31;:66;;;;2124:11;-1:-1:-1;;;2104:31:26;2069:66;2065:97;;;2144:18;;-1:-1:-1;;;2144:18:26;;;;;;;;;;;2065:97;-1:-1:-1;;;;;2271:41:26;;2268:1;2264:49;2361:9;;;2434:18;2428:25;;2425:1;2421:33;2502:9;;;2575:10;2569:17;;2566:1;2562:25;2635:9;;;2708:6;2702:13;;2699:1;2695:21;2764:9;;;2837:4;2831:11;;2828:1;2824:19;;;2891:9;;;2964:3;2958:10;;2955:1;2951:18;3017:9;;;3084:10;;;3081:1;3077:18;;;3143:9;;;;3203:10;;;2474;;2607;;;2736;;;2863;2989;;;3115;3233;2173:9;3323:3;3316:10;;3312:95;;3354:3;3348;:9;3332:11;:26;;3328:30;;3312:95;;;3403:3;3397;:9;3381:11;:26;;3377:30;;3312:95;-1:-1:-1;3515:9:26;;;3510:3;3506:19;;;3547:11;;;;3625:9;;;;3690;;3681:19;;;3722:11;;;3800:9;3865;;3856:19;;;3897:11;;;3975:9;4040;;4031:19;;;4072:11;;;4150:9;4215;;4206:19;;;4247:11;;;4325:9;4390;;4381:19;;;4422:11;;;4500:9;4565;;4556:19;;;4597:11;;;4675:9;4740;;4731:19;;;4772:11;;;4850:9;4915;;4906:19;;;;4947:11;;;;5025:9;;;;;3515;-1:-1:-1;;3433:17:26;;3455:2;3432:25;3596:10;;;;;;;3583:24;3771:10;;;;;;;3758:24;;;;3946:10;;;;;;;3933:24;;;;4121:10;;;;;;;4108:24;;;;4296:10;;;;;;;4283:24;4471:10;;;;;;;4458:24;4646:10;;;;;;;4633:24;4821:10;;;;;;;4808:24;4996:10;;;;;;;4983:24;550:20;5092:39;;-1:-1:-1;;5168:40:26;;3447:3;5167:49;;;;734:34;5253:39;;5252:48;;5319:17;;;;;;;;;5315:37;;-1:-1:-1;5345:7:26;1966:3501;-1:-1:-1;;;;;;1966:3501:26:o;5315:37::-;5396:11;5370:22;5385:6;5370:14;:22::i;:::-;:37;5366:56;;5416:6;1966:3501;-1:-1:-1;;;;;;;1966:3501:26:o;5366:56::-;-1:-1:-1;5443:7:26;1966:3501;-1:-1:-1;;;;;;1966:3501:26:o;21409:281:38:-;21540:31;21613:70;21650:15;21667;21613:36;:70::i;:::-;21583:100;21409:281;-1:-1:-1;;;;21409:281:38:o;2006:105:0:-;2050:8;;2062:1;2050:13;2046:59;;2086:8;;-1:-1:-1;;;2086:8:0;;;;;;;;;;;2046:59;2006:105::o;44736:282::-;44991:19;;26839:21:21;:15;:21;;;44902:109:0;;44920:15;;44937;;26839:21:21;;44972:38:0;;-1:-1:-1;;;44991:19:0;;;;26839:21:21;44972:38:0;:::i;:::-;44902:17;:109::i;:::-;44821:197;44736:282;;:::o;48578:439::-;48679:14;48695;48713:18;:16;:18::i;:::-;48678:53;;;;48759:7;-1:-1:-1;;;;;48745:22:0;:2;-1:-1:-1;;;;;48745:22:0;;:48;;;;48785:7;-1:-1:-1;;;;;48771:22:0;:2;-1:-1:-1;;;;;48771:22:0;;48745:48;48741:104;;;48816:18;;-1:-1:-1;;;48816:18:0;;;;;;;;;;;48741:104;48858:17;;48854:73;;48877:50;48900:7;48909:2;48913:13;48877:22;:50::i;:::-;48941:17;;48937:73;;48960:50;48983:7;48992:2;48996:13;48960:22;:50::i;:::-;48668:349;;48578:439;;;:::o;17916:473:38:-;18035:7;18044;18064:14;18080;18098:18;:16;:18::i;:::-;18063:53;;;;18238:15;18210:25;279:1:19;18210:14:38;:25::i;:::-;-1:-1:-1;;;;;18148:87:38;18183:24;373:1:19;18183:14:38;:24::i;:::-;18148:32;;-1:-1:-1;;;18148:32:38;;18174:4;18148:32;;;3876:51:195;-1:-1:-1;;;;;18148:59:38;;;;;-1:-1:-1;;;;;18148:17:38;;;;;3849:18:195;;18148:32:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:59;;;;:::i;:::-;:87;;;;:::i;:::-;:105;;;;:::i;:::-;18357:15;18329:25;311:1:19;18329:14:38;:25::i;:::-;-1:-1:-1;;;;;18267:87:38;18302:24;404:1:19;18302:14:38;:24::i;:::-;18267:32;;-1:-1:-1;;;18267:32:38;;18293:4;18267:32;;;3876:51:195;-1:-1:-1;;;;;18267:59:38;;;;;-1:-1:-1;;;;;18267:17:38;;;;;3849:18:195;;18267:32:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:59;;;;:::i;:::-;:87;;;;:::i;:::-;:105;;;;:::i;:::-;18127:255;;;;;;17916:473;;;;;;:::o;10683:268:0:-;10815:16;10857:19;10867:9;10857:7;:19;:::i;:::-;10847:7;:29;10843:102;;;10914:19;10924:9;10914:7;:19;:::i;:::-;10903:31;;:7;:31;:::i;:::-;10892:42;;10843:102;10683:268;;;;;:::o;13190:370::-;13321:25;13397:26;1641:3;13397:7;:26;:::i;:::-;13378:16;1481:2;13378:7;:16;:::i;:::-;:45;:154;;13508:24;1532:1;13508:7;:24;:::i;:::-;13378:154;;;1641:3;13439:17;13449:7;13439;:17;:::i;:::-;13438:38;;;;:::i;11792:871::-;11990:25;12027:11;12041:78;12083:8;12093:7;12102:16;12041:41;:78::i;:::-;12027:92;-1:-1:-1;12153:26:0;1641:3;12153:7;:26;:::i;:::-;12134:16;1481:2;12134:7;:16;:::i;:::-;:45;12130:527;;;12244:174;1641:3;12326:14;12337:3;12326:8;:14;:::i;:::-;-1:-1:-1;;;12276:17:0;12286:7;12276;:17;:::i;:::-;12275:48;;;;:::i;:::-;:65;;;;:::i;:::-;12274:86;;;;:::i;:::-;-1:-1:-1;;;12244:12:0;:174::i;:::-;12224:194;;12130:527;;;12502:144;1532:1;12572:14;12583:3;12572:8;:14;:::i;:::-;12533:36;-1:-1:-1;;;12533:7:0;:36;:::i;12502:144::-;12482:164;;12130:527;12017:646;11792:871;;;;;;;:::o;16687:340:38:-;16785:22;16809;16835:51;16849:17;16868;16835:13;:51::i;:::-;16896:14;:31;;-1:-1:-1;;;;;16896:31:38;;;-1:-1:-1;;;;;;16937:31:38;;;;;-1:-1:-1;;;16937:31:38;;;;;;;;;;16984:36;;;3048:62:195;;;3141:2;3126:18;;3119:71;;;;16896:31:38;;-1:-1:-1;16937:31:38;;-1:-1:-1;16984:36:38;;3021:18:195;16984:36:38;;;;;;;16774:253;;16687:340;;:::o;10409:2305::-;-1:-1:-1;;;;;10502:23:38;;;;;:52;;;10553:1;10529:21;:25;;;10502:52;10498:2210;;;10803:9;:20;10864:19;;;10775:25;10925:20;;10803;10986:19;11319:17;;11245:183;;-1:-1:-1;;;11245:183:38;;-1:-1:-1;;;;;20951:32:195;;;11245:183:38;;;20933:51:195;;;;-1:-1:-1;;;;;11319:17:38;;;21000:18:195;;;20993:71;21112:10;21100:23;;21080:18;;;21073:51;-1:-1:-1;;;;;10803:20:38;;;21140:18:195;;;21133:34;;;-1:-1:-1;;;10864:19:38;;;;;;21183::195;;;21176:35;;;-1:-1:-1;;;10986:19:38;;;;;21227::195;;;21220:35;;;10803:20:38;;10864:19;;10925:20;;;10986:19;10775:25;;;11245:31;:47;;;;;;20905:19:195;;11245:183:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;11154:274:38;;;-1:-1:-1;;;;;11154:274:38;;;11545:36;11600:96;11617:27;11646:17;11665;475:4:19;11600:16:38;:96::i;:::-;11545:151;;11847:105;342:1:19;11872:4:38;11887;11894:28;11924:27;11847:6;:105::i;:::-;11990:48;12009:28;11990:18;:48::i;:::-;11966:9;:72;;247:1:19;;11966:72:38;;;;-1:-1:-1;;;;;11966:72:38;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;11966:72:38;;;;;-1:-1:-1;;;;;11966:72:38;;;;;;12251:29;12247:1;:33;12243:285;;;12300:16;342:1:19;12300:6:38;:16::i;:::-;12456:38;;-1:-1:-1;;;12456:38:38;;12360:4;12456:38;;;3876:51:195;;;-1:-1:-1;;;;;12300:30:38;;;;;;12387:7;;12416:79;;12425:29;;12456:13;:23;;;;3849:18:195;;12456:38:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;12416:8;:79::i;:::-;12300:213;;-1:-1:-1;;;;;;12300:213:38;;;;;;;-1:-1:-1;;;;;22121:32:195;;;12300:213:38;;;22103:51:195;22190:32;;;;22170:18;;;22163:60;22239:18;;;22232:34;22076:18;;12300:213:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;12243:285;-1:-1:-1;;12639:20:38;:58;;-1:-1:-1;;;;;12639:58:38;26839:15:21;:21;;-1:-1:-1;;;12639:58:38;;;;-1:-1:-1;;;;;10498:2210:38;10409:2305;;:::o;46593:536:0:-;46795:4;-1:-1:-1;;;;;46787:25:0;;;46783:340;;46829:41;46872:14;46890:30;46905:8;46915:4;46890:14;:30::i;:::-;46828:92;;;;46938:9;:21;;;;46951:8;46938:21;46934:179;;;46979:40;47007:11;46979:27;:40::i;:::-;47037:61;;-1:-1:-1;;;47037:61:0;;-1:-1:-1;;;;;47037:31:0;:38;;;;:61;;47076:11;;47089:8;;47037:61;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;46814:309;;46593:536;;:::o;47464:1108::-;47577:41;;:::i;:::-;47620:14;47646:31;47680:13;:11;:13::i;:::-;47646:47;;47703:28;47734:33;47744:13;47759:7;47734:9;:33::i;:::-;47846:20;;;;47703:64;;-1:-1:-1;47846:25:0;;;:54;;-1:-1:-1;47875:20:0;;;;:25;;47846:54;:83;;;-1:-1:-1;47904:20:0;;;;:25;;47846:83;47834:95;;47944:9;47939:72;;47969:31;;;;47939:72;48022:23;48047;48075:13;:11;:13::i;:::-;48021:67;-1:-1:-1;;;;;48021:67:0;;;-1:-1:-1;;;;;48021:67:0;;;48100:13;48115;48132:31;-1:-1:-1;;;;;48132:44:0;;48198:4;48217:86;48241:61;48256:15;-1:-1:-1;;;48279:15:0;48296:5;48241:14;:61::i;48217:86::-;48132:215;;-1:-1:-1;;;;;;48132:215:0;;;;;;;-1:-1:-1;;;;;16982:32:195;;;48132:215:0;;;16964:51:195;17011:1;17051:21;17031:18;;;17024:49;17116:14;;17109:22;17089:18;;;17082:50;16937:18;;48132:215:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;48483:17;;48099:248;;-1:-1:-1;48099:248:0;-1:-1:-1;48379:153:0;;48422:13;;48437:10;;48449:15;;48466;;-1:-1:-1;;;;;48483:17:0;48099:248;;48379:25;:153::i;:::-;48358:207;;;;;;;;47464:1108;;;;;:::o;43240:1439::-;43358:23;43383;43408:22;43432;43470:21;43554:13;:11;:13::i;:::-;43956:20;;44033;;-1:-1:-1;;;;;43501:66:0;;;;-1:-1:-1;43501:66:0;;;;;-1:-1:-1;43501:66:0;-1:-1:-1;26839:21:21;:15;:21;;;43866:33:0;;;;-1:-1:-1;;;43956:20:0;;;;;;43937:39;;;44033:20;;;;44014:39;;44096:53;44110:15;44014:39;44096:13;:53::i;:::-;44180:91;44198:15;44215;44232:16;44250:20;44180:17;:91::i;:::-;44317:48;44332:15;44349;44317:14;:48::i;:::-;44282:83;;-1:-1:-1;44282:83:0;-1:-1:-1;44436:25:0;;;;44432:241;;44514:148;44553:16;44571:20;44593:21;44616:15;44633;44514:21;:148::i;:::-;-1:-1:-1;;;;;44477:185:0;;;;-1:-1:-1;44477:185:0;;-1:-1:-1;44432:241:0;43460:1219;;;;;43240:1439;;;;;:::o;16176:1167::-;16311:23;16346;16372:44;16390:4;16397:18;16372:9;:44::i;:::-;16346:70;;16486:15;16505:1;16486:20;16482:34;;16515:1;16508:8;;;;;16482:34;16526:21;16550:34;16565:18;16550:14;:34::i;:::-;-1:-1:-1;;;;;16526:58:0;;;16594:20;16617:31;16629:18;16617:11;:31::i;:::-;16594:54;-1:-1:-1;16677:76:0;16694:15;16711:13;16594:54;16740:12;16677:16;:76::i;:::-;16659:94;;16764:57;16824:369;;;;;;;;16881:1;16824:369;;;;16929:15;16913:13;:31;;;;:::i;:::-;16824:369;;;;16974:53;16989:37;443:1:19;16989:18:0;:37;:::i;:::-;16974:14;:53::i;:::-;-1:-1:-1;;;;;16824:369:0;;;;;17050:8;16824:369;;;;17094:25;247:1:19;17094:14:0;:25::i;:::-;-1:-1:-1;;;;;16824:369:0;;;;;17158:24;342:1:19;17158:14:0;:24::i;:::-;-1:-1:-1;;;;;16824:369:0;;;16764:429;-1:-1:-1;17204:45:0;16764:429;17204:28;:45::i;:::-;17260:76;17267:18;17287:10;17299:2;17303:15;17320;17260:6;:76::i;:::-;16336:1007;;;;16176:1167;;;;;:::o;18523:699:38:-;18573:22;18598:25;279:1:19;18598:14:38;:25::i;:::-;-1:-1:-1;;;;;18573:50:38;;;18633:22;18658:25;311:1:19;18658:14:38;:25::i;:::-;-1:-1:-1;;;;;18633:50:38;;;18693:21;18717:24;373:1:19;18717:14:38;:24::i;:::-;-1:-1:-1;;;;;18693:48:38;;;18751:21;18775:24;404:1:19;18775:14:38;:24::i;:::-;-1:-1:-1;;;;;18751:48:38;;;19060:14;19044:13;:30;:67;;19110:1;19044:67;;;19077:30;19093:14;19077:13;:30;:::i;:::-;19019:14;:93;;-1:-1:-1;;;;;;19019:93:38;-1:-1:-1;;;;;19019:93:38;;;;;;;;;;19147:30;;;:67;;19213:1;19147:67;;;19180:30;19196:14;19180:13;:30;:::i;:::-;19122:14;;:93;;;;;-1:-1:-1;;;;;19122:93:38;;;;;-1:-1:-1;;;;;19122:93:38;;;;;;18563:659;;;;18523:699::o;3484:141::-;3553:7;-1:-1:-1;;;;;3553:19:38;;:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;3539:35:38;:10;-1:-1:-1;;;;;3539:35:38;;3535:84;;3597:11;;-1:-1:-1;;;3597:11:38;;;;;;;;;;;20567:5181:89;20615:7;20733:1;20728;:6;20724:53;;-1:-1:-1;20761:1:89;20567:5181::o;20724:53::-;21717:1;21745;-1:-1:-1;;;21765:16:89;;21761:92;;21808:3;21801:10;;;;;21836:2;21829:9;21761:92;21877:7;21870:2;:15;21866:90;;21912:2;21905:9;;;;;21939:2;21932:9;21866:90;21980:7;21973:2;:15;21969:90;;22015:2;22008:9;;;;;22042:2;22035:9;21969:90;22083:7;22076:2;:15;22072:89;;22118:2;22111:9;;;;;22145:1;22138:8;22072:89;22185:6;22178:2;:14;22174:87;;22219:1;22212:8;;;;;22245:1;22238:8;22174:87;22285:6;22278:2;:14;22274:87;;22319:1;22312:8;;;;;22345:1;22338:8;22274:87;22385:6;22378:2;:14;22374:61;;22419:1;22412:8;22374:61;22861:1;:6;22872:1;22860:13;;;;;24771:1;22860:13;24771:6;;;;:::i;:::-;;24766:2;:11;24765:18;;24760:23;;24891:1;24884:2;24880:1;:6;;;;;:::i;:::-;;24875:2;:11;24874:18;;24869:23;;25002:1;24995:2;24991:1;:6;;;;;:::i;:::-;;24986:2;:11;24985:18;;24980:23;;25111:1;25104:2;25100:1;:6;;;;;:::i;:::-;;25095:2;:11;25094:18;;25089:23;;25221:1;25214:2;25210:1;:6;;;;;:::i;:::-;;25205:2;:11;25204:18;;25199:23;;25331:1;25324:2;25320:1;:6;;;;;:::i;:::-;;25315:2;:11;25314:18;;25309:23;;25703:28;25728:2;25724:1;:6;;;;;:::i;:::-;;25719:11;;;34795:145:90;25703:28:89;25698:33;;;20567:5181;-1:-1:-1;;;20567:5181:89:o;17549:310:38:-;17645:7;17654;17673:22;17698:35;17717:15;17698:18;:35::i;:::-;17673:60;;17743:22;17768:35;17787:15;17768:18;:35::i;:::-;17821:14;;;;-1:-1:-1;17549:310:38;;-1:-1:-1;;;;17549:310:38:o;4735:507::-;4874:27;4893:7;4874:18;:27::i;:::-;4850:9;4860;4850:20;;;;;;;:::i;:::-;;;;;;;;;;;;:51;;;;;;;;;;-1:-1:-1;;;;;4850:51:38;;;;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;4850:51:38;;;;;-1:-1:-1;;;;;4850:51:38;;;;;;4911:21;4958:26;4977:6;4958:18;:26::i;:::-;4935:9;4945;4935:20;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;4935:20:38;:49;;;;:::i;:::-;4911:73;;5017:13;4994:9;5004;4994:20;;;;;;;:::i;:::-;;;;;;;;;;;;:36;;;;;-1:-1:-1;;;;;4994:36:38;;;;;-1:-1:-1;;;;;4994:36:38;;;;;;5040:38;5053:9;5064:13;5040:12;:38::i;:::-;5179:17;5186:9;5179:6;:17::i;:::-;:56;;-1:-1:-1;;;5179:56:38;;-1:-1:-1;;;;;23015:32:195;;;5179:56:38;;;22997:51:195;23084:32;;;23064:18;;;23057:60;23133:18;;;23126:34;;;23176:18;;;23169:34;;;5179:27:38;;;;;;;22969:19:195;;5179:56:38;22766:443:195;19705:395:38;19807:30;19839:21;19862:36;19939:25;247:1:19;19939:14:38;:25::i;:::-;-1:-1:-1;;;;;19914:50:38;;;19990:24;342:1:19;19990:14:38;:24::i;:::-;-1:-1:-1;;;;;19974:40:38;;-1:-1:-1;20055:38:38;19974:40;20055:22;:38;:::i;:::-;20024:69;;19705:395;;;:::o;6442:130::-;6519:7;6545:9;6555;6545:20;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;6545:20:38;;;-1:-1:-1;;6442:130:38:o;49023:1375:0:-;49371:23;49396;49432;49457;49484:15;18479:14:38;;-1:-1:-1;;;;;18479:14:38;;;;-1:-1:-1;;;18495:14:38;;;;;18395:122;49484:15:0;-1:-1:-1;;;;;49431:68:0;;;-1:-1:-1;;;;;49431:68:0;;;49510:28;49540;49572:269;49627:13;49654:15;49683;49712:21;49747:22;49783;49819:12;49572:41;:269::i;:::-;49509:332;;;;49852:28;49882;49914:269;49969:13;49996:15;50025;50054:21;50089:22;50125;50161:12;49914:41;:269::i;:::-;49851:332;;;;50253:20;50230;:43;:161;;50348:20;50370;50230:161;;;50289:20;50311;50230:161;50193:198;;;;;;;;49421:977;;;;;;49023:1375;;;;;;;;;;;:::o;17033:510:38:-;17230:52;17245:17;17264;17230:14;:52::i;:::-;17375:17;;17333:203;;17360:76;;-1:-1:-1;;;;;17375:17:38;17394;17413:15;17375:17;17360:14;:76::i;:::-;17465:17;;17450:76;;-1:-1:-1;;;17465:17:38;;-1:-1:-1;;;;;17465:17:38;17484;17503:15;17520:5;17450:14;:76::i;:::-;17333:13;:203::i;:::-;17293:17;17292:244;;-1:-1:-1;;;;;;17292:244:38;-1:-1:-1;;;;;;;;17292:244:38;;;;-1:-1:-1;;;;;;17292:244:38;;;;;;;;;;;;-1:-1:-1;;;;17033:510:38:o;47135:323:0:-;47234:41;47277:14;47295:30;47310:8;47320:4;47295:14;:30::i;:::-;47233:92;;;;47339:12;:25;;;;47355:9;47339:25;47335:117;;;47380:61;;-1:-1:-1;;;47380:61:0;;-1:-1:-1;;;;;47380:31:0;:38;;;;:61;;47419:11;;47432:8;;47380:61;;;:::i;6578:133:38:-;6658:7;6684:9;6694;6684:20;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;6684:20:38;6677:27;;6578:133;;;:::o;6408:1682:29:-;6593:18;;:28;;;;;6565:25;;;;6670:29;;6858;;;;6827:28;;;;6959:29;;;;6928:28;;;;;6565:56;;;;6670:60;;;;;-1:-1:-1;;;;;7007:42:29;;;;;7030:19;7007:42;7003:902;;;7138:18;;:28;;;7194:21;;;;7093:270;;7123:100;;7168:24;;7217:5;7123:14;:100::i;:::-;7260:18;;7245:100;;404:1:19;7260:28:29;;;;7290:24;7316:6;:21;;;7339:5;7245:14;:100::i;:::-;7093:8;:270::i;:::-;7069:294;;;;7003:902;;;7406:19;7402:236;;;7513:18;;7473:146;;373:1:19;7513:28:29;;;;7543:24;7569:6;:21;;;7592:5;7473:14;:146::i;:::-;7449:170;;;;7402:236;7659:19;7655:236;;;7766:18;;7726:146;;404:1:19;7766:28:29;;;;7796:24;7822:6;:21;;;7845:5;7726:14;:146::i;:::-;7702:170;;;;7655:236;7923:18;;:29;4401:3:30;7979:27:29;;669:2;7923:53;;;:83;7919:155;;;8033:26;;-1:-1:-1;;;8033:26:29;;;;;;;;;;;1541:361:20;1695:15;1726:11;1741:1;1726:16;1722:105;;-1:-1:-1;1765:6:20;1758:13;;1722:105;1843:52;1850:6;1858:11;1871;1884:10;1843:6;:52::i;6187:249:38:-;6265:7;6393:17;6400:9;6393:6;:17::i;:::-;:36;;-1:-1:-1;;;6393:36:38;;-1:-1:-1;;;;;3894:32:195;;;6393:36:38;;;3876:51:195;6393:27:38;;;;;;;3849:18:195;;6393:36:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;6187:249::-;;;;;:::o;1174:361:20:-;1328:15;1359:11;1374:1;1359:16;1355:105;;-1:-1:-1;1398:6:20;1391:13;;1355:105;1476:52;1483:6;1491:11;1504;1517:10;1476:6;:52::i;344:824::-;584:7;925:226;957:77;972:15;989:14;1005:21;1028:5;957:14;:77::i;:::-;1052:22;1092;1132:5;925:14;:226::i;:::-;918:233;344:824;-1:-1:-1;;;;;;344:824:20:o;5248:613:38:-;5558:17;5565:9;5558:6;:17::i;:::-;:58;;-1:-1:-1;;;5558:58:38;;-1:-1:-1;;;;;23015:32:195;;;5558:58:38;;;22997:51:195;23084:32;;;23064:18;;;23057:60;23133:18;;;23126:34;;;23176:18;;;23169:34;;;5558:27:38;;;;;;;22969:19:195;;5558:58:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5650:27;5669:7;5650:18;:27::i;:::-;5626:9;5636;5626:20;;;;;;;:::i;:::-;;;;;;;;;;;;:51;;;;;;;;;;-1:-1:-1;;;;;5626:51:38;;;;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;5626:51:38;;;;;-1:-1:-1;;;;;5626:51:38;;;;;;5687:21;5734:26;5753:6;5734:18;:26::i;:::-;5711:9;5721;5711:20;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;5711:20:38;:49;;;;:::i;:::-;5687:73;;5793:13;5770:9;5780;5770:20;;;;;;;:::i;:::-;;;;;;;;;;;;:36;;;;;-1:-1:-1;;;;;5770:36:38;;;;;-1:-1:-1;;;;;5770:36:38;;;;;;5816:38;5829:9;5840:13;5816:12;:38::i;18577:816:0:-;18835:20;18871:16;;18867:520;;18903:37;;;18954:23;;;:33;;;19034;19049:17;19034:14;:33::i;:::-;-1:-1:-1;;;;;19001:66:0;:30;;;:66;19115:34;19130:18;19115:14;:34::i;:::-;-1:-1:-1;;;;;19081:68:0;:31;;;:68;19164:45;19081:15;19164:28;:45::i;:::-;19293:83;19327:2;19331:17;19350:12;475:4:19;19293:33:0;:83::i;:::-;19278:98;18577:816;-1:-1:-1;;;;;;;18577:816:0:o;22735:1129::-;22802:23;22827;22862;22895;23011:48;23048:10;23011:36;:48::i;:::-;22928:131;;-1:-1:-1;22928:131:0;-1:-1:-1;22928:131:0;;-1:-1:-1;22928:131:0;-1:-1:-1;23071:23:0;;23123:15;18479:14:38;;-1:-1:-1;;;;;18479:14:38;;;;-1:-1:-1;;;18495:14:38;;;;;18395:122;23123:15:0;-1:-1:-1;;;;;23070:68:0;;;-1:-1:-1;;;;;23070:68:0;;;23149:23;23182;23264:84;23276:10;23288:15;23305;23322;373:1:19;23264:11:0;:84::i;:::-;23215:133;-1:-1:-1;23215:133:0;-1:-1:-1;23407:84:0;23419:10;23431:15;23448;23465;404:1:19;23407:11:0;:84::i;:::-;23358:133;-1:-1:-1;23358:133:0;-1:-1:-1;23506:19:0;;;;:42;;;23533:15;23529:1;:19;23506:42;23502:233;;;23564:160;23608:15;23625;23642:33;23660:15;23608;23642:33;:::i;:::-;23677;23695:15;23677;:33;:::i;23564:160::-;23745:21;:19;:21::i;:::-;23806:51;23840:10;23852:4;23806:33;:51::i;:::-;22852:1012;;;;;;22735:1129;;;:::o;24938:2533::-;25014:25;25041;25068:28;25108:23;25141;25261:48;25298:10;25261:36;:48::i;:::-;25174:135;;-1:-1:-1;25174:135:0;-1:-1:-1;25174:135:0;;-1:-1:-1;25174:135:0;-1:-1:-1;25340:36:0;25379:21;342:1:19;25379:11:0;:21::i;:::-;25340:60;;25410:34;25447:24;342:1:19;25447:14:0;:24::i;:::-;-1:-1:-1;;;;;25410:61:0;;;25482:28;25567:347;25617:17;25648;25679:15;25708;25737:60;25764:15;25781;25737:26;:60::i;:::-;25811:26;25851:28;475:4:19;25567:36:0;:347::i;:::-;25520:394;;-1:-1:-1;25520:394:0;-1:-1:-1;26071:1:0;26047:25;;;26043:91;;26095:28;;-1:-1:-1;;;26095:28:0;;;;;;;;;;;26043:91;26143:30;26176:31;26186:10;342:1:19;26176:9:0;:31::i;:::-;26143:64;;26245:22;26222:20;:45;26218:347;;;26306:22;26283:45;;26393:147;26431:20;26453:26;26481:28;475:4:19;26393:16:0;:147::i;:::-;26370:170;;26218:347;26602:26;26579:20;:49;26575:342;;;26880:26;26857:49;;26575:342;27014:84;342:1:19;27031:10:0;27043;27055:20;27077;27014:6;:84::i;:::-;27217:156;27257:15;27274;27291:35;27309:17;27257:15;27291:35;:::i;:::-;27328;27346:17;27328:15;:35;:::i;27217:156::-;27413:51;27447:10;27459:4;27413:33;:51::i;:::-;25098:2373;;;;;;24938:2533;;;;;:::o;31292:3252::-;31565:35;31658:21;:37;;;31654:1;:41;:86;;;;31703:21;:37;;;31699:1;:41;31654:86;31650:597;;;31756:91;31770:21;:37;;;31809:21;:37;;;31756:13;:91::i;:::-;31862:30;31894;31928:16;31935:8;31928:6;:16::i;:::-;31861:83;;;;32021:215;32050:22;32090:21;:37;;;32145:22;32185:21;:37;;;32021:11;:215::i;:::-;31742:505;;31650:597;32336:21;:38;;;32332:1;:42;:88;;;;32382:21;:38;;;32378:1;:42;32332:88;32328:743;;;32436:93;32450:21;:38;;;32490:21;:38;;;32436:13;:93::i;:::-;32544:31;32577;32610:36;32666:25;32682:8;32666:15;:25::i;:::-;32543:148;;;;;;32768:219;32797:23;32838:21;:38;;;32894:23;32935:21;:38;;;32768:11;:219::i;:::-;33032:28;-1:-1:-1;;;32328:743:0;33147:157;;-1:-1:-1;;;33147:157:0;;33132:12;;-1:-1:-1;;;;;33147:31:0;:60;;;;:157;;33221:11;;33234:8;;33244:21;;33267:27;;33147:157;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;33132:172;;33319:7;33315:1223;;;33346:31;;33342:104;;33397:34;33409:8;342:1:19;33429::0;33397:11;:34::i;:::-;33467:21;:37;;;33463:1;:41;:86;;;;33512:21;:37;;;33508:1;:41;33463:86;33459:324;;;33570:23;33595;33623:13;:11;:13::i;:::-;33569:67;-1:-1:-1;;;;;33569:67:0;;;-1:-1:-1;;;;;33569:67:0;;;33654:48;33666:8;373:1:19;33686:15:0;33654:11;:48::i;:::-;33720;33732:8;404:1:19;33752:15:0;33720:11;:48::i;:::-;33551:232;;33459:324;33864:7;:5;:7::i;:::-;33972:22;;:33;;;34023;;;34074;;;;;33886:235;;33926:8;;33952:2;;33972:33;;34023;33886:22;:235::i;:::-;33315:1223;;;34229:298;34269:8;34295:2;34315:21;:54;;;34387:21;:54;;;34459:21;:54;;;34229:22;:298::i;35478:862::-;35772:269;;-1:-1:-1;;;35772:269:0;;:11;;:29;;:269;;35815:31;;35860:11;;35885:8;;35907:32;;35953;;35999;;35772:269;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;36125:208;36161:8;36183:2;36199:32;36245;36291;36125:22;:208::i;36714:2487::-;37083:79;;-1:-1:-1;;;37083:79:0;;36996:72;;37083:11;;:48;;:79;;37132:11;;37145:8;;37155:6;;37083:79;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;36996:166;;37216:6;37212:950;;;37242:28;37274:25;37290:8;37274:15;:25::i;:::-;37238:61;;;;37340:26;:41;;;37317:20;:64;37313:153;;;37408:43;;-1:-1:-1;;;37408:43:0;;;;;;;;;;;37313:153;37483:26;:34;;;37479:74;;;37519:34;37531:8;342:1:19;37551::0;37519:11;:34::i;:::-;37224:340;37212:950;;;37585:23;37610;37637:16;37644:8;37637:6;:16::i;:::-;37584:69;;;;37706:26;:41;;;37688:15;:59;:142;;;;37789:26;:41;;;37771:15;:59;37688:142;37667:261;;;37870:43;;-1:-1:-1;;;37870:43:0;;;;;;;;;;;37667:261;37945:26;:34;;;37941:211;;;37999:60;38011:8;373:1:19;38031:11:0;:27;;;37999:11;:60::i;:::-;38077;38089:8;404:1:19;38109:11:0;:27;;;38077:11;:60::i;:::-;37570:592;;37212:950;38176:8;38172:875;;;38200:263;38237:8;38263:2;38327:26;:43;;;38283:26;:41;;;:87;;;;:::i;:::-;247:1:19;38415:26:0;:34;;;38200:19;:263::i;:::-;38172:875;;;38494:264;38531:8;38557:2;38621:26;:44;;;38577:26;:41;;;:88;;;;:::i;:::-;279:1:19;38710:26:0;:34;;;38494:19;:264::i;:::-;38772;38809:8;38835:2;38899:26;:44;;;38855:26;:41;;;:88;;;;:::i;:::-;311:1:19;38988:26:0;:34;;;38772:19;:264::i;:::-;39060:26;:34;;;39056:139;;;39177:7;:5;:7::i;5278:393:29:-;5513:1;5495:15;:19;:43;;;;;5537:1;5518:16;:20;5495:43;5494:94;;;;5562:1;5544:15;:19;:43;;;;;5586:1;5567:16;:20;5544:43;5490:175;;;5611:43;;-1:-1:-1;;;5611:43:29;;;;;;;;;;;14918:572:0;15119:22;15157:16;;15153:331;;15206:87;15249:12;15263:13;15278:14;15206:42;:87::i;:::-;15189:104;-1:-1:-1;15307:23:0;15341:29;15189:104;15341:12;:29;:::i;:::-;15307:64;-1:-1:-1;15385:88:0;15419:2;15423:18;-1:-1:-1;;;;;15385:88:0;;15460:12;15385:33;:88::i;:::-;;15175:309;14918:572;;;;;;;:::o;42024:357::-;42060:23;42085;42110:20;42132;42168:50;2144:1:30;42168:36:0;:50::i;:::-;42059:159;;;;;;;;42228:146;42268:15;42285;42320:12;42302:15;:30;;;;:::i;:::-;42334;42352:12;42334:15;:30;:::i;6215:704:89:-;6277:7;6300:1;6305;6300:6;6296:150;;6400:35;1035:4:79;6400:11:89;:35::i;:::-;6896:1;6891;6887;:5;6886:11;;;;;:::i;:::-;;6900:1;6886:15;6876:5;;;6860:42;;6215:704;-1:-1:-1;;;6215:704:89:o;5473:602:26:-;5546:19;-1:-1:-1;;5581:15:26;;;;;;:34;;-1:-1:-1;5600:15:26;;;;1234:6;5600:15;5581:34;5577:64;;;5624:17;;-1:-1:-1;;;5624:17:26;;;;;;;;;;;5577:64;5651:21;;;;:14;5708:11;;;:32;;5733:7;5708:32;;;5722:8;5723:7;5722:8;:::i;:::-;5682:59;-1:-1:-1;5848:12:26;5859:1;5682:59;5848:12;:::i;:::-;;;5884:29;5905:7;5884:20;:29::i;:::-;5870:43;-1:-1:-1;5937:6:26;5927:16;;:21;5923:75;;5995:3;5965:25;:11;5979;5965:25;:::i;:::-;5964:34;;5950:48;;5923:75;6017:4;6013:8;;:1;:8;6009:59;;;6037:31;6057:11;-1:-1:-1;;6037:31:26;:::i;:::-;6023:45;;6009:59;5567:508;;5473:602;;;:::o;21696:453:38:-;21837:31;21870;21939:60;21966:15;21983;21939:26;:60::i;:::-;22062:25;;21913:86;;-1:-1:-1;22047:95:38;;-1:-1:-1;;;;;;;;22062:25:38;;;;;21913:86;;22114:20;;22047:14;:95::i;:::-;22009:133;;21696:453;;;;;:::o;45075:754:0:-;45270:20;45266:24;;:1;:24;:47;;;;;45298:15;45294:1;:19;45266:47;:70;;;;;45321:15;45317:1;:19;45266:70;45262:561;;;45352:13;45368:86;45392:61;45407:15;-1:-1:-1;;;45430:15:0;45447:5;45392:14;:61::i;45368:86::-;45614:80;;-1:-1:-1;;;45614:80:0;;27495:1:195;27484:21;;;45614:80:0;;;27466:40:195;27554:10;27542:23;;27522:18;;;27515:51;45352:102:0;;-1:-1:-1;45614:31:0;-1:-1:-1;;;;;45614:49:0;;;;27439:18:195;;45614:80:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;45610:157;;;45714:19;:38;;-1:-1:-1;;;;;45714:38:0;-1:-1:-1;;;45714:38:0;;;;;;;45610:157;45781:31;45804:7;45781:31;;:22;:31::i;1219:204:76:-;1306:37;1320:5;1327:2;1331:5;1338:4;1306:13;:37::i;:::-;1301:116;;1366:40;;-1:-1:-1;;;1366:40:76;;-1:-1:-1;;;;;3894:32:195;;1366:40:76;;;3876:51:195;3849:18;;1366:40:76;;;;;;;;2332:2247:24;2483:11;2510:5;2519:1;2510:10;2506:49;;-1:-1:-1;2543:1:24;2536:8;;2506:49;2585:16;2568:14;:33;2564:2009;;;2698:16;2673:22;2681:14;2673:5;:22;:::i;:::-;:41;2669:1097;;;2915:14;2957:16;2932:22;2940:14;2932:5;:22;:::i;:::-;:41;;;;:::i;:::-;2915:58;-1:-1:-1;3020:48:24;1619:1;3020:16;:48;:::i;:::-;2995:22;3003:14;2995:5;:22;:::i;:::-;:73;2991:535;;;3238:74;3253:16;-1:-1:-1;;;3294:10:24;3298:6;1364:2;3294:10;:::i;:::-;3306:5;3238:14;:74::i;:::-;3228:84;;-1:-1:-1;;;3228:84:24;:::i;:::-;3170:143;;1767:4;3170:143;:::i;:::-;3164:149;;2991:535;;;3430:77;-1:-1:-1;;;3475:6:24;3483:16;3501:5;3430:14;:77::i;:::-;3424:83;;2991:535;3549:41;3564:3;3569:6;3577:5;3584;3549:14;:41::i;:::-;3543:47;;2716:889;2564:2009;;2669:1097;-1:-1:-1;876:18:24;2564:2009;;;3811:48;1619:1;3811:16;:48;:::i;:::-;3786:22;3794:14;3786:5;:22;:::i;:::-;:73;3782:791;;;4047:255;4091:16;-1:-1:-1;;;4206:33:24;4091:16;4206:14;:33;:::i;:::-;4201:39;;:1;:39;:::i;:::-;4193:47;;:5;:47;:::i;:::-;4188:53;;1364:2;4188:53;:::i;4047:255::-;4013:289;;-1:-1:-1;;;4013:289:24;:::i;:::-;3942:378;;1767:4;3942:378;:::i;:::-;3936:384;;3782:791;;;4414:148;-1:-1:-1;;;4518:5:24;4481:33;4498:16;4481:14;:33;:::i;:::-;4476:39;;:1;:39;:::i;:::-;:47;;;;:::i;:::-;4525:16;4543:5;4414:14;:148::i;9264:218:90:-;9321:7;-1:-1:-1;;;;;9344:25:90;;9340:105;;;9392:42;;-1:-1:-1;;;9392:42:90;;9423:3;9392:42;;;27759:36:195;27811:18;;;27804:34;;;27732:18;;9392:42:90;27577:267:195;9340:105:90;-1:-1:-1;9469:5:90;9264:218::o;5617:111:89:-;5675:7;5312:5;;;5709;;;5311:36;5306:42;;5701:20;5071:294;4938:334:29;5034:69;5091:11;5034:56;:69::i;:::-;5113:36;5152:30;5170:11;5152:17;:30::i;:::-;5113:69;;5192:73;5215:14;5231:11;:33;;;5192:22;:73::i;12720:520:38:-;12834:28;;:::i;:::-;12879:9;12874:360;12894:17;12890:1;:21;12874:360;;;12932:21;12956;12966:7;12975:1;12956:9;:21::i;:::-;12932:45;-1:-1:-1;12995:17:38;;12991:233;;13123:86;13140:13;13155;13169:1;13155:16;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;13123:86:38;13173:9;13183:1;13173:12;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;13173:12:38;-1:-1:-1;;;;;13123:86:38;13207:1;443::19;13187:21:38;;13123:16;:86::i;:::-;13107:10;13118:1;13107:13;;;;;;;:::i;:::-;;;;:102;12991:233;-1:-1:-1;12913:3:38;;12874:360;;;;12720:520;;;;:::o;2467:977:29:-;2744:41;;:::i;:::-;2856:23;;;;2829:24;;2797:29;;2829:50;;;:::i;:::-;-1:-1:-1;;;;;2797:82:29;;;2896:541;;;;;;;;2945:10;2896:541;;;;2988:36;3016:7;2988:27;:36::i;:::-;2896:541;;;;3057:36;3085:7;3057:27;:36::i;:::-;2896:541;;;;3135:123;3167:42;3177:31;3194:14;3177;:31;:::i;3167:42::-;-1:-1:-1;;;3216:21:29;3239:5;3135:14;:123::i;:::-;2896:541;;;;3295:41;3319:17;3295:21;:41;:::i;:::-;2896:541;;;;3367:14;2896:541;;;;3412:14;2896:541;;;2889:548;;;2467:977;;;;;;;;;:::o;13246:2409:38:-;13475:24;13501;13537:57;;:::i;:::-;13973:130;;-1:-1:-1;;;13973:130:38;;;28037:23:195;;;13973:130:38;;;28019:42:195;28097:23;;28077:18;;;28070:51;13618:22:38;;;;-1:-1:-1;;;;;13973:31:38;:85;;;;27992:18:195;;13973:130:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;14131:20;:39;;;;;;-1:-1:-1;;;14131:39:38;-1:-1:-1;;;;;14131:39:38;;;;;;;;;;14207:348;;;;;;;;;;;;;-1:-1:-1;14207:348:38;;;;;;;13931:172;;-1:-1:-1;13931:172:38;;-1:-1:-1;14207:348:38;;;14381:66;14414:15;14431;14381:32;:66::i;:::-;14207:348;;;;;;;;;;;;;;;;;;-1:-1:-1;;14207:348:38;;-1:-1:-1;;14207:348:38;;;;;;;;;;;-1:-1:-1;;;;;14207:348:38;-1:-1:-1;;;;;14207:348:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;14521:19;14207:348;;;14184:371;;13604:962;;14577:22;14601;14625:35;14676:8;:50;14727:9;14738:20;14676:83;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;14576:183;;-1:-1:-1;14576:183:38;-1:-1:-1;14576:183:38;-1:-1:-1;14770:82:38;14785:32;14576:183;14785:15;:32;:::i;:::-;14819;14837:14;14819:15;:32;:::i;14770:82::-;14922:32;14940:14;14922:15;:32;:::i;:::-;14965;14983:14;14965:15;:32;:::i;:::-;14862:137;;;;;;;;15010:13;15026:7;-1:-1:-1;;;;;15026:13:38;;:15;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15010:31;-1:-1:-1;15052:64:38;247:1:19;15010:31:38;15087:17;247:1:19;15087:28:38;;;;;15052:16;:64::i;:::-;15126;279:1:19;15154:5:38;15161:17;279:1:19;15161:28:38;;15126:64;15200;311:1:19;15228:5:38;15235:17;311:1:19;15235:28:38;;15200:64;15275:43;;;;;;;;;;-1:-1:-1;;15309:9:38;;15275:43;;15309:9;-1:-1:-1;15275:43:38;;;;;;;;;;;-1:-1:-1;;;;;15275:43:38;-1:-1:-1;;;;;15275:43:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;15350:13;247:1:19;15350:24:38;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;15328:46:38;;:19;:46;-1:-1:-1;15406:24:38;;;;-1:-1:-1;;;;;15384:46:38;;279:1:19;15384:46:38;-1:-1:-1;15462:24:38;;;;-1:-1:-1;;;;;15440:46:38;;311:1:19;15440:46:38;-1:-1:-1;15517:23:38;;;;-1:-1:-1;;;;;15496:44:38;;342:1:19;15496:44:38;-1:-1:-1;15571:23:38;;;;-1:-1:-1;;;;;15550:44:38;;373:1:19;15550:44:38;-1:-1:-1;15625:23:38;;;;-1:-1:-1;;;;;15604:44:38;;404:1:19;15604:44:38;;13527:2128;;;;;;13246:2409;;;;;;;;:::o;5677:725:29:-;5805:37;5845:209;5877:6;:14;;;5909:6;:30;;;5987:6;:30;;;5957:6;:27;;;:60;6035:5;5845:14;:209::i;:::-;5805:249;;6284:6;:21;;;6252:29;6236:6;:13;;;:45;:69;6211:6;:22;;;6090:98;6122:29;6105:6;:14;;;:46;669:2;4401:3:30;6182:5:29;6090:14;:98::i;:::-;:143;:215;6069:317;;;6345:26;;-1:-1:-1;;;6345:26:29;;;;;;;;;;;10282:218:90;10339:7;-1:-1:-1;;;;;10362:25:90;;10358:105;;;10410:42;;-1:-1:-1;;;10410:42:90;;10441:3;10410:42;;;27759:36:195;27811:18;;;27804:34;;;27732:18;;10410:42:90;27577:267:195;3751:759:38;3951:19;;3974:1;3951:24;3947:37;;3751:759;;:::o;3947:37::-;3998:9;3994:510;;-1:-1:-1;;;;;4036:28:38;;;:19;:28;;10409:2305;;:::o;3994:510::-;279:1:19;4085:9:38;:22;4081:423;;-1:-1:-1;;;;;4123:28:38;;;:19;:28;;10409:2305;;:::o;4081:423::-;311:1:19;4172:9:38;:22;4168:336;;-1:-1:-1;;;;;4210:28:38;;;:19;:28;;10409:2305;;:::o;4168:336::-;342:1:19;4259:9:38;:21;4255:249;;-1:-1:-1;;;;;4296:27:38;;;:18;:27;;10409:2305;;:::o;4255:249::-;373:1:19;4344:9:38;:21;4340:164;;-1:-1:-1;;;;;4381:27:38;;;:18;:27;;10409:2305;;:::o;4340:164::-;404:1:19;4429:9:38;:21;4425:79;;-1:-1:-1;;;;;4466:27:38;;;:18;:27;;3751:759;;:::o;2118:1299:20:-;2435:23;2460;2576:74;2583:14;2599:21;2622:13;2637:12;2576:6;:74::i;:::-;2558:92;-1:-1:-1;2724:21:20;4401:3:30;2724:14:20;:21;:::i;:::-;2698:22;335:2;2698:13;:22;:::i;:::-;:47;;:712;;3056:340;3124:14;3160:13;3195:14;3231:21;3274:24;3320;3366:12;3056:46;:340::i;:::-;2698:712;;;2778:15;2918:91;2778:15;2944:24;2970;2996:12;2918:8;:91::i;:::-;2661:749;;;;-1:-1:-1;2118:1299:20;-1:-1:-1;;;;;;;;2118:1299:20:o;5435:111:89:-;5493:7;5312:5;;;5527;;;5311:36;5306:42;;5519:20;5071:294;19399:390:0;19569:20;19616:95;19633:12;19647:25;19662:9;19647:14;:25::i;:::-;-1:-1:-1;;;;;19616:95:0;19674:22;19686:9;19674:11;:22::i;:::-;19698:12;19616:16;:95::i;:::-;19601:110;;19721:61;19728:9;19739:10;19751:2;19755:12;19769;19721:6;:61::i;23870:840::-;24072:32;24106:24;24202:13;24219:1;24202:18;24198:37;;-1:-1:-1;24230:1:0;;-1:-1:-1;24230:1:0;24222:13;;24198:37;24285:91;24328:13;24343:15;24360;24285:42;:91::i;:::-;24246:130;-1:-1:-1;24406:40:0;24246:130;24406:13;:40;:::i;:::-;24387:59;;24456:21;24480:133;24510:16;24528:31;24543:15;24528:14;:31::i;:::-;-1:-1:-1;;;;;24480:133:0;24561:28;24573:15;24561:11;:28::i;:::-;24591:12;24480:16;:133::i;:::-;24456:157;;24623:80;24630:15;24647:10;24659;24671:16;24689:13;24623:6;:80::i;:::-;24132:578;23870:840;;;;;;;;;:::o;34550:165::-;34635:73;;-1:-1:-1;;;34635:73:0;;;;;5833:25:195;;;5874:18;;;5867:34;;;34645:10:0;;34635:45;;5806:18:195;;34635:73:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;34721:245;34851:9;34841:7;:19;:42;;;;34874:9;34864:7;:19;34841:42;34837:123;;;34906:43;;-1:-1:-1;;;34906:43:0;;;;;;;;;;;22368:1332:38;22462:21;22486:17;22493:9;22486:6;:17::i;:::-;:37;;-1:-1:-1;;;22486:37:38;;-1:-1:-1;;;;;3894:32:195;;;22486:37:38;;;3876:51:195;22486:27:38;;;;;;;3849:18:195;;22486:37:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;22462:61;;22615:21;22651:96;22668:13;22683:25;22698:9;22683:14;:25::i;:::-;-1:-1:-1;;;;;22651:96:38;22710:22;22722:9;22710:11;:22::i;:::-;22734:12;22651:16;:96::i;:::-;22615:132;;22758:72;22765:9;22784:4;22791:8;22801:13;22816;22758:6;:72::i;:::-;342:1:19;22844:9:38;:21;22840:776;;22953:33;22972:13;22953:18;:33::i;:::-;22929:9;:57;;-1:-1:-1;;;;;22929:57:38;;;;;247:1:19;;22929:57:38;;;;-1:-1:-1;;;;;22929:57:38;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;22929:57:38;;;;;-1:-1:-1;;;;;22929:57:38;;;;;;22840:776;;;23017:31;23098:7;23051:44;23066:28;443:1:19;23066:9:38;:28;:::i;23051:44::-;-1:-1:-1;;;;;23051:54:38;;;;;:::i;:::-;23017:88;;23119:20;23142:70;23157:13;23172:7;23181:23;23206:5;23142:14;:70::i;:::-;23119:93;;373:1:19;23231:9:38;:21;23227:379;;23290:32;23309:12;23290:18;:32::i;:::-;23272:14;:50;;:14;;:50;;;;-1:-1:-1;;;;;23272:50:38;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;23272:50:38;;;;;-1:-1:-1;;;;;23272:50:38;;;;;;23364:48;23399:12;23383:13;:28;;;;:::i;:::-;23364:18;:48::i;:::-;23340:9;:72;;-1:-1:-1;;;;;23340:72:38;;;;;:20;;:72;;;;-1:-1:-1;;;23340:72:38;;-1:-1:-1;;;;;23340:72:38;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;23340:72:38;;;;;-1:-1:-1;;;;;23340:72:38;;;;;;23227:379;;;23469:32;23488:12;23469:18;:32::i;:::-;23451:14;:50;;:14;;:50;;;;-1:-1:-1;;;23451:50:38;;-1:-1:-1;;;;;23451:50:38;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;23451:50:38;;;;;-1:-1:-1;;;;;23451:50:38;;;;;;23543:48;23578:12;23562:13;:28;;;;:::i;23543:48::-;23519:20;:72;;-1:-1:-1;;;;;23519:72:38;;;;;:20;;:72;;;;-1:-1:-1;;;;;23519:72:38;;:::i;:::-;;;;;;;;-1:-1:-1;;;;;23519:72:38;;;;;-1:-1:-1;;;;;23519:72:38;;;;;;23227:379;23003:613;;22840:776;23631:62;;;5833:25:195;;;5889:2;5874:18;;5867:34;;;23653:9:38;;-1:-1:-1;;;;;23631:62:38;;;;;5806:18:195;23631:62:38;;;;;;;22452:1248;;22368:1332;;;:::o;39207:534:0:-;39459:85;39479:8;39489:2;39493:32;247:1:19;39538:5:0;39459:19;:85::i;:::-;39554;39574:8;39584:2;39588:32;279:1:19;39633:5:0;39554:19;:85::i;:::-;39649;39669:8;39679:2;39683:32;311:1:19;39728:5:0;40123:1321;40368:43;;40404:7;40368:43;40653:20;40676:17;40683:9;40676:6;:17::i;:::-;40729:21;;-1:-1:-1;;;40729:21:0;;-1:-1:-1;;;;;3894:32:195;;;40729:21:0;;;3876:51:195;40653:40:0;;-1:-1:-1;40703:23:0;;40729:15;;;;;3849:18:195;;40729:21:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;40703:47;;40760:20;40783:22;40795:9;40783:11;:22::i;:::-;40760:45;;40815:20;40838:25;40853:9;40838:14;:25::i;:::-;-1:-1:-1;;;;;40815:48:0;;;40873:22;40898:84;40915:25;40942:12;40956;475:4:19;40898:16:0;:84::i;:::-;40873:109;;40992:5;-1:-1:-1;;;;;40992:19:0;;41012:4;41018:2;41022:41;41031:15;41048:14;41022:8;:41::i;:::-;40992:72;;-1:-1:-1;;;;;;40992:72:0;;;;;;;-1:-1:-1;;;;;22121:32:195;;;40992:72:0;;;22103:51:195;22190:32;;;;22170:18;;;22163:60;22239:18;;;22232:34;22076:18;;40992:72:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;41079:9;:45;;;;;41110:14;41092:15;:32;41079:45;41075:363;;;41140:18;41161:32;41179:14;41161:15;:32;:::i;:::-;41140:53;;41208:17;41215:9;41208:6;:17::i;:::-;:64;;-1:-1:-1;;;41208:64:0;;-1:-1:-1;;;;;22121:32:195;;;41208:64:0;;;22103:51:195;41254:4:0;22170:18:195;;;22163:60;22239:18;;;22232:34;;;41208:31:0;;;;;;;22076:18:195;;41208:64:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;41286:141;41310:9;41321:2;41325:4;41331:70;41348:10;41360:12;41374;475:4:19;41388:12:0;41331:16;:70::i;:::-;41403:10;41286:6;:141::i;:::-;41126:312;40302:1142;;;;;40123:1321;;;;;:::o;42624:610::-;42797:23;42861:21;4401:3:30;42861:14:0;:21;:::i;:::-;42836:22;1481:2;42836:13;:22;:::i;:::-;:46;42832:396;;;42934:14;42919:12;:29;:134;;;-1:-1:-1;4401:3:30;43016:29:0;43033:12;43016:14;:29;:::i;:::-;43015:38;;;;:::i;:::-;1481:2;42973:28;42989:12;42973:13;:28;:::i;:::-;42972:39;;;;:::i;:::-;:81;;42919:134;42898:320;;;43104:30;43120:14;43104:13;:30;:::i;42898:320::-;-1:-1:-1;43191:12:0;;42624:610;-1:-1:-1;;42624:610:0:o;1776:194:79:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;6081:2078:26;6164:19;6233:7;6243:3;6233:13;6250:1;6233:18;:93;;-1:-1:-1;;;6233:93:26;;;-1:-1:-1;;;6233:93:26;6219:107;;;-1:-1:-1;6354:3:26;6344:13;;:18;6340:95;;-1:-1:-1;;;6379:48:26;6432:3;6378:57;6340:95;6463:3;6453:13;;:18;6449:95;;-1:-1:-1;;;6488:48:26;6541:3;6487:57;6449:95;6572:3;6562:13;;:18;6558:95;;6611:34;6597:48;6650:3;6596:57;6558:95;6681:4;6671:14;;:19;6667:129;;6739:34;6725:48;6778:3;6724:57;6667:129;6823:4;6813:14;;:19;6809:129;;6881:34;6867:48;6920:3;6866:57;6809:129;6965:4;6955:14;;:19;6951:129;;7023:34;7009:48;7062:3;7008:57;6951:129;7107:4;7097:14;;:19;7093:129;;7165:34;7151:48;7204:3;7150:57;7093:129;7249:5;7239:15;;:20;7235:130;;7308:34;7294:48;7347:3;7293:57;7235:130;7392:5;7382:15;;:20;7378:130;;7451:34;7437:48;7490:3;7436:57;7378:130;7535:5;7525:15;;:20;7521:130;;7594:34;7580:48;7633:3;7579:57;7521:130;7678:5;7668:15;;:20;7664:129;;7737:33;7723:47;7775:3;7722:56;7664:129;7820:6;7810:16;;:21;7806:129;;7880:32;7866:46;7917:3;7865:55;7806:129;7962:6;7952:16;;:21;7948:93;;8004:29;7990:43;8038:3;7989:52;7948:93;8069:6;8059:16;;:21;8055:87;;8111:23;8097:37;8139:3;8096:46;8055:87;6081:2078;;;:::o;22155:207:38:-;22285:7;22311:44;22321:33;22339:15;22321;:33;:::i;15661:621::-;15763:61;;-1:-1:-1;;;15763:61:38;;15742:18;15763:61;;;29406:41:195;;;15742:18:38;15763:31;-1:-1:-1;;;;;15763:54:38;;;;29379:18:195;;15763:61:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15944:19;;:9;15921:20;15742:82;;;;;;-1:-1:-1;15835:20:38;;;;15881:302;;15921:42;;-1:-1:-1;;;;;;;;15944:19:38;;;;;;15921:20;:42;:::i;:::-;-1:-1:-1;;;;;15881:302:38;16093:7;16079:11;:21;:93;;16167:1;16162;16138:21;16152:7;16138:11;:21;:::i;:::-;:25;;;;:::i;:::-;16137:31;;;;:::i;:::-;:35;;16171:1;16137:35;:::i;:::-;15881:26;:302::i;16079:93::-;16133:1;16122:7;16104:15;:11;16118:1;16104:15;:::i;:::-;:25;;;;:::i;:::-;16103:31;;;;:::i;15881:302::-;15834:349;;;;16234:41;16248:12;16262;16234:13;:41::i;8368:1235:76:-;8595:4;8589:11;-1:-1:-1;;;8462:12:76;8613:22;;;-1:-1:-1;;;;;8661:24:76;;8655:4;8648:38;8706:4;8699:19;;;8462:12;8776:4;8462:12;8767:4;8462:12;;8754:5;8747;8742:39;8731:50;;8993:1;8986:4;8980:11;8977:18;8968:7;8964:32;8954:603;;9125:6;9115:7;9108:15;9104:28;9101:162;;;9178:16;9175:1;9170:3;9155:40;9228:16;9223:3;9216:29;9101:162;9539:1;9531:5;9519:18;9516:25;9497:16;9490:24;9486:56;9477:7;9473:70;9462:81;;8954:603;9577:4;9570:17;-1:-1:-1;8368:1235:76;;-1:-1:-1;;;;8368:1235:76:o;3908:540:29:-;4131:22;;:33;4094;;;;:70;4090:106;;4173:23;;-1:-1:-1;;;4173:23:29;;;;;;;;;;;4090:106;4259:22;;:33;;;;4306;;;;4353:32;;;;4399;;;;;4207:234;;4353:32;4207:38;:234::i;:::-;3908:540;:::o;3450:452::-;3546:36;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3546:36:29;3628:22;;:33;3594:31;;;:67;3750:27;3628:22;3750:14;:27::i;:::-;3710:36;;;3671:106;3672:36;;;3671:106;3868:27;3883:11;3868:14;:27::i;:::-;3827:37;;;3787:108;;;3788:14;3450:452;-1:-1:-1;3450:452:29:o;4454:240::-;4601:47;4610:14;4626:21;4601:8;:47::i;:::-;;;4658:29;4672:14;4658:13;:29::i;1452:464:26:-;1529:22;-1:-1:-1;;1567:15:26;;;;;;:34;;-1:-1:-1;1586:15:26;;;;1234:6;1586:15;1567:34;1563:64;;;1610:17;;-1:-1:-1;;;1610:17:26;;;;;;;;;;;1563:64;1655:12;;;;1638:14;1703:11;;;:32;;1728:7;1703:32;;;1717:8;1718:7;1717:8;:::i;:::-;1677:59;;1797:2;1764:29;1785:7;1764:20;:29::i;:::-;:35;;1747:52;;1835:4;1831:8;;:1;:8;1827:65;;;1858:34;1878:14;1858:17;:34;:::i;20404:635:38:-;20536:31;20579;20685:70;20722:15;20739;20685:36;:70::i;:::-;20765:20;:55;;-1:-1:-1;;;;;;20765:55:38;-1:-1:-1;;;;;20765:55:38;;;;;20620:135;-1:-1:-1;20765:55:38;-1:-1:-1;;20864:25:38;-1:-1:-1;20864:14:38;:25::i;:::-;-1:-1:-1;;;;;20831:58:38;;;20899:21;20923:24;342:1:19;20923:14:38;:24::i;:::-;-1:-1:-1;;;;;20899:48:38;;-1:-1:-1;20993:38:38;20899:48;20993:22;:38;:::i;:::-;20957:25;:75;;-1:-1:-1;;;;;20957:75:38;;;-1:-1:-1;;;20957:75:38;;;;;;;;;-1:-1:-1;20404:635:38;;;-1:-1:-1;;;;20404:635:38:o;16288:393::-;16391:15;;16387:288;;16422:242;16446:9;16481:4;16504:5;16527:11;16556:94;16573:11;16586:25;16601:9;16586:14;:25::i;:::-;-1:-1:-1;;;;;16556:94:38;16613:22;16625:9;16613:11;:22::i;16556:94::-;16422:6;:242::i;4103:1402:20:-;4924:28;;;4412:23;4401:3:30;4998:21:20;;335:2;4970:24;;:49;4966:256;;5057:59;5072:15;5089:13;4401:3:30;5110:5:20;5057:14;:59::i;:::-;5039:77;;4966:256;;;5192:14;5174:15;:32;5155:52;;4966:256;5362:21;5253:90;5260:15;5277:21;5316:14;5300:13;:30;5332:10;5253:6;:90::i;:::-;:130;5235:148;;5415:73;5424:15;5441:16;5459;5477:10;5415:8;:73::i;:::-;5397:91;;4103:1402;;;;;;;;;;:::o;4281:853:22:-;4408:22;4432;4518:43;4564:45;4592:16;4564:27;:45::i;:::-;4518:91;;4619:43;4677:72;4692:35;1283:21:30;-1:-1:-1;;;4744:4:22;4677:14;:72::i;:::-;4619:130;;4832:86;4847:21;4870:35;-1:-1:-1;;;4912:5:22;4832:14;:86::i;:::-;4815:103;;4981:86;4996:21;-1:-1:-1;;;5024:35:22;5061:5;4981:14;:86::i;:::-;4964:103;;5088:39;;4281:853;;;;;:::o;9114:996:29:-;9302:22;;:32;;;;9415;;;;;9302;;9415:36;9411:342;;9532:22;;9492:250;;373:1:19;9532:32:29;;;;9582:11;:29;;;9668:11;:38;;;9724:4;9492:22;:250::i;:::-;9467:275;;;;:::i;:::-;;;9411:342;9766:22;;:32;;;:36;9762:342;;9883:22;;9843:250;;404:1:19;9883:32:29;;;;9933:11;:29;;;10019:11;:38;;;10075:4;9843:22;:250::i;:::-;9818:275;;;;:::i;:::-;;;9762:342;9114:996;;;:::o;8096:1012::-;8287:22;;:33;;;8407;;;;8287;;8403:37;8399:347;;8522:22;;8482:253;;279:1:19;8522:33:29;;;;8573:11;:29;;;8660:11;:38;;;8716:5;8482:22;:253::i;:::-;8456:279;;;;:::i;:::-;;;8399:347;8763:22;;:33;;;8759:37;8755:347;;8878:22;;8838:253;;311:1:19;8878:33:29;;;;8929:11;:29;;;9016:11;:38;;;9072:5;8838:22;:253::i;14658:1090::-;14789:27;14818:33;14867:14;:36;;;14907:1;14867:41;:86;;;;-1:-1:-1;14912:36:29;;;;:41;14867:86;14863:105;;;-1:-1:-1;14963:1:29;;-1:-1:-1;14963:1:29;14955:13;;14863:105;15031:37;15053:14;15031:21;:37::i;:::-;-1:-1:-1;14979:89:29;;-1:-1:-1;14979:89:29;-1:-1:-1;15082:30:29;;:57;;;;;15138:1;15116:19;:23;15082:57;15078:131;;;15162:36;;-1:-1:-1;;;15162:36:29;;;;;;;;;;;15078:131;15522:97;15542:19;15587:14;:31;;;15563:21;:55;15522:19;:97::i;:::-;15484:135;;4401:3:30;15683:19:29;:26;3784:2:30;15638:25:29;:42;:71;15634:97;;;15718:13;;-1:-1:-1;;;15718:13:29;;;;;;;;;;;18782:684;18971:37;;;;18931;;19085:36;;;;19046;;;;18931:77;;;;;19046:75;19140:17;;19136:314;;19221:13;19202:16;:32;:142;;;;19328:16;4008:3:30;19282:13:29;19263:16;:32;19262:63;:82;19202:142;19177:259;;;19392:25;;-1:-1:-1;;;19392:25:29;;;;;;;;;;;10973:428;11157:21;11194:15;11213:1;11194:20;11190:34;;-1:-1:-1;11223:1:29;11216:8;;11190:34;11250:144;11278:64;11293:15;-1:-1:-1;;;11315:17:29;11334:7;11278:14;:64::i;:::-;-1:-1:-1;;;11349:26:29;11377:7;11250:14;:144::i;12472:393::-;12674:23;12713:15;12732:1;12713:20;12709:34;;-1:-1:-1;12742:1:29;12735:8;;13211:1441;13478:36;;;;13438:37;;13596:36;;;;13556:37;;;;13317:27;;;;;;13438:76;-1:-1:-1;13438:76:29;13556;13647:25;;13438:76;13647:54;;;13677:24;13676:25;13647:54;13643:1003;;;13906:14;:37;;;13867:14;:36;;;:76;13806:14;:37;;;13767:14;:36;;;:76;:177;13745:199;;13643:1003;;;13980:24;13975:671;;14109:14;:37;;;14070:14;:36;;;:76;14048:98;;14232:14;:36;;;14192:14;:37;;;:76;14164:104;;14307:4;14296:15;;13975:671;;;14333:24;14328:318;;14462:14;:37;;;14423:14;:36;;;:76;14401:98;;14585:14;:36;;;14545:14;:37;;;:76;14517:104;;14328:318;13396:1256;;13211:1441;;;;;:::o;18393:383::-;18527:7;18573:21;18550:19;:44;18546:103;;18617:21;;-1:-1:-1;;;18617:21:29;;;;;;;;;;;18546:103;18665:104;18678:43;18702:19;18678:21;:43;:::i;:::-;18724;18748:19;18724:21;:43;:::i;:::-;18665:12;:104::i;-1:-1:-1:-;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::o;14:513:195:-;194:3;179:19;;183:9;275:6;152:4;309:212;323:4;320:1;317:11;309:212;;;386:13;;-1:-1:-1;;;;;382:54:195;370:67;;466:4;457:14;;;;494:17;;;;343:1;336:9;309:212;;;313:3;;;14:513;;;;:::o;532:131::-;-1:-1:-1;;;;;607:31:195;;597:42;;587:70;;653:1;650;643:12;668:347;719:8;729:6;783:3;776:4;768:6;764:17;760:27;750:55;;801:1;798;791:12;750:55;-1:-1:-1;824:20:195;;867:18;856:30;;853:50;;;899:1;896;889:12;853:50;936:4;928:6;924:17;912:29;;988:3;981:4;972:6;964;960:19;956:30;953:39;950:59;;;1005:1;1002;995:12;1020:785;1117:6;1125;1133;1141;1149;1202:3;1190:9;1181:7;1177:23;1173:33;1170:53;;;1219:1;1216;1209:12;1170:53;1264:23;;;-1:-1:-1;1384:2:195;1369:18;;1356:32;;-1:-1:-1;1466:2:195;1451:18;;1438:32;1479:33;1438:32;1479:33;:::i;:::-;1531:7;-1:-1:-1;1589:2:195;1574:18;;1561:32;1616:18;1605:30;;1602:50;;;1648:1;1645;1638:12;1602:50;1687:58;1737:7;1728:6;1717:9;1713:22;1687:58;:::i;:::-;1020:785;;;;-1:-1:-1;1020:785:195;;-1:-1:-1;1764:8:195;;1661:84;1020:785;-1:-1:-1;;;1020:785:195:o;1810:118::-;1896:5;1889:13;1882:21;1875:5;1872:32;1862:60;;1918:1;1915;1908:12;1933:523;2007:6;2015;2023;2076:2;2064:9;2055:7;2051:23;2047:32;2044:52;;;2092:1;2089;2082:12;2044:52;2131:9;2118:23;2150:31;2175:5;2150:31;:::i;:::-;2200:5;-1:-1:-1;2257:2:195;2242:18;;2229:32;2270:33;2229:32;2270:33;:::i;:::-;2322:7;-1:-1:-1;2381:2:195;2366:18;;2353:32;2394:30;2353:32;2394:30;:::i;:::-;2443:7;2433:17;;;1933:523;;;;;:::o;3476:226::-;3535:6;3588:2;3576:9;3567:7;3563:23;3559:32;3556:52;;;3604:1;3601;3594:12;3556:52;-1:-1:-1;3649:23:195;;3476:226;-1:-1:-1;3476:226:195:o;3938:247::-;3997:6;4050:2;4038:9;4029:7;4025:23;4021:32;4018:52;;;4066:1;4063;4056:12;4018:52;4105:9;4092:23;4124:31;4149:5;4124:31;:::i;4190:142::-;-1:-1:-1;;;;;4269:5:195;4265:42;4258:5;4255:53;4245:81;;4322:1;4319;4312:12;4337:247;4396:6;4449:2;4437:9;4428:7;4424:23;4420:32;4417:52;;;4465:1;4462;4455:12;4417:52;4504:9;4491:23;4523:31;4548:5;4523:31;:::i;4990:664::-;5078:6;5086;5094;5102;5155:2;5143:9;5134:7;5130:23;5126:32;5123:52;;;5171:1;5168;5161:12;5123:52;5210:9;5197:23;5229:31;5254:5;5229:31;:::i;:::-;5279:5;-1:-1:-1;5357:2:195;5342:18;;5329:32;;-1:-1:-1;5438:2:195;5423:18;;5410:32;5465:18;5454:30;;5451:50;;;5497:1;5494;5487:12;5451:50;5536:58;5586:7;5577:6;5566:9;5562:22;5536:58;:::i;:::-;4990:664;;;;-1:-1:-1;5613:8:195;-1:-1:-1;;;;4990:664:195:o;5912:785::-;6009:6;6017;6025;6033;6041;6094:3;6082:9;6073:7;6069:23;6065:33;6062:53;;;6111:1;6108;6101:12;6062:53;6150:9;6137:23;6169:31;6194:5;6169:31;:::i;:::-;6219:5;-1:-1:-1;6297:2:195;6282:18;;6269:32;;-1:-1:-1;6400:2:195;6385:18;;6372:32;;-1:-1:-1;6481:2:195;6466:18;;6453:32;6508:18;6497:30;;6494:50;;;6540:1;6537;6530:12;7363:1355;7503:6;7511;7519;7527;7535;7543;7551;7559;7567;7575;7628:3;7616:9;7607:7;7603:23;7599:33;7596:53;;;7645:1;7642;7635:12;7596:53;7684:9;7671:23;7703:31;7728:5;7703:31;:::i;:::-;7753:5;-1:-1:-1;7810:2:195;7795:18;;7782:32;7823:33;7782:32;7823:33;:::i;:::-;7363:1355;;7875:7;;-1:-1:-1;;;;7955:2:195;7940:18;;7927:32;;8058:2;8043:18;;8030:32;;8161:3;8146:19;;8133:33;;-1:-1:-1;8265:3:195;8250:19;;8237:33;;-1:-1:-1;8369:3:195;8354:19;;8341:33;;-1:-1:-1;8473:3:195;8458:19;;8445:33;;-1:-1:-1;8577:3:195;8562:19;;8549:33;;-1:-1:-1;8681:3:195;8666:19;;;8653:33;;-1:-1:-1;7363:1355:195:o;8916:164::-;8993:13;;9046:1;9035:20;;;9025:31;;9015:59;;9070:1;9067;9060:12;9085:204;9153:6;9206:2;9194:9;9185:7;9181:23;9177:32;9174:52;;;9222:1;9219;9212:12;9174:52;9245:38;9273:9;9245:38;:::i;9659:311::-;9736:6;9744;9797:2;9785:9;9776:7;9772:23;9768:32;9765:52;;;9813:1;9810;9803:12;9765:52;9836:38;9864:9;9836:38;:::i;:::-;9936:2;9921:18;;;;9915:25;9826:48;;9915:25;;-1:-1:-1;;;9659:311:195:o;9975:678::-;10067:5;10061:12;10056:3;10049:25;10137:4;10130:5;10126:16;10120:23;10117:1;10106:38;10099:4;10094:3;10090:14;10083:62;10194:4;10187:5;10183:16;10177:23;10170:4;10165:3;10161:14;10154:47;10247:4;10240:5;10236:16;10230:23;10284:4;10279:3;10275:14;10365:1;10375:214;10389:4;10386:1;10383:11;10375:214;;;10454:13;;-1:-1:-1;;;;;10450:50:195;10436:65;;10534:4;10562:17;;;;10523:16;;;;10409:1;10402:9;10375:214;;;-1:-1:-1;;;10640:4:195;10629:16;10623:23;10614:6;10605:16;;;;10598:49;9975:678::o;10658:857::-;10947:3;10932:19;;10936:9;11028:6;10905:4;11072:361;11112:4;11108:1;11095:11;11091:19;11088:29;11072:361;;;11219:13;;-1:-1:-1;;;;;11257:45:195;;11245:58;;11343:3;11339:14;11332:4;11323:14;;11316:38;11383:2;11374:12;;;;11421:1;11409:14;;;;11172:1;11155:19;11072:361;;;11076:3;;;11442:67;11504:3;11493:9;11489:19;11481:6;11442:67;:::i;11520:127::-;11581:10;11576:3;11572:20;11569:1;11562:31;11612:4;11609:1;11602:15;11636:4;11633:1;11626:15;11652:252;11724:2;11718:9;11766:3;11754:16;;11800:18;11785:34;;11821:22;;;11782:62;11779:88;;;11847:18;;:::i;:::-;11883:2;11876:22;11652:252;:::o;11909:275::-;11980:2;11974:9;12045:2;12026:13;;-1:-1:-1;;12022:27:195;12010:40;;12080:18;12065:34;;12101:22;;;12062:62;12059:88;;;12127:18;;:::i;:::-;12163:2;12156:22;11909:275;;-1:-1:-1;11909:275:195:o;12189:620::-;12250:5;12303:3;12296:4;12288:6;12284:17;12280:27;12270:55;;12321:1;12318;12311:12;12270:55;12422:19;12400:2;12422:19;:::i;:::-;12465:3;12503:2;12495:6;12491:15;12529:3;12521:6;12518:15;12515:35;;;12546:1;12543;12536:12;12515:35;12570:6;12585:193;12601:6;12596:3;12593:15;12585:193;;;12693:10;;12716:18;;12763:4;12754:14;;;;12618;12585:193;;;-1:-1:-1;12796:7:195;;12189:620;-1:-1:-1;;;;;12189:620:195:o;12814:1146::-;12957:6;12965;12973;12981;13034:3;13022:9;13013:7;13009:23;13005:33;13002:53;;;13051:1;13048;13041:12;13002:53;13100:7;13093:4;13082:9;13078:20;13074:34;13064:62;;13122:1;13119;13112:12;13064:62;13224:20;13201:3;13224:20;:::i;:::-;13266:3;13307;13296:9;13292:19;13334:7;13326:6;13323:19;13320:39;;;13355:1;13352;13345:12;13320:39;13379:9;13397:268;13413:6;13408:3;13405:15;13397:268;;;13488:3;13482:10;-1:-1:-1;;;;;13529:5:195;13525:46;13518:5;13515:57;13505:85;;13586:1;13583;13576:12;13505:85;13603:18;;13650:4;13641:14;;;;13430;13397:268;;;-1:-1:-1;13734:13:195;13839:3;13824:19;;13818:26;13684:5;;-1:-1:-1;13734:13:195;-1:-1:-1;13818:26:195;-1:-1:-1;13889:65:195;;-1:-1:-1;13946:7:195;13940:3;13925:19;;13889:65;:::i;:::-;13879:75;;12814:1146;;;;;;;:::o;13965:127::-;14026:10;14021:3;14017:20;14014:1;14007:31;14057:4;14054:1;14047:15;14081:4;14078:1;14071:15;14097:128;14164:9;;;14185:11;;;14182:37;;;14199:18;;:::i;14230:266::-;14318:6;14313:3;14306:19;14370:6;14363:5;14356:4;14351:3;14347:14;14334:43;-1:-1:-1;14422:1:195;14397:16;;;14415:4;14393:27;;;14386:38;;;;14478:2;14457:15;;;-1:-1:-1;;14453:29:195;14444:39;;;14440:50;;14230:266::o;14501:485::-;14771:1;14767;14762:3;14758:11;14754:19;14746:6;14742:32;14731:9;14724:51;14811:6;14806:2;14795:9;14791:18;14784:34;14854:6;14849:2;14838:9;14834:18;14827:34;14897:3;14892:2;14881:9;14877:18;14870:31;14705:4;14918:62;14975:3;14964:9;14960:19;14952:6;14944;14918:62;:::i;14991:168::-;15064:9;;;15095;;15112:15;;;15106:22;;15092:37;15082:71;;15133:18;;:::i;15560:125::-;15625:9;;;15646:10;;;15643:36;;;15659:18;;:::i;15690:713::-;15765:12;;15799:3;15866:1;15876:177;15890:4;15887:1;15884:11;15876:177;;;15951:13;;15937:28;;15998:4;16026:17;;;;15987:16;;;;15910:1;15903:9;15876:177;;;15880:3;;;16102:4;16095:5;16091:16;16085:23;16078:4;16073:3;16069:14;16062:47;16158:4;16151:5;16147:16;16141:23;16134:4;16129:3;16125:14;16118:47;16216:4;16209:5;16205:16;16199:23;16190:6;16185:3;16181:16;16174:49;16274:4;16267:5;16263:16;16257:23;16248:6;16243:3;16239:16;16232:49;16332:4;16325:5;16321:16;16315:23;16306:6;16301:3;16297:16;16290:49;16390:4;16383:5;16379:16;16373:23;16364:6;16359:3;16355:16;16348:49;15690:713;;:::o;16408:359::-;16630:3;16615:19;;16643:48;16619:9;16673:6;16643:48;:::i;:::-;-1:-1:-1;;;;;16728:32:195;;;;16722:3;16707:19;;;;16700:61;16408:359;;-1:-1:-1;16408:359:195:o;17143:285::-;17218:6;17226;17279:2;17267:9;17258:7;17254:23;17250:32;17247:52;;;17295:1;17292;17285:12;17247:52;17318:38;17346:9;17318:38;:::i;:::-;17308:48;;17375:47;17418:2;17407:9;17403:18;17375:47;:::i;17433:127::-;17494:10;17489:3;17485:20;17482:1;17475:31;17525:4;17522:1;17515:15;17549:4;17546:1;17539:15;17565:557;17863:1;17859;17854:3;17850:11;17846:19;17838:6;17834:32;17823:9;17816:51;17903:6;17898:2;17887:9;17883:18;17876:34;17946:6;17941:2;17930:9;17926:18;17919:34;17989:6;17984:2;17973:9;17969:18;17962:34;18033:3;18027;18016:9;18012:19;18005:32;17797:4;18054:62;18111:3;18100:9;18096:19;18088:6;18080;18054:62;:::i;:::-;18046:70;17565:557;-1:-1:-1;;;;;;;;17565:557:195:o;18127:629::-;18453:1;18449;18444:3;18440:11;18436:19;18428:6;18424:32;18413:9;18406:51;18493:6;18488:2;18477:9;18473:18;18466:34;18536:6;18531:2;18520:9;18516:18;18509:34;18579:6;18574:2;18563:9;18559:18;18552:34;18623:6;18617:3;18606:9;18602:19;18595:35;18667:3;18661;18650:9;18646:19;18639:32;18387:4;18688:62;18745:3;18734:9;18730:19;18722:6;18714;18688:62;:::i;:::-;18680:70;18127:629;-1:-1:-1;;;;;;;;;18127:629:195:o;19445:127::-;19506:10;19501:3;19497:20;19494:1;19487:31;19537:4;19534:1;19527:15;19561:4;19558:1;19551:15;19577:120;19617:1;19643;19633:35;;19648:18;;:::i;:::-;-1:-1:-1;19682:9:195;;19577:120::o;19702:170::-;19799:10;19792:18;;;19772;;;19768:43;;19823:20;;19820:46;;;19846:18;;:::i;20085:230::-;20155:6;20208:2;20196:9;20187:7;20183:23;20179:32;20176:52;;;20224:1;20221;20214:12;20176:52;-1:-1:-1;20269:16:195;;20085:230;-1:-1:-1;20085:230:195:o;21266:385::-;21345:6;21353;21406:2;21394:9;21385:7;21381:23;21377:32;21374:52;;;21422:1;21419;21412:12;21374:52;21454:9;21448:16;21473:31;21498:5;21473:31;:::i;:::-;21573:2;21558:18;;21552:25;21523:5;;-1:-1:-1;21586:33:195;21552:25;21586:33;:::i;:::-;21638:7;21628:17;;;21266:385;;;;;:::o;21656:240::-;-1:-1:-1;;;;;21725:42:195;;;21769;;;21721:91;;21824:43;;21821:69;;;21870:18;;:::i;22277:251::-;22347:6;22400:2;22388:9;22379:7;22375:23;22371:32;22368:52;;;22416:1;22413;22406:12;22368:52;22448:9;22442:16;22467:31;22492:5;22467:31;:::i;22533:228::-;-1:-1:-1;;;;;22602:38:195;;;22642;;;22598:83;;22693:39;;22690:65;;;22735:18;;:::i;23214:231::-;-1:-1:-1;;;;;23325:38:195;;;23285;;;23281:83;;23376:40;;23373:66;;;23419:18;;:::i;23450:243::-;-1:-1:-1;;;;;23565:42:195;;;23521;;;23517:91;;23620:44;;23617:70;;;23667:18;;:::i;23698:960::-;24054:3;24039:19;;24067:48;24043:9;24097:6;24067:48;:::i;:::-;-1:-1:-1;;;;;24152:32:195;;;;24146:3;24131:19;;24124:61;24222:13;;24216:3;24201:19;;24194:42;24291:4;24279:17;;24273:24;24267:3;24252:19;;24245:53;24353:4;24341:17;;24335:24;24329:3;24314:19;;24307:53;24415:4;24403:17;;24397:24;24391:3;24376:19;;24369:53;24477:4;24465:17;;24459:24;24453:3;24438:19;;24431:53;24172:3;24527:17;;24521:24;24515:3;24500:19;;24493:53;24601:4;24589:17;;;24583:24;24577:3;24562:19;;24555:53;24639:3;24624:19;;;24617:35;23698:960;;-1:-1:-1;23698:960:195:o;24663:245::-;24730:6;24783:2;24771:9;24762:7;24758:23;24754:32;24751:52;;;24799:1;24796;24789:12;24751:52;24831:9;24825:16;24850:28;24872:5;24850:28;:::i;24913:721::-;-1:-1:-1;;;;;25327:32:195;;25309:51;;25296:3;25281:19;;25369:57;25422:2;25407:18;;25399:6;25369:57;:::i;:::-;-1:-1:-1;;;;;25463:32:195;;;;25457:3;25442:19;;25435:61;25527:3;25512:19;;25505:35;;;;25571:3;25556:19;;25549:35;;;;25615:3;25600:19;;;25593:35;24913:721;;-1:-1:-1;;24913:721:195:o;25639:433::-;25885:3;25870:19;;25898:48;25874:9;25928:6;25898:48;:::i;:::-;25990:14;;25983:22;25977:3;25962:19;;25955:51;26050:14;;26043:22;26037:3;26022:19;;;26015:51;25639:433;;-1:-1:-1;25639:433:195:o;26077:1075::-;26191:6;26251:3;26239:9;26230:7;26226:23;26222:33;26267:2;26264:22;;;26282:1;26279;26272:12;26264:22;-1:-1:-1;26324:22:195;;:::i;:::-;26391:16;;26416:22;;26504:2;26489:18;;;26483:25;26524:14;;;26517:31;26614:2;26599:18;;;26593:25;26634:14;;;26627:31;26724:2;26709:18;;;26703:25;26744:14;;;26737:31;26834:3;26819:19;;;26813:26;26855:15;;;26848:32;26946:3;26931:19;;;26925:26;26967:15;;;26960:32;27037:3;27022:19;;27016:26;27051:30;27016:26;27051:30;:::i;:::-;27108:3;27097:15;;27090:32;27101:5;26077:1075;-1:-1:-1;;;26077:1075:195:o;27157:136::-;27192:3;-1:-1:-1;;;27213:22:195;;27210:48;;27238:18;;:::i;:::-;-1:-1:-1;27278:1:195;27274:13;;27157:136::o;28132:385::-;28411:25;;;28398:3;28383:19;;28445:66;28507:2;28492:18;;28484:6;28445:66;:::i;28522:467::-;28633:6;28641;28649;28702:3;28690:9;28681:7;28677:23;28673:33;28670:53;;;28719:1;28716;28709:12;28670:53;28764:16;;28870:2;28855:18;;28849:25;28764:16;;-1:-1:-1;28849:25:195;-1:-1:-1;28919:64:195;28975:7;28970:2;28955:18;;28919:64;:::i;:::-;28909:74;;28522:467;;;;;:::o;29458:216::-;29522:9;;;29550:11;;;29497:3;29580:9;;29608:10;;29604:19;;29633:10;;29625:19;;29601:44;29598:70;;;29648:18;;:::i;:::-;29598:70;;29458:216;;;;:::o;29679:200::-;29745:9;;;29718:4;29773:9;;29801:10;;29813:12;;;29797:29;29836:12;;;29828:21;;29794:56;29791:82;;;29853:18;;:::i;29884:193::-;29923:1;29949;29939:35;;29954:18;;:::i;:::-;-1:-1:-1;;;29990:18:195;;-1:-1:-1;;30010:13:195;;29986:38;29983:64;;;30027:18;;:::i;:::-;-1:-1:-1;30061:10:195;;29884:193::o", "linkReferences": {"contracts/libraries/Interest.sol": {"Interest": [{"start": 1700, "length": 20}, {"start": 15591, "length": 20}]}, "contracts/libraries/Liquidation.sol": {"Liquidation": [{"start": 13053, "length": 20}, {"start": 13209, "length": 20}]}}, "immutableReferences": {"17857": [{"start": 5464, "length": 32}], "17860": [{"start": 5498, "length": 32}], "17863": [{"start": 3056, "length": 32}], "17866": [{"start": 3093, "length": 32}], "17869": [{"start": 3133, "length": 32}], "17872": [{"start": 3175, "length": 32}, {"start": 8344, "length": 32}], "17875": [{"start": 3215, "length": 32}], "17878": [{"start": 3255, "length": 32}], "17927": [{"start": 3713, "length": 32}, {"start": 9738, "length": 32}, {"start": 15771, "length": 32}], "17930": [{"start": 1192, "length": 32}, {"start": 1417, "length": 32}, {"start": 2656, "length": 32}, {"start": 2911, "length": 32}, {"start": 3801, "length": 32}, {"start": 8047, "length": 32}, {"start": 8673, "length": 32}, {"start": 8899, "length": 32}, {"start": 11075, "length": 32}, {"start": 12736, "length": 32}, {"start": 13085, "length": 32}, {"start": 14208, "length": 32}, {"start": 15287, "length": 32}, {"start": 18808, "length": 32}]}}, "methodIdentifiers": {"borrow(address,uint256,uint256,bytes)": "8b049907", "borrowLiquidity(address,uint256,bytes)": "8726b94c", "burn(address)": "89afcb44", "deposit(address)": "f340fa01", "externalLiquidity()": "861d4d88", "getReserves()": "0902f1ac", "getTickRange()": "37116566", "liquidate(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256)": "c9e7cde8", "mint(address)": "6a627842", "referenceReserves()": "2c0e7587", "repay(address)": "9f7c73e9", "repayLiquidity(address)": "c64b1ea4", "skim(address)": "bc25cf77", "swap(uint256,uint256,address,bytes)": "022c0d9f", "sync()": "fff6cae9", "tokens(uint256)": "4f64b2be", "totalAssets()": "01e1d114", "underlyingTokens()": "bd27dc9f", "updateExternalLiquidity(uint112)": "6945e18f", "validateOnUpdate(address,address,bool)": "032ca742", "withdraw(address)": "51cff8d9"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"AmmalgamCannotBorrowAgainstSameCollateral\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamDepositIsNotStrictlyBigger\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamLTV\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamMaxBorrowReached\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamMaxSlippage\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AmmalgamTooMuchLeverage\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Forbidden\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientInputAmount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientLiquidity\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientLiquidity\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientLiquidityBurned\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientLiquidityMinted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientOutputAmount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientRepayLiquidity\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidToAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"K\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Locked\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotEnoughRepaidForLiquidation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Overflow\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PriceOutOfBounds\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"bits\",\"type\":\"uint8\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"SafeCastOverflowedUintDowncast\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TickOutOfBounds\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"badDebtAssets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"badDebtShares\",\"type\":\"uint256\"}],\"name\":\"BurnBadDebt\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositLAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositXAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositYAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowLAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowXAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowYAssets\",\"type\":\"uint128\"}],\"name\":\"InterestAccrued\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"depositL\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"depositX\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"depositY\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayLX\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayLY\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayX\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"repayY\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"liquidationType\",\"type\":\"uint256\"}],\"name\":\"Liquidate\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountXIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountYIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountXOut\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountYOut\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"Swap\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reserveXAssets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reserveYAssets\",\"type\":\"uint256\"}],\"name\":\"Sync\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint112\",\"name\":\"externalLiquidity\",\"type\":\"uint112\"}],\"name\":\"UpdateExternalLiquidity\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountYAssets\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"borrow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"borrowAmountLAssets\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"borrowLiquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"burn\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"amountXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountYAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"externalLiquidity\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getReserves\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"_reserveXAssets\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"_reserveYAssets\",\"type\":\"uint112\"},{\"internalType\":\"uint32\",\"name\":\"_lastTimestamp\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTickRange\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"minTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"maxTick\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"depositLToBeTransferredInLAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"depositXToBeTransferredInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"depositYToBeTransferredInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayLXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayLYInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayYInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"liquidationType\",\"type\":\"uint256\"}],\"name\":\"liquidate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"mint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"liquidityShares\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"referenceReserves\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"}],\"name\":\"repay\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"repayXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayYInYAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"}],\"name\":\"repayLiquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"repaidLXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repaidLYInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayLiquidityAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"skim\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amountXOut\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountYOut\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"swap\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"sync\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"}],\"name\":\"tokens\",\"outputs\":[{\"internalType\":\"contract IAmmalgamERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalAssets\",\"outputs\":[{\"internalType\":\"uint128[6]\",\"name\":\"\",\"type\":\"uint128[6]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"underlyingTokens\",\"outputs\":[{\"internalType\":\"contract IERC20\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"contract IERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint112\",\"name\":\"_externalLiquidity\",\"type\":\"uint112\"}],\"name\":\"updateExternalLiquidity\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"validate\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"update\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"isBorrow\",\"type\":\"bool\"}],\"name\":\"validateOnUpdate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"SafeCastOverflowedUintDowncast(uint8,uint256)\":[{\"details\":\"Value doesn't fit in an uint of `bits` size.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}]},\"events\":{\"BurnBadDebt(address,uint256,uint256,uint256)\":{\"details\":\"Emitted when bad debt is burned\",\"params\":{\"badDebtAssets\":\"The amount of bad debt assets being burned\",\"badDebtShares\":\"The amount of bad debt shares being burned\",\"borrower\":\"The address of the borrower\",\"tokenType\":\"The type of token being burned\"}},\"InterestAccrued(uint128,uint128,uint128,uint128,uint128,uint128)\":{\"details\":\"Emitted when Interest gets accrued\",\"params\":{\"borrowLAssets\":\"The amount of total `BORROW_L` assets in the pool after interest accrual\",\"borrowXAssets\":\"The amount of total `BORROW_X` assets in the pool after interest accrual\",\"borrowYAssets\":\"The amount of total `BORROW_Y` assets in the pool after interest accrual\",\"depositLAssets\":\"The amount of total `DEPOSIT_L` assets in the pool after interest accrual\",\"depositXAssets\":\"The amount of total `DEPOSIT_X` assets in the pool after interest accrual\",\"depositYAssets\":\"The amount of total `DEPOSIT_Y` assets in the pool after interest accrual\"}},\"Liquidate(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256)\":{\"details\":\"Emitted on liquidation\",\"params\":{\"borrower\":\"The account being liquidated.\",\"depositL\":\"The amount of L tokens to be transferred from the hard deposit.\",\"depositX\":\"The amount of X tokens to be transferred from the hard deposit.\",\"depositY\":\"The amount of Y tokens to be transferred from the hard deposit.\",\"liquidationType\":\"The type of liquidation to be performed: HARD, SOFT, LEVERAGE\",\"repayLX\":\"The amount of L tokens repaid in X.\",\"repayLY\":\"The amount of L tokens repaid in Y.\",\"repayX\":\"The amount of X tokens repaid.\",\"repayY\":\"The amount of Y tokens repaid.\",\"to\":\"The account to send the liquidated deposit to\"}},\"Swap(address,uint256,uint256,uint256,uint256,address)\":{\"details\":\"Emitted on a token swap\",\"params\":{\"amountXIn\":\"The amount of token X provided for the swap\",\"amountXOut\":\"The amount of token X received from the swap\",\"amountYIn\":\"The amount of token Y provided for the swap\",\"amountYOut\":\"The amount of token Y received from the swap\",\"sender\":\"The address initiating the swap\",\"to\":\"Address where the swapped tokens are sent\"}},\"Sync(uint256,uint256)\":{\"details\":\"Emitted when reserves are synchronized\",\"params\":{\"reserveXAssets\":\"The updated reserve for token X\",\"reserveYAssets\":\"The updated reserve for token Y\"}},\"UpdateExternalLiquidity(uint112)\":{\"details\":\"Emitted when external liquidity is updated\",\"params\":{\"externalLiquidity\":\"The updated value for external liquidity\"}}},\"kind\":\"dev\",\"methods\":{\"borrow(address,uint256,uint256,bytes)\":{\"details\":\"Verifies the borrowing amounts, mints corresponding debt tokens, transfers the assets, and updates missing assets. Also supports flash loan interactions.\",\"params\":{\"amountXAssets\":\"Amount of asset X to borrow.\",\"amountYAssets\":\"Amount of asset Y to borrow.\",\"data\":\"Call data to be sent to external contract if flash loan interaction is desired.\",\"to\":\"Address to which the borrowed assets will be transferred.\"}},\"burn(address)\":{\"details\":\"Calculates the amounts of assets to be returned based on liquidity.      Requires amountXAssets and amountYAssets to be greater than 0.      Emits a #Burn event and performs a safe transfer of assets.\",\"params\":{\"to\":\"address to which the underlying assets will be transferred\"},\"returns\":{\"amountXAssets\":\"amount of first token to be returned\",\"amountYAssets\":\"amount of second token to be returned\"}},\"deposit(address)\":{\"details\":\"Verifies deposit amounts and types, adjusts reserves if necessary, mints corresponding tokens, and updates missing assets.\",\"params\":{\"to\":\"Address to which tokens will be minted.\"}},\"getReserves()\":{\"returns\":{\"_lastTimestamp\":\"The timestamp of the last operation.\",\"_reserveXAssets\":\"The current reserve of asset X.\",\"_reserveYAssets\":\"The current reserve of asset Y.\"}},\"liquidate(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256)\":{\"params\":{\"borrower\":\"The account being liquidated\",\"depositLToBeTransferredInLAssets\":\"The amount of L to be transferred to the liquidator.\",\"depositXToBeTransferredInXAssets\":\"The amount of X to be transferred to the liquidator.\",\"depositYToBeTransferredInYAssets\":\"The amount of Y to be transferred to the liquidator.\",\"liquidationType\":\"The type of liquidation to be performed: HARD, SOFT, LEVERAGE\",\"repayLXInXAssets\":\"The amount of LX to be repaid by the liquidator.\",\"repayLYInYAssets\":\"The amount of LY to be repaid by the liquidator.\",\"repayXInXAssets\":\"The amount of X to be repaid by the liquidator.\",\"repayYInYAssets\":\"The amount of Y to be repaid by the liquidator.\",\"to\":\"The account to send the liquidated deposit to\"}},\"mint(address)\":{\"details\":\"Calculates the amount of tokens to mint based on reserves and balances. Requires liquidity > 0. Emits a #Mint event.\",\"params\":{\"to\":\"address to which tokens will be minted\"},\"returns\":{\"liquidityShares\":\"amount of tokens minted\"}},\"referenceReserves()\":{\"returns\":{\"_0\":\"The reference reserve for asset X.\",\"_1\":\"The reference reserve for asset Y.\"}},\"repay(address)\":{\"details\":\"Burns corresponding borrowed tokens, adjusts the reserves, and updates missing assets.\",\"params\":{\"onBehalfOf\":\"Address of the entity on whose behalf the repayment is made.\"},\"returns\":{\"repayXInXAssets\":\"Amount of token X repaid\",\"repayYInYAssets\":\"Amount of token Y repaid\"}},\"repayLiquidity(address)\":{\"details\":\"Calculates repayable liquidity, burns corresponding tokens, adjusts reserves, and updates active liquidity.\",\"params\":{\"onBehalfOf\":\"Address of the entity on whose behalf the liquidity repayment is made.\"},\"returns\":{\"repaidLXInXAssets\":\"Amount of liquidity repaid in X.\",\"repaidLYInYAssets\":\"Amount of liquidity repaid in Y.\",\"repayLiquidityAssets\":\"Amount of liquidity repaid in L.\"}},\"skim(address)\":{\"details\":\"Calculates the excess of tokenX and tokenY balances and transfers them to the specified address.\",\"params\":{\"to\":\"The address to which the excess tokens are transferred.\"}},\"swap(uint256,uint256,address,bytes)\":{\"details\":\"Requires at least one of `amountXOut` and `amountYOut` to be greater than 0,      and that the amount out does not exceed the reserves.      An optimistically transfer of tokens is performed.      A callback is executed if `data` is not empty.      Emits a #Swap event.\",\"params\":{\"amountXOut\":\"Amount of first token to be swapped out.\",\"amountYOut\":\"Amount of second token to be swapped out.\",\"data\":\"Data to be sent along with the call, can be used for a callback.\",\"to\":\"Address to which the swapped tokens are sent.\"}},\"sync()\":{\"details\":\"Reads the current balance of tokenX and tokenY in the contract, and updates the reserves to match these balances.\"},\"tokens(uint256)\":{\"params\":{\"tokenType\":\"The type of token for which the scaler is being computed.                  Can be one of BORROW_X, DEPOSIT_X, BORROW_Y, DEPOSIT_Y, BORROW_L, or DEPOSIT_L.\"},\"returns\":{\"_0\":\"The IAmmalgamERC20 token\"}},\"totalAssets()\":{\"details\":\"If the last lending state update is outdated (i.e., not matching the current block timestamp),      the function recalculates the assets based on the duration since the last update, the lending state,      and reserve balances. If the timestamp is current, the previous asset (without recalculation) is returned.\",\"returns\":{\"_0\":\"totalAssets An array of six `uint128` values representing the total assets for each of the 6 amalgam token types.  These values may be adjusted based on the time elapsed since the last update. If the timestamp is up-to-date, the  previously calculated total assets are returned without recalculation.\"}},\"underlyingTokens()\":{\"returns\":{\"_0\":\"The addresses of the underlying tokens.\"}},\"updateExternalLiquidity(uint112)\":{\"details\":\"This function sets the external liquidity to a new value and emits an event with the new value. It can only be called by the fee setter.\",\"params\":{\"_externalLiquidity\":\"The new external liquidity value.\"}},\"validateOnUpdate(address,address,bool)\":{\"details\":\"Implementation should properly protect against any creation of new debt or transfer of existing debt or collateral that would leave any individual address with insufficient collateral to cover all debts.\",\"params\":{\"update\":\"The address of the account having its saturation updated\",\"validate\":\"The address of the account being checked for solvency and having its saturation updated\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"borrow(address,uint256,uint256,bytes)\":{\"notice\":\"Handles borrowing from the contract.\"},\"burn(address)\":{\"notice\":\"Burns liquidity tokens from the contract and sends the underlying assets to `to` address.\"},\"deposit(address)\":{\"notice\":\"Handles deposits into the contract.\"},\"getReserves()\":{\"notice\":\"Fetches the current reserves of asset X and asset Y, as well as the block of the last operation.\"},\"liquidate(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256)\":{\"notice\":\"LTV based liquidation. The LTV dictates the max premium that can be had by the liquidator.\"},\"mint(address)\":{\"notice\":\"Mints tokens and assigns them to `to` address.\"},\"referenceReserves()\":{\"notice\":\"Returns the reference reserves for the block, these represent a snapshot of the   reserves at the start of the block weighted for mints, burns, borrow and repayment of   liquidity. These amounts are critical to calculating the correct fees for any swap.\"},\"repay(address)\":{\"notice\":\"Handles repayment of borrowed assets.\"},\"repayLiquidity(address)\":{\"notice\":\"Handles repayment of borrowed liquidity.\"},\"skim(address)\":{\"notice\":\"Transfers excess tokens to a specified address.\"},\"swap(uint256,uint256,address,bytes)\":{\"notice\":\"Executes a swap of tokens.\"},\"sync()\":{\"notice\":\"Updates the reserves to match the current token balances.\"},\"tokens(uint256)\":{\"notice\":\"Return the IAmmalgamERC20 token corresponding to the token type\"},\"totalAssets()\":{\"notice\":\"Computes the current total Assets.\"},\"underlyingTokens()\":{\"notice\":\"Get the underlying tokens for the AmmalgamERC20Controller.\"},\"updateExternalLiquidity(uint112)\":{\"notice\":\"Updates the external liquidity value.\"},\"validateOnUpdate(address,address,bool)\":{\"notice\":\"Validates the solvency of an account for a given token transfer operation.\"},\"withdraw(address)\":{\"notice\":\"withdraw X and/or Y\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/AmmalgamPair.sol\":\"AmmalgamPair\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/AmmalgamPair.sol\":{\"keccak256\":\"0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4\",\"urls\":[\"bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2\",\"dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS\"]},\"contracts/SaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76\",\"dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE\"]},\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/ISaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20\",\"dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR\"]},\"contracts/interfaces/callbacks/IAmmalgamCallee.sol\":{\"keccak256\":\"0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d\",\"dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/factories/IFactoryCallback.sol\":{\"keccak256\":\"0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b\",\"dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT\"]},\"contracts/interfaces/factories/INewTokensFactory.sol\":{\"keccak256\":\"0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b\",\"dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/GeometricTWAP.sol\":{\"keccak256\":\"0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa\",\"dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz\"]},\"contracts/libraries/Interest.sol\":{\"keccak256\":\"0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194\",\"dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX\"]},\"contracts/libraries/Liquidation.sol\":{\"keccak256\":\"0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877\",\"dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/Saturation.sol\":{\"keccak256\":\"0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20\",\"dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/Uint16Set.sol\":{\"keccak256\":\"0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06\",\"dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/TokenController.sol\":{\"keccak256\":\"0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159\",\"dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn\"]},\"lib/mangrove-core/lib/core/BitLib.sol\":{\"keccak256\":\"0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8\",\"dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr\"]},\"lib/morpho-blue/src/libraries/MathLib.sol\":{\"keccak256\":\"0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08\",\"dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "AmmalgamCannotBorrowAgainstSameCollateral"}, {"inputs": [], "type": "error", "name": "AmmalgamDepositIsNotStrictlyBigger"}, {"inputs": [], "type": "error", "name": "AmmalgamLTV"}, {"inputs": [], "type": "error", "name": "AmmalgamMaxBorrowReached"}, {"inputs": [], "type": "error", "name": "AmmalgamMaxSlippage"}, {"inputs": [], "type": "error", "name": "AmmalgamTooMuchLeverage"}, {"inputs": [], "type": "error", "name": "Forbidden"}, {"inputs": [], "type": "error", "name": "InsufficientInputAmount"}, {"inputs": [], "type": "error", "name": "InsufficientLiquidity"}, {"inputs": [], "type": "error", "name": "InsufficientLiquidity"}, {"inputs": [], "type": "error", "name": "InsufficientLiquidityBurned"}, {"inputs": [], "type": "error", "name": "InsufficientLiquidityMinted"}, {"inputs": [], "type": "error", "name": "InsufficientOutputAmount"}, {"inputs": [], "type": "error", "name": "InsufficientRepayLiquidity"}, {"inputs": [], "type": "error", "name": "InvalidToAddress"}, {"inputs": [], "type": "error", "name": "K"}, {"inputs": [], "type": "error", "name": "Locked"}, {"inputs": [], "type": "error", "name": "NotEnoughRepaidForLiquidation"}, {"inputs": [], "type": "error", "name": "Overflow"}, {"inputs": [], "type": "error", "name": "PriceOutOfBounds"}, {"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "type": "error", "name": "SafeCastOverflowedUintDowncast"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [], "type": "error", "name": "TickOutOfBounds"}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "tokenType", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "badDebtAssets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "badDebtShares", "type": "uint256", "indexed": false}], "type": "event", "name": "BurnBadDebt", "anonymous": false}, {"inputs": [{"internalType": "uint128", "name": "depositLAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "depositXAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "depositYAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowLAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowXAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowYAssets", "type": "uint128", "indexed": false}], "type": "event", "name": "InterestAccrued", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "depositL", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "depositX", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "depositY", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "repayLX", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "repayLY", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "repayX", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "repayY", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "liquidationType", "type": "uint256", "indexed": false}], "type": "event", "name": "Liquidate", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amountXIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amountYIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amountXOut", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amountYOut", "type": "uint256", "indexed": false}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}], "type": "event", "name": "<PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "reserveXAssets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "reserveYAssets", "type": "uint256", "indexed": false}], "type": "event", "name": "Sync", "anonymous": false}, {"inputs": [{"internalType": "uint112", "name": "externalLiquidity", "type": "uint112", "indexed": false}], "type": "event", "name": "UpdateExternalLiquidity", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amountXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "amountYAssets", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "borrow"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "borrowAmountLAssets", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "borrowLiquidity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "burn", "outputs": [{"internalType": "uint256", "name": "amountXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "amountYAssets", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "deposit"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "externalLiquidity", "outputs": [{"internalType": "uint112", "name": "", "type": "uint112"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getReserves", "outputs": [{"internalType": "uint112", "name": "_reserveXAssets", "type": "uint112"}, {"internalType": "uint112", "name": "_reserveYAssets", "type": "uint112"}, {"internalType": "uint32", "name": "_lastTimestamp", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getTickRange", "outputs": [{"internalType": "int16", "name": "minTick", "type": "int16"}, {"internalType": "int16", "name": "maxTick", "type": "int16"}]}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "depositLToBeTransferredInLAssets", "type": "uint256"}, {"internalType": "uint256", "name": "depositXToBeTransferredInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "depositYToBeTransferredInYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayLXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayLYInYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayYInYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "liquidationType", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "liquidate"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "mint", "outputs": [{"internalType": "uint256", "name": "liquidityShares", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "referenceReserves", "outputs": [{"internalType": "uint112", "name": "", "type": "uint112"}, {"internalType": "uint112", "name": "", "type": "uint112"}]}, {"inputs": [{"internalType": "address", "name": "onBehalfOf", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "repay", "outputs": [{"internalType": "uint256", "name": "repayXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayYInYAssets", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "onBehalfOf", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "repayLiquidity", "outputs": [{"internalType": "uint256", "name": "repaidLXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repaidLYInYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayLiquidityAssets", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "skim"}, {"inputs": [{"internalType": "uint256", "name": "amountXOut", "type": "uint256"}, {"internalType": "uint256", "name": "amountYOut", "type": "uint256"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "swap"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "sync"}, {"inputs": [{"internalType": "uint256", "name": "tokenType", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "tokens", "outputs": [{"internalType": "contract IAmmalgamERC20", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalAssets", "outputs": [{"internalType": "uint128[6]", "name": "", "type": "uint128[6]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "underlyingTokens", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}, {"internalType": "contract IERC20", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint112", "name": "_externalLiquidity", "type": "uint112"}], "stateMutability": "nonpayable", "type": "function", "name": "updateExternalLiquidity"}, {"inputs": [{"internalType": "address", "name": "validate", "type": "address"}, {"internalType": "address", "name": "update", "type": "address"}, {"internalType": "bool", "name": "isBorrow", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "validateOnUpdate"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw"}], "devdoc": {"kind": "dev", "methods": {"borrow(address,uint256,uint256,bytes)": {"details": "Verifies the borrowing amounts, mints corresponding debt tokens, transfers the assets, and updates missing assets. Also supports flash loan interactions.", "params": {"amountXAssets": "Amount of asset X to borrow.", "amountYAssets": "Amount of asset Y to borrow.", "data": "Call data to be sent to external contract if flash loan interaction is desired.", "to": "Address to which the borrowed assets will be transferred."}}, "burn(address)": {"details": "Calculates the amounts of assets to be returned based on liquidity.      Requires amountXAssets and amountYAssets to be greater than 0.      Emits a #Burn event and performs a safe transfer of assets.", "params": {"to": "address to which the underlying assets will be transferred"}, "returns": {"amountXAssets": "amount of first token to be returned", "amountYAssets": "amount of second token to be returned"}}, "deposit(address)": {"details": "Verifies deposit amounts and types, adjusts reserves if necessary, mints corresponding tokens, and updates missing assets.", "params": {"to": "Address to which tokens will be minted."}}, "getReserves()": {"returns": {"_lastTimestamp": "The timestamp of the last operation.", "_reserveXAssets": "The current reserve of asset X.", "_reserveYAssets": "The current reserve of asset Y."}}, "liquidate(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256)": {"params": {"borrower": "The account being liquidated", "depositLToBeTransferredInLAssets": "The amount of L to be transferred to the liquidator.", "depositXToBeTransferredInXAssets": "The amount of X to be transferred to the liquidator.", "depositYToBeTransferredInYAssets": "The amount of Y to be transferred to the liquidator.", "liquidationType": "The type of liquidation to be performed: HARD, SOFT, LEVERAGE", "repayLXInXAssets": "The amount of LX to be repaid by the liquidator.", "repayLYInYAssets": "The amount of LY to be repaid by the liquidator.", "repayXInXAssets": "The amount of X to be repaid by the liquidator.", "repayYInYAssets": "The amount of Y to be repaid by the liquidator.", "to": "The account to send the liquidated deposit to"}}, "mint(address)": {"details": "Calculates the amount of tokens to mint based on reserves and balances. Requires liquidity > 0. Emits a #Mint event.", "params": {"to": "address to which tokens will be minted"}, "returns": {"liquidityShares": "amount of tokens minted"}}, "referenceReserves()": {"returns": {"_0": "The reference reserve for asset X.", "_1": "The reference reserve for asset Y."}}, "repay(address)": {"details": "<PERSON> corresponding borrowed tokens, adjusts the reserves, and updates missing assets.", "params": {"onBehalfOf": "Address of the entity on whose behalf the repayment is made."}, "returns": {"repayXInXAssets": "Amount of token X repaid", "repayYInYAssets": "Amount of token Y repaid"}}, "repayLiquidity(address)": {"details": "Calculates repayable liquidity, burns corresponding tokens, adjusts reserves, and updates active liquidity.", "params": {"onBehalfOf": "Address of the entity on whose behalf the liquidity repayment is made."}, "returns": {"repaidLXInXAssets": "Amount of liquidity repaid in X.", "repaidLYInYAssets": "Amount of liquidity repaid in Y.", "repayLiquidityAssets": "Amount of liquidity repaid in L."}}, "skim(address)": {"details": "Calculates the excess of tokenX and tokenY balances and transfers them to the specified address.", "params": {"to": "The address to which the excess tokens are transferred."}}, "swap(uint256,uint256,address,bytes)": {"details": "Requires at least one of `amountXOut` and `amountYOut` to be greater than 0,      and that the amount out does not exceed the reserves.      An optimistically transfer of tokens is performed.      A callback is executed if `data` is not empty.      Emits a #Swap event.", "params": {"amountXOut": "Amount of first token to be swapped out.", "amountYOut": "Amount of second token to be swapped out.", "data": "Data to be sent along with the call, can be used for a callback.", "to": "Address to which the swapped tokens are sent."}}, "sync()": {"details": "Reads the current balance of tokenX and tokenY in the contract, and updates the reserves to match these balances."}, "tokens(uint256)": {"params": {"tokenType": "The type of token for which the scaler is being computed.                  Can be one of BORROW_X, DEPOSIT_X, BORROW_Y, DEPOSIT_Y, BORROW_L, or DEPOSIT_L."}, "returns": {"_0": "The IAmmalgamERC20 token"}}, "totalAssets()": {"details": "If the last lending state update is outdated (i.e., not matching the current block timestamp),      the function recalculates the assets based on the duration since the last update, the lending state,      and reserve balances. If the timestamp is current, the previous asset (without recalculation) is returned.", "returns": {"_0": "totalAssets An array of six `uint128` values representing the total assets for each of the 6 amalgam token types.  These values may be adjusted based on the time elapsed since the last update. If the timestamp is up-to-date, the  previously calculated total assets are returned without recalculation."}}, "underlyingTokens()": {"returns": {"_0": "The addresses of the underlying tokens."}}, "updateExternalLiquidity(uint112)": {"details": "This function sets the external liquidity to a new value and emits an event with the new value. It can only be called by the fee setter.", "params": {"_externalLiquidity": "The new external liquidity value."}}, "validateOnUpdate(address,address,bool)": {"details": "Implementation should properly protect against any creation of new debt or transfer of existing debt or collateral that would leave any individual address with insufficient collateral to cover all debts.", "params": {"update": "The address of the account having its saturation updated", "validate": "The address of the account being checked for solvency and having its saturation updated"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"borrow(address,uint256,uint256,bytes)": {"notice": "<PERSON><PERSON> borrowing from the contract."}, "burn(address)": {"notice": "Burns liquidity tokens from the contract and sends the underlying assets to `to` address."}, "deposit(address)": {"notice": "Handles deposits into the contract."}, "getReserves()": {"notice": "Fetches the current reserves of asset X and asset Y, as well as the block of the last operation."}, "liquidate(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,uint256)": {"notice": "LTV based liquidation. The LTV dictates the max premium that can be had by the liquidator."}, "mint(address)": {"notice": "Mints tokens and assigns them to `to` address."}, "referenceReserves()": {"notice": "Returns the reference reserves for the block, these represent a snapshot of the   reserves at the start of the block weighted for mints, burns, borrow and repayment of   liquidity. These amounts are critical to calculating the correct fees for any swap."}, "repay(address)": {"notice": "Handles repayment of borrowed assets."}, "repayLiquidity(address)": {"notice": "Handles repayment of borrowed liquidity."}, "skim(address)": {"notice": "Transfers excess tokens to a specified address."}, "swap(uint256,uint256,address,bytes)": {"notice": "Executes a swap of tokens."}, "sync()": {"notice": "Updates the reserves to match the current token balances."}, "tokens(uint256)": {"notice": "Return the IAmmalgamERC20 token corresponding to the token type"}, "totalAssets()": {"notice": "Computes the current total Assets."}, "underlyingTokens()": {"notice": "Get the underlying tokens for the AmmalgamERC20Controller."}, "updateExternalLiquidity(uint112)": {"notice": "Updates the external liquidity value."}, "validateOnUpdate(address,address,bool)": {"notice": "Validates the solvency of an account for a given token transfer operation."}, "withdraw(address)": {"notice": "withdraw X and/or Y"}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/AmmalgamPair.sol": "AmmalgamPair"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/AmmalgamPair.sol": {"keccak256": "0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4", "urls": ["bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2", "dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS"], "license": null}, "contracts/SaturationAndGeometricTWAPState.sol": {"keccak256": "0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419", "urls": ["bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76", "dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE"], "license": "GPL-3.0-only"}, "contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/ISaturationAndGeometricTWAPState.sol": {"keccak256": "0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c", "urls": ["bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20", "dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/IAmmalgamCallee.sol": {"keccak256": "0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339", "urls": ["bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d", "dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IFactoryCallback.sol": {"keccak256": "0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52", "urls": ["bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b", "dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/INewTokensFactory.sol": {"keccak256": "0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9", "urls": ["bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b", "dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/GeometricTWAP.sol": {"keccak256": "0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e", "urls": ["bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa", "dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz"], "license": "GPL-3.0-only"}, "contracts/libraries/Interest.sol": {"keccak256": "0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646", "urls": ["bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194", "dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX"], "license": "GPL-3.0-only"}, "contracts/libraries/Liquidation.sol": {"keccak256": "0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63", "urls": ["bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877", "dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/Saturation.sol": {"keccak256": "0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860", "urls": ["bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20", "dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Uint16Set.sol": {"keccak256": "0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f", "urls": ["bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06", "dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy"], "license": "GPL-3.0-only"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/TokenController.sol": {"keccak256": "0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6", "urls": ["bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159", "dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn"], "license": "GPL-3.0-only"}, "lib/mangrove-core/lib/core/BitLib.sol": {"keccak256": "0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3", "urls": ["bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8", "dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr"], "license": "MIT"}, "lib/morpho-blue/src/libraries/MathLib.sol": {"keccak256": "0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a", "urls": ["bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08", "dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd"], "license": "GPL-2.0-or-later"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}}, "version": 1}, "id": 0}