{"abi": [{"type": "constructor", "inputs": [{"name": "_fixture", "type": "address", "internalType": "contract FactoryPairTestFixture"}, {"name": "_liquidator", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "ammalgamBorrowCallV1", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "borrowedLXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "borrowedLYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ammalgamBorrowLiquidityCallV1", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "borrowedLXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "borrowedLYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ammalgamLiquidateCallV1", "inputs": [{"name": "repayXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayYInYAssets", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ammalgamSwapCallV1", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "2636:4213:133:-:0;;;2744:135;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2820:7;:18;;-1:-1:-1;;;;;2820:18:133;;;-1:-1:-1;;;;;;2820:18:133;;;;;;;;2848:24;;;;;;;;;;;2636:4213;;14:155:195;-1:-1:-1;;;;;113:31:195;;103:42;;93:70;;159:1;156;149:12;93:70;14:155;:::o;174:466::-;286:6;294;347:2;335:9;326:7;322:23;318:32;315:52;;;363:1;360;353:12;315:52;395:9;389:16;414:55;463:5;414:55;:::i;:::-;538:2;523:18;;517:25;488:5;;-1:-1:-1;551:57:195;517:25;551:57;:::i;:::-;627:7;617:17;;;174:466;;;;;:::o;:::-;2636:4213:133;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "2636:4213:133:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2885:82;;;;;;:::i;:::-;;;;;;;;;5011:1728;;;;;;:::i;:::-;;:::i;6745:94::-;;;;;;:::i;:::-;;;;2981:2024;;;;;;:::i;:::-;;:::i;5011:1728::-;5236:16;;;5284:39;;;;5295:4;5284:39;:::i;:::-;5334:7;;5359:21;;;-1:-1:-1;;;5359:21:133;;;;5235:88;;-1:-1:-1;5235:88:133;;-1:-1:-1;5235:88:133;;-1:-1:-1;;;;;;5334:7:133;;:24;;:7;;5359:19;;:21;;;;;;;;;;;;;;;5334:7;5359:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5382:16;5400;5334:83;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;5427:7;;;;;;;;;-1:-1:-1;;;;;5427:7:133;-1:-1:-1;;;;;5427:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;5427:24:133;;5465:8;5475:7;;;;;;;;;-1:-1:-1;;;;;5475:7:133;-1:-1:-1;;;;;5475:19:133;;:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5498:8;:16;;5513:1;5498:16;;;5509:1;5498:16;5516:1;5519;5522:6;:14;;5535:1;5522:14;;;5531:1;5522:14;5538:1;5541;5544;1495::23;5427:150:133;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5592:8;5588:700;;;5616:13;5632:7;;;;;;;;;-1:-1:-1;;;;;5632:7:133;-1:-1:-1;;;;;5632:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:32;;-1:-1:-1;;;5632:32:133;;247:1:19;5632:32:133;;;6497:25:195;-1:-1:-1;;;;;5632:21:133;;;;;;;6470:18:195;;5632:32:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5696:31;;-1:-1:-1;;;5696:31:133;;5721:4;5696:31;;;6958:51:195;5616:48:133;;-1:-1:-1;5678:15:133;;-1:-1:-1;;;;;5696:16:133;;;;;6931:18:195;;5696:31:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5678:49;;5741:6;-1:-1:-1;;;;;5741:15:133;;5757:7;;;;;;;;;-1:-1:-1;;;;;5757:7:133;-1:-1:-1;;;;;5757:19:133;;:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5741:47;;-1:-1:-1;;;;;;5741:47:133;;;;;;;-1:-1:-1;;;;;7447:32:195;;;5741:47:133;;;7429:51:195;7496:18;;;7489:34;;;7402:18;;5741:47:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;5802:7;;;;;;;;;-1:-1:-1;;;;;5802:7:133;-1:-1:-1;;;;;5802:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:34;;-1:-1:-1;;;5802:34:133;;5830:4;5802:34;;;6958:51:195;-1:-1:-1;;;;;5802:19:133;;;;;;;6931:18:195;;5802:34:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;5602:245;;5588:700;;;5867:13;5883:7;;;;;;;;;-1:-1:-1;;;;;5883:7:133;-1:-1:-1;;;;;5883:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:32;;-1:-1:-1;;;5883:32:133;;279:1:19;5883:32:133;;;6497:25:195;-1:-1:-1;;;;;5883:21:133;;;;;;;6470:18:195;;5883:32:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5867:48;;5929:13;5945:7;;;;;;;;;-1:-1:-1;;;;;5945:7:133;-1:-1:-1;;;;;5945:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:32;;-1:-1:-1;;;5945:32:133;;311:1:19;5945:32:133;;;6497:25:195;-1:-1:-1;;;;;5945:21:133;;;;;;;6470:18:195;;5945:32:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6009:31;;-1:-1:-1;;;6009:31:133;;6034:4;6009:31;;;6958:51:195;5929:48:133;;-1:-1:-1;5991:15:133;;-1:-1:-1;;;;;6009:16:133;;;;;6931:18:195;;6009:31:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6072;;-1:-1:-1;;;6072:31:133;;6097:4;6072:31;;;6958:51:195;5991:49:133;;-1:-1:-1;6054:15:133;;-1:-1:-1;;;;;6072:16:133;;;;;6931:18:195;;6072:31:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6054:49;;6117:6;-1:-1:-1;;;;;6117:15:133;;6133:7;;;;;;;;;-1:-1:-1;;;;;6133:7:133;-1:-1:-1;;;;;6133:19:133;;:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6117:47;;-1:-1:-1;;;;;;6117:47:133;;;;;;;-1:-1:-1;;;;;7447:32:195;;;6117:47:133;;;7429:51:195;7496:18;;;7489:34;;;7402:18;;6117:47:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;6178:6;-1:-1:-1;;;;;6178:15:133;;6194:7;;;;;;;;;-1:-1:-1;;;;;6194:7:133;-1:-1:-1;;;;;6194:19:133;;:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6178:47;;-1:-1:-1;;;;;;6178:47:133;;;;;;;-1:-1:-1;;;;;7447:32:195;;;6178:47:133;;;7429:51:195;7496:18;;;7489:34;;;7402:18;;6178:47:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;6239:7;;;;;;;;;-1:-1:-1;;;;;6239:7:133;-1:-1:-1;;;;;6239:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:38;;-1:-1:-1;;;6239:38:133;;6271:4;6239:38;;;6958:51:195;-1:-1:-1;;;;;6239:23:133;;;;;;;6931:18:195;;6239:38:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5853:435;;;;5588:700;6298:7;;6323:21;;;-1:-1:-1;;;6323:21:133;;;;-1:-1:-1;;;;;6298:7:133;;;;:24;;:7;;6323:19;;:21;;;;;;;;;;;;;;;6298:7;6323:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6346:20;:16;6365:1;6346:20;:::i;:::-;6368;:16;6387:1;6368:20;:::i;:::-;6298:91;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;6404:6;6400:141;;;6426:7;;;;;;;;;-1:-1:-1;;;;;6426:7:133;-1:-1:-1;;;;;6426:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6456:10;;6426:41;;-1:-1:-1;;;6426:41:133;;-1:-1:-1;;;;;6456:10:133;;;6426:41;;;6958:51:195;6426:29:133;;;;;6931:18:195;;6426:41:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;6400:141;;;6498:7;;;;;;;;;-1:-1:-1;;;;;6498:7:133;-1:-1:-1;;;;;6498:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6519:10;;6498:32;;-1:-1:-1;;;6498:32:133;;-1:-1:-1;;;;;6519:10:133;;;6498:32;;;6958:51:195;6498:20:133;;;;;6931:18:195;;6498:32:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;6400:141;6588:7;;;6626:10;6638:16;;;-1:-1:-1;;;6638:16:133;;;;-1:-1:-1;;;;;6588:7:133;;;;:24;;6626:10;;6588:7;;6638:14;;:16;;;;;;;;;;;;;;;6588:7;6638:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:41;;-1:-1:-1;;;6638:41:133;;6673:4;6638:41;;;6958:51:195;-1:-1:-1;;;;;6638:26:133;;;;;;;6931:18:195;;6638:41:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6681:7;;;;;;;;;-1:-1:-1;;;;;6681:7:133;-1:-1:-1;;;;;6681:14:133;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:41;;-1:-1:-1;;;6681:41:133;;6716:4;6681:41;;;6958:51:195;-1:-1:-1;;;;;6681:26:133;;;;;;;6931:18:195;;6681:41:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6588:144;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;5225:1514;;;5011:1728;;;;;;:::o;2981:2024::-;3232:16;;;3280:39;;;;3291:4;3280:39;:::i;:::-;3330:7;;3355:21;;;-1:-1:-1;;;3355:21:133;;;;3231:88;;-1:-1:-1;3231:88:133;;-1:-1:-1;3231:88:133;;-1:-1:-1;;;;;;3330:7:133;;:24;;:7;;3355:19;;:21;;;;;;;;;;;;;;;3330:7;3355:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3378:16;3396;3330:83;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;3423:7;;;;;;;;;-1:-1:-1;;;;;3423:7:133;-1:-1:-1;;;;;3423:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;3423:24:133;;3461:8;3479:4;3486:8;:16;;3501:1;3486:16;;;3497:1;3486:16;3504:1;3507;3510:6;:14;;3523:1;3510:14;;;3519:1;3510:14;3526:1;3529;3532;1495::23;3423:142:133;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3580:8;3576:700;;;3604:13;3620:7;;;;;;;;;-1:-1:-1;;;;;3620:7:133;-1:-1:-1;;;;;3620:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:32;;-1:-1:-1;;;3620:32:133;;247:1:19;3620:32:133;;;6497:25:195;-1:-1:-1;;;;;3620:21:133;;;;;;;6470:18:195;;3620:32:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3684:31;;-1:-1:-1;;;3684:31:133;;3709:4;3684:31;;;6958:51:195;3604:48:133;;-1:-1:-1;3666:15:133;;-1:-1:-1;;;;;3684:16:133;;;;;6931:18:195;;3684:31:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3666:49;;3729:6;-1:-1:-1;;;;;3729:15:133;;3745:7;;;;;;;;;-1:-1:-1;;;;;3745:7:133;-1:-1:-1;;;;;3745:19:133;;:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3729:47;;-1:-1:-1;;;;;;3729:47:133;;;;;;;-1:-1:-1;;;;;7447:32:195;;;3729:47:133;;;7429:51:195;7496:18;;;7489:34;;;7402:18;;3729:47:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;3790:7;;;;;;;;;-1:-1:-1;;;;;3790:7:133;-1:-1:-1;;;;;3790:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:34;;-1:-1:-1;;;3790:34:133;;3818:4;3790:34;;;6958:51:195;-1:-1:-1;;;;;3790:19:133;;;;;;;6931:18:195;;3790:34:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;3590:245;;3576:700;;;3855:13;3871:7;;;;;;;;;-1:-1:-1;;;;;3871:7:133;-1:-1:-1;;;;;3871:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:32;;-1:-1:-1;;;3871:32:133;;279:1:19;3871:32:133;;;6497:25:195;-1:-1:-1;;;;;3871:21:133;;;;;;;6470:18:195;;3871:32:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3855:48;;3917:13;3933:7;;;;;;;;;-1:-1:-1;;;;;3933:7:133;-1:-1:-1;;;;;3933:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:32;;-1:-1:-1;;;3933:32:133;;311:1:19;3933:32:133;;;6497:25:195;-1:-1:-1;;;;;3933:21:133;;;;;;;6470:18:195;;3933:32:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3997:31;;-1:-1:-1;;;3997:31:133;;4022:4;3997:31;;;6958:51:195;3917:48:133;;-1:-1:-1;3979:15:133;;-1:-1:-1;;;;;3997:16:133;;;;;6931:18:195;;3997:31:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4060;;-1:-1:-1;;;4060:31:133;;4085:4;4060:31;;;6958:51:195;3979:49:133;;-1:-1:-1;4042:15:133;;-1:-1:-1;;;;;4060:16:133;;;;;6931:18:195;;4060:31:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4042:49;;4105:6;-1:-1:-1;;;;;4105:15:133;;4121:7;;;;;;;;;-1:-1:-1;;;;;4121:7:133;-1:-1:-1;;;;;4121:19:133;;:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4105:47;;-1:-1:-1;;;;;;4105:47:133;;;;;;;-1:-1:-1;;;;;7447:32:195;;;4105:47:133;;;7429:51:195;7496:18;;;7489:34;;;7402:18;;4105:47:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;4166:6;-1:-1:-1;;;;;4166:15:133;;4182:7;;;;;;;;;-1:-1:-1;;;;;4182:7:133;-1:-1:-1;;;;;4182:19:133;;:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4166:47;;-1:-1:-1;;;;;;4166:47:133;;;;;;;-1:-1:-1;;;;;7447:32:195;;;4166:47:133;;;7429:51:195;7496:18;;;7489:34;;;7402:18;;4166:47:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;4227:7;;;;;;;;;-1:-1:-1;;;;;4227:7:133;-1:-1:-1;;;;;4227:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:38;;-1:-1:-1;;;4227:38:133;;4259:4;4227:38;;;6958:51:195;-1:-1:-1;;;;;4227:23:133;;;;;;;6931:18:195;;4227:38:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3841:435;;;;3576:700;4290:6;4286:521;;;4312:7;;4337:21;;;-1:-1:-1;;;4337:21:133;;;;-1:-1:-1;;;;;4312:7:133;;;;:24;;:7;;4337:19;;:21;;;;;;;;;;;;;;;4312:7;4337:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4360:20;:16;4379:1;4360:20;:::i;:::-;4382;:16;4401:1;4382:20;:::i;:::-;4312:91;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;4417:7;;;;;;;;;-1:-1:-1;;;;;4417:7:133;-1:-1:-1;;;;;4417:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4447:10;;4417:41;;-1:-1:-1;;;4417:41:133;;-1:-1:-1;;;;;4447:10:133;;;4417:41;;;6958:51:195;4417:29:133;;;;;6931:18:195;;4417:41:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;4286:521;;;4489:7;;4531:21;;;-1:-1:-1;;;4531:21:133;;;;-1:-1:-1;;;;;4489:7:133;;;;:24;;:7;;4531:19;;:21;;;;;;;;;;;;;;;4489:7;4531:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4587:7;;;;;;;;;-1:-1:-1;;;;;4587:7:133;-1:-1:-1;;;;;4587:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:31;;-1:-1:-1;;;4587:31:133;;373:1:19;4587:31:133;;;6497:25:195;-1:-1:-1;;;;;4587:21:133;;;;;;;6470:18:195;;4587:31:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4633:10;;4570:74;;-1:-1:-1;;;4570:74:133;;-1:-1:-1;;;;;4633:10:133;;;4570:74;;;6958:51:195;4570:62:133;;;;;6931:18:195;;4570:74:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4679:7;;;;;;;;;-1:-1:-1;;;;;4679:7:133;-1:-1:-1;;;;;4679:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:31;;-1:-1:-1;;;4679:31:133;;404:1:19;4679:31:133;;;6497:25:195;-1:-1:-1;;;;;4679:21:133;;;;;;;6470:18:195;;4679:31:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4725:10;;4662:74;;-1:-1:-1;;;4662:74:133;;-1:-1:-1;;;;;4725:10:133;;;4662:74;;;6958:51:195;4662:62:133;;;;;6931:18:195;;4662:74:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4489:261;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;4764:7;;;;;;;;;-1:-1:-1;;;;;4764:7:133;-1:-1:-1;;;;;4764:12:133;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4785:10;;4764:32;;-1:-1:-1;;;4764:32:133;;-1:-1:-1;;;;;4785:10:133;;;4764:32;;;6958:51:195;4764:20:133;;;;;6931:18:195;;4764:32:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;4286:521;4854:7;;;4892:10;4904:16;;;-1:-1:-1;;;4904:16:133;;;;-1:-1:-1;;;;;4854:7:133;;;;:24;;4892:10;;4854:7;;4904:14;;:16;;;;;;;;;;;;;;;4854:7;4904:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:41;;-1:-1:-1;;;4904:41:133;;4939:4;4904:41;;;6958:51:195;-1:-1:-1;;;;;4904:26:133;;;;;;;6931:18:195;;4904:41:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4947:7;;;;;;;;;-1:-1:-1;;;;;4947:7:133;-1:-1:-1;;;;;4947:14:133;;:16;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:41;;-1:-1:-1;;;4947:41:133;;4982:4;4947:41;;;6958:51:195;-1:-1:-1;;;;;4947:26:133;;;;;;;6931:18:195;;4947:41:133;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4854:144;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;3221:1784;;;2981:2024;;;;;;;:::o;14:131:195:-;-1:-1:-1;;;;;89:31:195;;79:42;;69:70;;135:1;132;125:12;69:70;14:131;:::o;150:347::-;201:8;211:6;265:3;258:4;250:6;246:17;242:27;232:55;;283:1;280;273:12;232:55;-1:-1:-1;306:20:195;;349:18;338:30;;335:50;;;381:1;378;371:12;335:50;418:4;410:6;406:17;394:29;;470:3;463:4;454:6;446;442:19;438:30;435:39;432:59;;;487:1;484;477:12;432:59;150:347;;;;;:::o;502:785::-;599:6;607;615;623;631;684:3;672:9;663:7;659:23;655:33;652:53;;;701:1;698;691:12;652:53;740:9;727:23;759:31;784:5;759:31;:::i;:::-;809:5;-1:-1:-1;887:2:195;872:18;;859:32;;-1:-1:-1;990:2:195;975:18;;962:32;;-1:-1:-1;1071:2:195;1056:18;;1043:32;1098:18;1087:30;;1084:50;;;1130:1;1127;1120:12;1084:50;1169:58;1219:7;1210:6;1199:9;1195:22;1169:58;:::i;:::-;502:785;;;;-1:-1:-1;502:785:195;;-1:-1:-1;1246:8:195;;1143:84;502:785;-1:-1:-1;;;502:785:195:o;1292:906::-;1398:6;1406;1414;1422;1430;1438;1491:3;1479:9;1470:7;1466:23;1462:33;1459:53;;;1508:1;1505;1498:12;1459:53;1547:9;1534:23;1566:31;1591:5;1566:31;:::i;:::-;1616:5;-1:-1:-1;1694:2:195;1679:18;;1666:32;;-1:-1:-1;1797:2:195;1782:18;;1769:32;;-1:-1:-1;1900:2:195;1885:18;;1872:32;;-1:-1:-1;1981:3:195;1966:19;;1953:33;2009:18;1998:30;;1995:50;;;2041:1;2038;2031:12;1995:50;2080:58;2130:7;2121:6;2110:9;2106:22;2080:58;:::i;:::-;1292:906;;;;-1:-1:-1;1292:906:195;;-1:-1:-1;1292:906:195;;2157:8;;1292:906;-1:-1:-1;;;1292:906:195:o;2203:346::-;2271:6;2279;2332:2;2320:9;2311:7;2307:23;2303:32;2300:52;;;2348:1;2345;2338:12;2300:52;-1:-1:-1;;2393:23:195;;;2513:2;2498:18;;;2485:32;;-1:-1:-1;2203:346:195:o;2554:1027::-;2669:6;2677;2685;2693;2701;2709;2717;2770:3;2758:9;2749:7;2745:23;2741:33;2738:53;;;2787:1;2784;2777:12;2738:53;2826:9;2813:23;2845:31;2870:5;2845:31;:::i;:::-;2895:5;-1:-1:-1;2973:2:195;2958:18;;2945:32;;-1:-1:-1;3076:2:195;3061:18;;3048:32;;-1:-1:-1;3179:2:195;3164:18;;3151:32;;-1:-1:-1;3282:3:195;3267:19;;3254:33;;-1:-1:-1;3364:3:195;3349:19;;3336:33;3392:18;3381:30;;3378:50;;;3424:1;3421;3414:12;3378:50;3463:58;3513:7;3504:6;3493:9;3489:22;3463:58;:::i;:::-;2554:1027;;;;-1:-1:-1;2554:1027:195;;-1:-1:-1;2554:1027:195;;;;3437:84;;-1:-1:-1;;;2554:1027:195:o;3586:118::-;3672:5;3665:13;3658:21;3651:5;3648:32;3638:60;;3694:1;3691;3684:12;3709:525;3788:6;3796;3804;3857:2;3845:9;3836:7;3832:23;3828:32;3825:52;;;3873:1;3870;3863:12;3825:52;3912:9;3899:23;3931:31;3956:5;3931:31;:::i;:::-;3981:5;-1:-1:-1;4038:2:195;4023:18;;4010:32;4051:30;4010:32;4051:30;:::i;:::-;4100:7;-1:-1:-1;4159:2:195;4144:18;;4131:32;4172:30;4131:32;4172:30;:::i;:::-;4221:7;4211:17;;;3709:525;;;;;:::o;4239:251::-;4309:6;4362:2;4350:9;4341:7;4337:23;4333:32;4330:52;;;4378:1;4375;4368:12;4330:52;4410:9;4404:16;4429:31;4454:5;4429:31;:::i;:::-;4479:5;4239:251;-1:-1:-1;;;4239:251:195:o;4495:345::-;-1:-1:-1;;;;;4715:32:195;;;;4697:51;;4779:2;4764:18;;4757:34;;;;4822:2;4807:18;;4800:34;4685:2;4670:18;;4495:345::o;5413:933::-;-1:-1:-1;;;;;5866:32:195;;;5848:51;;5935:32;;;;5930:2;5915:18;;5908:60;6016:4;6004:17;;;5999:2;5984:18;;5977:45;6053:2;6038:18;;6031:34;;;;6096:3;6081:19;;6074:35;;;;6146:17;;5886:3;6125:19;;6118:46;6195:3;6180:19;;6173:35;;;;6239:3;6224:19;;6217:35;;;;6283:3;6268:19;;6261:35;6327:3;6312:19;;6305:35;;;;5835:3;5820:19;;5413:933::o;7020:230::-;7090:6;7143:2;7131:9;7122:7;7118:23;7114:32;7111:52;;;7159:1;7156;7149:12;7111:52;-1:-1:-1;7204:16:195;;7020:230;-1:-1:-1;7020:230:195:o;7534:245::-;7601:6;7654:2;7642:9;7633:7;7629:23;7625:32;7622:52;;;7670:1;7667;7660:12;7622:52;7702:9;7696:16;7721:28;7743:5;7721:28;:::i;7784:343::-;7863:6;7871;7924:2;7912:9;7903:7;7899:23;7895:32;7892:52;;;7940:1;7937;7930:12;7892:52;-1:-1:-1;;7985:16:195;;8091:2;8076:18;;;8070:25;7985:16;;8070:25;;-1:-1:-1;7784:343:195:o;8132:222::-;8197:9;;;8218:10;;;8215:133;;;8270:10;8265:3;8261:20;8258:1;8251:31;8305:4;8302:1;8295:15;8333:4;8330:1;8323:15;8215:133;8132:222;;;;:::o;8359:456::-;8447:6;8455;8463;8516:2;8504:9;8495:7;8491:23;8487:32;8484:52;;;8532:1;8529;8522:12;8484:52;-1:-1:-1;;8577:16:195;;8683:2;8668:18;;8662:25;8779:2;8764:18;;;8758:25;8577:16;;8662:25;;-1:-1:-1;8758:25:195;8359:456;-1:-1:-1;8359:456:195:o", "linkReferences": {}}, "methodIdentifiers": {"ammalgamBorrowCallV1(address,uint256,uint256,uint256,uint256,bytes)": "f0a49494", "ammalgamBorrowLiquidityCallV1(address,uint256,uint256,uint256,bytes)": "4f2799a9", "ammalgamLiquidateCallV1(uint256,uint256)": "b9a2ff54", "ammalgamSwapCallV1(address,uint256,uint256,bytes)": "4e8eced1"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"contract FactoryPairTestFixture\",\"name\":\"_fixture\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_liquidator\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"borrowedLXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowedLYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"ammalgamBorrowCallV1\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"borrowedLXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowedLYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"ammalgamBorrowLiquidityCallV1\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"repayXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayYInYAssets\",\"type\":\"uint256\"}],\"name\":\"ammalgamLiquidateCallV1\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"ammalgamSwapCallV1\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"ammalgamLiquidateCallV1(uint256,uint256)\":{\"params\":{\"repayXInXAssets\":\"The amount of token X the liquidator should transfer to the pair.\",\"repayYInYAssets\":\"The amount of token Y the liquidator should transfer to the pair.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"ammalgamLiquidateCallV1(uint256,uint256)\":{\"notice\":\"Handles a liquidate call in the Ammalgam protocol. The callback is expected to transfer repayXInXAssets and repayYInYAssets from the liquidator to the pair.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/LiquidationTests.sol\":\"LeverageLiquidator\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/AmmalgamPair.sol\":{\"keccak256\":\"0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4\",\"urls\":[\"bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2\",\"dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS\"]},\"contracts/SaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76\",\"dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE\"]},\"contracts/factories/AmmalgamFactory.sol\":{\"keccak256\":\"0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b\",\"dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa\"]},\"contracts/factories/ERC20DebtLiquidityTokenFactory.sol\":{\"keccak256\":\"0x72e3ada6a2f0792a353b730c1b45ae832f9ce2f58f0bda039383f8890cb2a4f7\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4949e7b66647313aaba2e11d7edde06eb87345b476c1a20f890659c1af827b2b\",\"dweb:/ipfs/Qmf3emVXfGp1oc8iVYxnVqpJ88vnxxdj7WqPm1vzVKb1SD\"]},\"contracts/factories/ERC20LiquidityTokenFactory.sol\":{\"keccak256\":\"0x762974ca1ed600e0930a92bd2eb3a1a5f9ef0469ab2e6e811e4674e098238762\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5fd5f33537aeea9bac1f18c6fca2057899ec5f90cb8c756622eb436d5b13e27e\",\"dweb:/ipfs/QmfYznzzwN1AmdnuzNKe1R6t8UeztaZVGuzJ8vKfzjMXYN\"]},\"contracts/factories/ERC4626DebtTokenFactory.sol\":{\"keccak256\":\"0x7deeb7a40d26bc790112f29836da83050fa3554e471e1dce4dda6bf29ab9bf67\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5a46a4c8270e0b8a731259328b6c35c84de270a14f2f69ba04bc58d18400efc6\",\"dweb:/ipfs/QmQ56QbX6S9GjQinsFYtTMns6HgpcTXW1wnvQT6QgiuW1Z\"]},\"contracts/factories/ERC4626DepositTokenFactory.sol\":{\"keccak256\":\"0xf84b75119f2680f8079bb9567b0c03c0ad49b71a8c00f968d03d5fca2a954035\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c3fc7a9e300a935991746d5be835418b09e6d2b20b65e3e297d4faf28516469b\",\"dweb:/ipfs/QmQMr9MA5a3UcZCiP3e2haYqzBsbE8Pe6rDq6j6RJ3ub4Z\"]},\"contracts/factories/NewTokensFactory.sol\":{\"keccak256\":\"0x86cd420e1df8a59b11a4ab53a16971a44953f0a07741ef69d95baa4bd60126ac\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://d8cdd98060f059705b9ae2b64ab3e74395c0f3a24e12f5ac11ca7e509c6a7aa0\",\"dweb:/ipfs/QmahgKkRzuWHpQ73DHGZ4Kvd2MQG7MpfPShayJDRJQYSVr\"]},\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/ISaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20\",\"dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR\"]},\"contracts/interfaces/callbacks/IAmmalgamCallee.sol\":{\"keccak256\":\"0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d\",\"dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/factories/IAmmalgamFactory.sol\":{\"keccak256\":\"0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628\",\"dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9\"]},\"contracts/interfaces/factories/IFactoryCallback.sol\":{\"keccak256\":\"0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b\",\"dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT\"]},\"contracts/interfaces/factories/INewTokensFactory.sol\":{\"keccak256\":\"0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b\",\"dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E\"]},\"contracts/interfaces/factories/ITokenFactory.sol\":{\"keccak256\":\"0xac23e5c0441599add526b0c308faa7787f90bf01603b6dbc231944c166ca32d6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://ac574b98b2c1034786581137a218277ec58e06e9612f76814f34960383083626\",\"dweb:/ipfs/QmZgZqVnshjzuHBXJTR9g87S15CyLwJUSErGEDoJpBd4kg\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/IERC20DebtToken.sol\":{\"keccak256\":\"0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696\",\"dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b\"]},\"contracts/interfaces/tokens/IPluginRegistry.sol\":{\"keccak256\":\"0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d\",\"dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/GeometricTWAP.sol\":{\"keccak256\":\"0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa\",\"dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz\"]},\"contracts/libraries/Interest.sol\":{\"keccak256\":\"0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194\",\"dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX\"]},\"contracts/libraries/Liquidation.sol\":{\"keccak256\":\"0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877\",\"dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/Saturation.sol\":{\"keccak256\":\"0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20\",\"dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/TokenSymbol.sol\":{\"keccak256\":\"0x628df064fdbdacfe6783964d7bf38cdf1b34e1ad07caa3cea39bf7468cc19b43\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://da6823ce0debaabe20f25281e81a4fc88de98d4df2942a5e276826ac381c227b\",\"dweb:/ipfs/QmNpEuQ25788xfcJwPk2xUB7fyP7fW5ENK2e9qgRqp1BcH\"]},\"contracts/libraries/Uint16Set.sol\":{\"keccak256\":\"0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06\",\"dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/ERC20Base.sol\":{\"keccak256\":\"0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59\",\"dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL\"]},\"contracts/tokens/ERC20DebtBase.sol\":{\"keccak256\":\"0xc0a59cd54fcd847b160d662aa45a5fe7d24ed90c8030fe17fd5f9def427ed19a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://365c7f18505af36b2806404b1b3f2d897de6ac18e255ecfbb4ccc491cac7e444\",\"dweb:/ipfs/QmUqx8EBwRb6W1YQPb9MjwAhEEHNpZTCopbGWb1vbyuUpp\"]},\"contracts/tokens/ERC20DebtLiquidityToken.sol\":{\"keccak256\":\"0xf222ad5562ed41d74b0cfb5b4aad84ec9f4cb91b6d71928b30b018bab494efe8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a8e8f3e7ded2eae04c63ce3ae7a86c051d48d6db697cb6929d7064a4ec9d7371\",\"dweb:/ipfs/QmU3EuwHU3xB1e6MxaRjSRJcDMK73wfZig9uGWqZPaHnTn\"]},\"contracts/tokens/ERC20LiquidityToken.sol\":{\"keccak256\":\"0x2bb2429e551c031034c747749373d2e4c451580e9b203b689d6eaf03ad896358\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9ad5902756073578beee9068b74bd921e59a36b803cf34ef01570c670363689e\",\"dweb:/ipfs/QmTkT5K2XcB3ZbPDqd4ZAfnZMp2reCzu3Pv7JpRqhAtZHP\"]},\"contracts/tokens/ERC4626DebtToken.sol\":{\"keccak256\":\"0xe69b1ed2fb7b2d7c24c6838462001988b8e51795d215cfa74b9874d17257509e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c4f201e5f5621689046c58863ab9270cf770c68810d52269d1fc2ac93a7ccf96\",\"dweb:/ipfs/QmdtALf6LQdHhce3HsNdVtomQu8e5F5QcYU6S7H1PeBThZ\"]},\"contracts/tokens/ERC4626DepositToken.sol\":{\"keccak256\":\"0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac\",\"dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM\"]},\"contracts/tokens/PluginRegistry.sol\":{\"keccak256\":\"0x9263d71fc32da7d0ca4f8d272f8d75d565c1f06281952481322983bae9d7b488\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c9dcbc64172f4339547865b4f041826f0de5d464900f316edbe72e7d6bfb396d\",\"dweb:/ipfs/QmQykSWuY8xLJotWUPgG5JQDS5DmA2E5Hjb4c6Bz4YnbBQ\"]},\"contracts/tokens/TokenController.sol\":{\"keccak256\":\"0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159\",\"dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn\"]},\"contracts/utils/deployHelper.sol\":{\"keccak256\":\"0x9b9dd84e234bb2ffbf51da7e9ab42fe7b6329acf38de7f042d4f8abd146182f0\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://d07ded7de8e48a25ac7b0442c6e455338bd16ee483a89ad7f37585ab91865a3b\",\"dweb:/ipfs/QmeBAuZgRJEXeuX6qsGt46sTLovKNC5Pue8xFxbHMPtiBR\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol\":{\"keccak256\":\"0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22\",\"dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol\":{\"keccak256\":\"0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368\",\"dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB\"]},\"lib/1inch/token-plugins/contracts/ERC20Hooks.sol\":{\"keccak256\":\"0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5\",\"dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c\"]},\"lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol\":{\"keccak256\":\"0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8\",\"dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh\"]},\"lib/1inch/token-plugins/contracts/interfaces/IHook.sol\":{\"keccak256\":\"0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d\",\"dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS\"]},\"lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol\":{\"keccak256\":\"0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0\",\"dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z\"]},\"lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol\":{\"keccak256\":\"0x7d9d432e8f02168bf3f790e3dabcf36402782acf7ffa476cabe86fc4d8962eb2\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://1adc13e7f399f500ea5f81480ad149a50408fde7990a2c6347e6377486f389dc\",\"dweb:/ipfs/QmSvm5TUBJqknsqNJLLHqNS4MLSH5k3vNrbquVg6ZKSfx9\"]},\"lib/mangrove-core/lib/core/BitLib.sol\":{\"keccak256\":\"0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8\",\"dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr\"]},\"lib/morpho-blue/src/libraries/MathLib.sol\":{\"keccak256\":\"0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08\",\"dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd\"]},\"lib/openzeppelin-contracts/contracts/access/AccessControl.sol\":{\"keccak256\":\"0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d\",\"dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv\"]},\"lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e\",\"dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol\":{\"keccak256\":\"0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38\",\"dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol\":{\"keccak256\":\"0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4\",\"dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol\":{\"keccak256\":\"0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007\",\"dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol\":{\"keccak256\":\"0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819\",\"dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol\":{\"keccak256\":\"0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e\",\"dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215\",\"dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol\":{\"keccak256\":\"0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051\",\"dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol\":{\"keccak256\":\"0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78\",\"dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318\",\"dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79\",\"dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"keccak256\":\"0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26\",\"dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol\":{\"keccak256\":\"0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9\",\"dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol\":{\"keccak256\":\"0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834\",\"dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol\":{\"keccak256\":\"0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92\",\"dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol\":{\"keccak256\":\"0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896\",\"dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Nonces.sol\":{\"keccak256\":\"0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e\",\"dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/Pausable.sol\":{\"keccak256\":\"0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c\",\"dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8\"]},\"lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol\":{\"keccak256\":\"0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35\",\"dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211\",\"dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4\",\"dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol\":{\"keccak256\":\"0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f\",\"dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59\",\"dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4\",\"dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]},\"lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol\":{\"keccak256\":\"0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b\",\"dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr\"]},\"lib/openzeppelin-contracts/contracts/utils/types/Time.sol\":{\"keccak256\":\"0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6\",\"dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Base.sol\":{\"keccak256\":\"0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224\",\"dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1\",\"dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e\",\"dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c\",\"dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"test/InterestTests/InterestFixture.sol\":{\"keccak256\":\"0x458f1f72b1417a73ecdea81c25b269592e95c1808ca6aaa6b60a25243e143ed3\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://abeb9b791b75f44d48f898182c673d80ea1c0f513fffe48a6834fdebebc6fdbe\",\"dweb:/ipfs/QmU92joERfyZJaTonAknmtRBkTjs5Jb7S2zM8Zk1XAnhwj\"]},\"test/LiquidationTests.sol\":{\"keccak256\":\"0xe2d1babf7b8f8b60b61cf6bdc66dba3fc7d6669af080d97dd86213b98625d64f\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://279b2d86a18042355a1c651f5f9925fc8c9aed35ff21d6d741489c72295990a8\",\"dweb:/ipfs/QmR4pSdrKxEsFAvR74Vz9TufjgonZYTrkgtQvubnmnFJJe\"]},\"test/Saturation/SaturationTestUtils.sol\":{\"keccak256\":\"0xf337342f1d22905c1c09e15ebf1070c6eaad6182cac9a510a5720c3994547800\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5de929c0296b5d06e4c3c4ec19da66fb6169e8e4a4dabd197c5caabac080754b\",\"dweb:/ipfs/QmZQq1EfKo7mVF7DC3awppsgt41XF8rW6LgweTpfeAkLDE\"]},\"test/example/PeripheralDelegationContractExample.sol\":{\"keccak256\":\"0xf212fd0b2dd3a358c826623bd320e3aa0630892b9f6ba777b8126d3e2cfcfb14\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://2ad79b2d7eb1b46c69383b9e9090bf2031ff779e46637d850b881db3dc84d797\",\"dweb:/ipfs/QmTPx8qw1zdYXRzjBnmuzMCt8yiErwFiLBk47xnbTm1erP\"]},\"test/shared/FactoryPairTestFixture.sol\":{\"keccak256\":\"0x62487d7b3402461a61bd0c99be82302c8c1a94533c525a0ff6bcfe888af730a5\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://dfd509aaec3469ed23d1cda8c6f603b7f0163fc29ec4ba6e4b06b60ca8fdc042\",\"dweb:/ipfs/QmTPDPM7kt77VNWr61MVZGmNZp67RG8jKzdmz7zwWep4GE\"]},\"test/shared/StubErc20.sol\":{\"keccak256\":\"0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918\",\"dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn\"]},\"test/shared/utilities.sol\":{\"keccak256\":\"0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416\",\"dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG\"]},\"test/utils/DepletedAssetUtils.sol\":{\"keccak256\":\"0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e\",\"dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR\"]},\"test/utils/constants.sol\":{\"keccak256\":\"0xe7d13ea4f26a2c43b7beed68c83a0e36555ead8f6bfd181430c74f853546fc34\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5098f47b615afa3d6489c2c8c2576f6202601fb15b1f32e6900639986e44f1fd\",\"dweb:/ipfs/QmPU1Ejtv4yY7eqjW1SpVgvS8vMqwyEjMeNGCLax3Mwk9d\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "contract FactoryPairTestFixture", "name": "_fixture", "type": "address"}, {"internalType": "address", "name": "_liquidator", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "borrowedLXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "borrowedLYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "ammalgamBorrowCallV1"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "borrowedLXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "borrowedLYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "ammalgamBorrowLiquidityCallV1"}, {"inputs": [{"internalType": "uint256", "name": "repayXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayYInYAssets", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "ammalgamLiquidateCallV1"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "ammalgamSwapCallV1"}], "devdoc": {"kind": "dev", "methods": {"ammalgamLiquidateCallV1(uint256,uint256)": {"params": {"repayXInXAssets": "The amount of token X the liquidator should transfer to the pair.", "repayYInYAssets": "The amount of token Y the liquidator should transfer to the pair."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"ammalgamLiquidateCallV1(uint256,uint256)": {"notice": "Handles a liquidate call in the Ammalgam protocol. The callback is expected to transfer repayXInXAssets and repayYInYAssets from the liquidator to the pair."}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/LiquidationTests.sol": "LeverageLiquidator"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/AmmalgamPair.sol": {"keccak256": "0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4", "urls": ["bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2", "dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS"], "license": null}, "contracts/SaturationAndGeometricTWAPState.sol": {"keccak256": "0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419", "urls": ["bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76", "dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE"], "license": "GPL-3.0-only"}, "contracts/factories/AmmalgamFactory.sol": {"keccak256": "0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e", "urls": ["bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b", "dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa"], "license": "GPL-3.0-only"}, "contracts/factories/ERC20DebtLiquidityTokenFactory.sol": {"keccak256": "0x72e3ada6a2f0792a353b730c1b45ae832f9ce2f58f0bda039383f8890cb2a4f7", "urls": ["bzz-raw://4949e7b66647313aaba2e11d7edde06eb87345b476c1a20f890659c1af827b2b", "dweb:/ipfs/Qmf3emVXfGp1oc8iVYxnVqpJ88vnxxdj7WqPm1vzVKb1SD"], "license": "GPL-3.0-only"}, "contracts/factories/ERC20LiquidityTokenFactory.sol": {"keccak256": "0x762974ca1ed600e0930a92bd2eb3a1a5f9ef0469ab2e6e811e4674e098238762", "urls": ["bzz-raw://5fd5f33537aeea9bac1f18c6fca2057899ec5f90cb8c756622eb436d5b13e27e", "dweb:/ipfs/QmfYznzzwN1AmdnuzNKe1R6t8UeztaZVGuzJ8vKfzjMXYN"], "license": "GPL-3.0-only"}, "contracts/factories/ERC4626DebtTokenFactory.sol": {"keccak256": "0x7deeb7a40d26bc790112f29836da83050fa3554e471e1dce4dda6bf29ab9bf67", "urls": ["bzz-raw://5a46a4c8270e0b8a731259328b6c35c84de270a14f2f69ba04bc58d18400efc6", "dweb:/ipfs/QmQ56QbX6S9GjQinsFYtTMns6HgpcTXW1wnvQT6QgiuW1Z"], "license": "GPL-3.0-only"}, "contracts/factories/ERC4626DepositTokenFactory.sol": {"keccak256": "0xf84b75119f2680f8079bb9567b0c03c0ad49b71a8c00f968d03d5fca2a954035", "urls": ["bzz-raw://c3fc7a9e300a935991746d5be835418b09e6d2b20b65e3e297d4faf28516469b", "dweb:/ipfs/QmQMr9MA5a3UcZCiP3e2haYqzBsbE8Pe6rDq6j6RJ3ub4Z"], "license": "GPL-3.0-only"}, "contracts/factories/NewTokensFactory.sol": {"keccak256": "0x86cd420e1df8a59b11a4ab53a16971a44953f0a07741ef69d95baa4bd60126ac", "urls": ["bzz-raw://d8cdd98060f059705b9ae2b64ab3e74395c0f3a24e12f5ac11ca7e509c6a7aa0", "dweb:/ipfs/QmahgKkRzuWHpQ73DHGZ4Kvd2MQG7MpfPShayJDRJQYSVr"], "license": "GPL-3.0-only"}, "contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/ISaturationAndGeometricTWAPState.sol": {"keccak256": "0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c", "urls": ["bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20", "dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/IAmmalgamCallee.sol": {"keccak256": "0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339", "urls": ["bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d", "dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IAmmalgamFactory.sol": {"keccak256": "0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8", "urls": ["bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628", "dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IFactoryCallback.sol": {"keccak256": "0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52", "urls": ["bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b", "dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/INewTokensFactory.sol": {"keccak256": "0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9", "urls": ["bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b", "dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/ITokenFactory.sol": {"keccak256": "0xac23e5c0441599add526b0c308faa7787f90bf01603b6dbc231944c166ca32d6", "urls": ["bzz-raw://ac574b98b2c1034786581137a218277ec58e06e9612f76814f34960383083626", "dweb:/ipfs/QmZgZqVnshjzuHBXJTR9g87S15CyLwJUSErGEDoJpBd4kg"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IERC20DebtToken.sol": {"keccak256": "0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a", "urls": ["bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696", "dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IPluginRegistry.sol": {"keccak256": "0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf", "urls": ["bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d", "dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X"], "license": "MIT"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/GeometricTWAP.sol": {"keccak256": "0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e", "urls": ["bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa", "dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz"], "license": "GPL-3.0-only"}, "contracts/libraries/Interest.sol": {"keccak256": "0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646", "urls": ["bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194", "dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX"], "license": "GPL-3.0-only"}, "contracts/libraries/Liquidation.sol": {"keccak256": "0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63", "urls": ["bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877", "dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/Saturation.sol": {"keccak256": "0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860", "urls": ["bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20", "dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/TokenSymbol.sol": {"keccak256": "0x628df064fdbdacfe6783964d7bf38cdf1b34e1ad07caa3cea39bf7468cc19b43", "urls": ["bzz-raw://da6823ce0debaabe20f25281e81a4fc88de98d4df2942a5e276826ac381c227b", "dweb:/ipfs/QmNpEuQ25788xfcJwPk2xUB7fyP7fW5ENK2e9qgRqp1BcH"], "license": "GPL-3.0-only"}, "contracts/libraries/Uint16Set.sol": {"keccak256": "0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f", "urls": ["bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06", "dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy"], "license": "GPL-3.0-only"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/ERC20Base.sol": {"keccak256": "0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b", "urls": ["bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59", "dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL"], "license": "MIT"}, "contracts/tokens/ERC20DebtBase.sol": {"keccak256": "0xc0a59cd54fcd847b160d662aa45a5fe7d24ed90c8030fe17fd5f9def427ed19a", "urls": ["bzz-raw://365c7f18505af36b2806404b1b3f2d897de6ac18e255ecfbb4ccc491cac7e444", "dweb:/ipfs/QmUqx8EBwRb6W1YQPb9MjwAhEEHNpZTCopbGWb1vbyuUpp"], "license": "MIT"}, "contracts/tokens/ERC20DebtLiquidityToken.sol": {"keccak256": "0xf222ad5562ed41d74b0cfb5b4aad84ec9f4cb91b6d71928b30b018bab494efe8", "urls": ["bzz-raw://a8e8f3e7ded2eae04c63ce3ae7a86c051d48d6db697cb6929d7064a4ec9d7371", "dweb:/ipfs/QmU3EuwHU3xB1e6MxaRjSRJcDMK73wfZig9uGWqZPaHnTn"], "license": "MIT"}, "contracts/tokens/ERC20LiquidityToken.sol": {"keccak256": "0x2bb2429e551c031034c747749373d2e4c451580e9b203b689d6eaf03ad896358", "urls": ["bzz-raw://9ad5902756073578beee9068b74bd921e59a36b803cf34ef01570c670363689e", "dweb:/ipfs/QmTkT5K2XcB3ZbPDqd4ZAfnZMp2reCzu3Pv7JpRqhAtZHP"], "license": "MIT"}, "contracts/tokens/ERC4626DebtToken.sol": {"keccak256": "0xe69b1ed2fb7b2d7c24c6838462001988b8e51795d215cfa74b9874d17257509e", "urls": ["bzz-raw://c4f201e5f5621689046c58863ab9270cf770c68810d52269d1fc2ac93a7ccf96", "dweb:/ipfs/QmdtALf6LQdHhce3HsNdVtomQu8e5F5QcYU6S7H1PeBThZ"], "license": "MIT"}, "contracts/tokens/ERC4626DepositToken.sol": {"keccak256": "0xd914aa43dc5e9f2f02f98b05561faf6f00853b701f51dfcd7a08a31feaf220be", "urls": ["bzz-raw://8c2282d40855587b2ac70e89d4e0e147b9afe32a41245fffc96b923a9e5ce7ac", "dweb:/ipfs/QmVn3tBkZcXKnqjfnLTXFkKtu1EetDL1UF7rRjNrHdRCSM"], "license": "MIT"}, "contracts/tokens/PluginRegistry.sol": {"keccak256": "0x9263d71fc32da7d0ca4f8d272f8d75d565c1f06281952481322983bae9d7b488", "urls": ["bzz-raw://c9dcbc64172f4339547865b4f041826f0de5d464900f316edbe72e7d6bfb396d", "dweb:/ipfs/QmQykSWuY8xLJotWUPgG5JQDS5DmA2E5Hjb4c6Bz4YnbBQ"], "license": "MIT"}, "contracts/tokens/TokenController.sol": {"keccak256": "0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6", "urls": ["bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159", "dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn"], "license": "GPL-3.0-only"}, "contracts/utils/deployHelper.sol": {"keccak256": "0x9b9dd84e234bb2ffbf51da7e9ab42fe7b6329acf38de7f042d4f8abd146182f0", "urls": ["bzz-raw://d07ded7de8e48a25ac7b0442c6e455338bd16ee483a89ad7f37585ab91865a3b", "dweb:/ipfs/QmeBAuZgRJEXeuX6qsGt46sTLovKNC5Pue8xFxbHMPtiBR"], "license": "GPL-3.0-only"}, "lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol": {"keccak256": "0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e", "urls": ["bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22", "dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9"], "license": "MIT"}, "lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol": {"keccak256": "0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318", "urls": ["bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368", "dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/ERC20Hooks.sol": {"keccak256": "0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2", "urls": ["bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5", "dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol": {"keccak256": "0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875", "urls": ["bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8", "dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IHook.sol": {"keccak256": "0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80", "urls": ["bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d", "dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol": {"keccak256": "0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c", "urls": ["bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0", "dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z"], "license": "MIT"}, "lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol": {"keccak256": "0x7d9d432e8f02168bf3f790e3dabcf36402782acf7ffa476cabe86fc4d8962eb2", "urls": ["bzz-raw://1adc13e7f399f500ea5f81480ad149a50408fde7990a2c6347e6377486f389dc", "dweb:/ipfs/QmSvm5TUBJqknsqNJLLHqNS4MLSH5k3vNrbquVg6ZKSfx9"], "license": "MIT OR Apache-2.0"}, "lib/mangrove-core/lib/core/BitLib.sol": {"keccak256": "0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3", "urls": ["bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8", "dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr"], "license": "MIT"}, "lib/morpho-blue/src/libraries/MathLib.sol": {"keccak256": "0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a", "urls": ["bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08", "dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd"], "license": "GPL-2.0-or-later"}, "lib/openzeppelin-contracts/contracts/access/AccessControl.sol": {"keccak256": "0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2", "urls": ["bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d", "dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1", "urls": ["bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e", "dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol": {"keccak256": "0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548", "urls": ["bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38", "dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol": {"keccak256": "0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47", "urls": ["bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4", "dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol": {"keccak256": "0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d", "urls": ["bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007", "dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol": {"keccak256": "0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73", "urls": ["bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819", "dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol": {"keccak256": "0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541", "urls": ["bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e", "dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol": {"keccak256": "0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad", "urls": ["bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215", "dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol": {"keccak256": "0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe", "urls": ["bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051", "dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol": {"keccak256": "0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8", "urls": ["bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78", "dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9", "urls": ["bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318", "dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db", "urls": ["bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79", "dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"keccak256": "0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073", "urls": ["bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26", "dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol": {"keccak256": "0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74", "urls": ["bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9", "dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol": {"keccak256": "0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8", "urls": ["bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834", "dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol": {"keccak256": "0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5", "urls": ["bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92", "dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol": {"keccak256": "0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63", "urls": ["bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896", "dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Nonces.sol": {"keccak256": "0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f", "urls": ["bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e", "dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Pausable.sol": {"keccak256": "0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f", "urls": ["bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c", "dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol": {"keccak256": "0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402", "urls": ["bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35", "dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52", "urls": ["bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211", "dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5", "urls": ["bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4", "dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol": {"keccak256": "0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e", "urls": ["bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f", "dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631", "urls": ["bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59", "dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol": {"keccak256": "0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f", "urls": ["bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4", "dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol": {"keccak256": "0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a", "urls": ["bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b", "dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/types/Time.sol": {"keccak256": "0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc", "urls": ["bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6", "dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Base.sol": {"keccak256": "0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c", "urls": ["bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224", "dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol": {"keccak256": "0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b", "urls": ["bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1", "dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Test.sol": {"keccak256": "0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe", "urls": ["bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e", "dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol": {"keccak256": "0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354", "urls": ["bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c", "dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC"], "license": "MIT OR Apache-2.0"}, "lib/openzeppelin-contracts/lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "test/InterestTests/InterestFixture.sol": {"keccak256": "0x458f1f72b1417a73ecdea81c25b269592e95c1808ca6aaa6b60a25243e143ed3", "urls": ["bzz-raw://abeb9b791b75f44d48f898182c673d80ea1c0f513fffe48a6834fdebebc6fdbe", "dweb:/ipfs/QmU92joERfyZJaTonAknmtRBkTjs5Jb7S2zM8Zk1XAnhwj"], "license": "GPL-3.0-only"}, "test/LiquidationTests.sol": {"keccak256": "0xe2d1babf7b8f8b60b61cf6bdc66dba3fc7d6669af080d97dd86213b98625d64f", "urls": ["bzz-raw://279b2d86a18042355a1c651f5f9925fc8c9aed35ff21d6d741489c72295990a8", "dweb:/ipfs/QmR4pSdrKxEsFAvR74Vz9TufjgonZYTrkgtQvubnmnFJJe"], "license": "GPL-3.0-only"}, "test/Saturation/SaturationTestUtils.sol": {"keccak256": "0xf337342f1d22905c1c09e15ebf1070c6eaad6182cac9a510a5720c3994547800", "urls": ["bzz-raw://5de929c0296b5d06e4c3c4ec19da66fb6169e8e4a4dabd197c5caabac080754b", "dweb:/ipfs/QmZQq1EfKo7mVF7DC3awppsgt41XF8rW6LgweTpfeAkLDE"], "license": "GPL-3.0-only"}, "test/example/PeripheralDelegationContractExample.sol": {"keccak256": "0xf212fd0b2dd3a358c826623bd320e3aa0630892b9f6ba777b8126d3e2cfcfb14", "urls": ["bzz-raw://2ad79b2d7eb1b46c69383b9e9090bf2031ff779e46637d850b881db3dc84d797", "dweb:/ipfs/QmTPx8qw1zdYXRzjBnmuzMCt8yiErwFiLBk47xnbTm1erP"], "license": "GPL-3.0-only"}, "test/shared/FactoryPairTestFixture.sol": {"keccak256": "0x62487d7b3402461a61bd0c99be82302c8c1a94533c525a0ff6bcfe888af730a5", "urls": ["bzz-raw://dfd509aaec3469ed23d1cda8c6f603b7f0163fc29ec4ba6e4b06b60ca8fdc042", "dweb:/ipfs/QmTPDPM7kt77VNWr61MVZGmNZp67RG8jKzdmz7zwWep4GE"], "license": "GPL-3.0-only"}, "test/shared/StubErc20.sol": {"keccak256": "0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee", "urls": ["bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918", "dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn"], "license": "MIT"}, "test/shared/utilities.sol": {"keccak256": "0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8", "urls": ["bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416", "dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG"], "license": "GPL-3.0-only"}, "test/utils/DepletedAssetUtils.sol": {"keccak256": "0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42", "urls": ["bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e", "dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR"], "license": "GPL-3.0-only"}, "test/utils/constants.sol": {"keccak256": "0xe7d13ea4f26a2c43b7beed68c83a0e36555ead8f6bfd181430c74f853546fc34", "urls": ["bzz-raw://5098f47b615afa3d6489c2c8c2576f6202601fb15b1f32e6900639986e44f1fd", "dweb:/ipfs/QmPU1Ejtv4yY7eqjW1SpVgvS8vMqwyEjMeNGCLax3Mwk9d"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 133}