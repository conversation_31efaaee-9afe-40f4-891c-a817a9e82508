{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "externalLiquidity", "inputs": [], "outputs": [{"name": "", "type": "uint112", "internalType": "uint112"}], "stateMutability": "view"}, {"type": "function", "name": "getReserves", "inputs": [], "outputs": [{"name": "_reserveXAssets", "type": "uint112", "internalType": "uint112"}, {"name": "_reserveYAssets", "type": "uint112", "internalType": "uint112"}, {"name": "_lastTimestamp", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "getTickRange", "inputs": [], "outputs": [{"name": "minTick", "type": "int16", "internalType": "int16"}, {"name": "maxTick", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "referenceReserves", "inputs": [], "outputs": [{"name": "", "type": "uint112", "internalType": "uint112"}, {"name": "", "type": "uint112", "internalType": "uint112"}], "stateMutability": "view"}, {"type": "function", "name": "tokens", "inputs": [{"name": "tokenType", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "contract IAmmalgamERC20"}], "stateMutability": "view"}, {"type": "function", "name": "totalAssets", "inputs": [], "outputs": [{"name": "", "type": "uint128[6]", "internalType": "uint128[6]"}], "stateMutability": "view"}, {"type": "function", "name": "underlyingTokens", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IERC20"}, {"name": "", "type": "address", "internalType": "contract IERC20"}], "stateMutability": "view"}, {"type": "function", "name": "updateExternalLiquidity", "inputs": [{"name": "_externalLiquidity", "type": "uint112", "internalType": "uint112"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "BurnBadDebt", "inputs": [{"name": "borrower", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenType", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "badDebtAssets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "badDebtShares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "InterestAccrued", "inputs": [{"name": "depositLAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "depositXAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "depositYAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowLAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowXAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "borrowYAssets", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "Sync", "inputs": [{"name": "reserveXAssets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "reserveYAssets", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "UpdateExternalLiquidity", "inputs": [{"name": "externalLiquidity", "type": "uint112", "indexed": false, "internalType": "uint112"}], "anonymous": false}, {"type": "error", "name": "Forbidden", "inputs": []}, {"type": "error", "name": "PriceOutOfBounds", "inputs": []}, {"type": "error", "name": "TickOutOfBounds", "inputs": []}], "bytecode": {"object": "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__$0148cd7411c566e8e3abb1476dee2c2502$__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", "sourceMap": "1333:22369:38:-:0;;;2596:45;;;-1:-1:-1;;;;;;2596:45:38;;;2812:587;;;;;;;;;;2836:34;;:::i;:::-;2907:10;2880:38;;;;2958:37;;;-1:-1:-1;;;2958:37:38;;;;:35;;:37;;;;;;;;;;;;;;;;2907:10;2958:37;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;2928:67:38;;;;;;;;;;;;;;3023:20;;3006:37;;;;-1:-1:-1;3070:20:38;;;;3053:37;;;;3117:20;;;;;3100:37;;;;3163:19;;;;3147:35;;;;3208:19;;;;3192:35;;;;3253:19;;;;3237:35;;;;3350:7;;:41;;-1:-1:-1;;;3350:41:38;;;;2928:67;;-1:-1:-1;3350:39:38;;;;;;373:1:19;3350:41:38;;;;;;;;;;:39;:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;3283:109:38;;;-1:-1:-1;1333:22369:38;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1333:22369:38;;;-1:-1:-1;;1333:22369:38:o;14:139:195:-;-1:-1:-1;;;;;97:31:195;;87:42;;77:70;;143:1;140;133:12;77:70;14:139;:::o;158:127::-;219:10;214:3;210:20;207:1;200:31;250:4;247:1;240:15;274:4;271:1;264:15;290:162;385:13;;407:39;385:13;407:39;:::i;:::-;290:162;;;:::o;457:1132::-;623:6;631;639;692:3;680:9;671:7;667:23;663:33;660:53;;;709:1;706;699:12;660:53;741:9;735:16;760:39;793:5;760:39;:::i;:::-;868:2;853:18;;847:25;818:5;;-1:-1:-1;881:41:195;847:25;881:41;:::i;:::-;941:7;-1:-1:-1;986:2:195;971:18;;967:32;-1:-1:-1;957:60:195;;1013:1;1010;1003:12;957:60;1046:2;1040:9;1088:3;1076:16;;-1:-1:-1;;;;;1107:34:195;;1143:22;;;1104:62;1101:88;;;1169:18;;:::i;:::-;1205:2;1198:22;1240:6;1284:3;1269:19;;1300;;;1297:39;;;1332:1;1329;1322:12;1297:39;1371:2;1360:9;1356:18;1383:175;1399:6;1394:3;1391:15;1383:175;;;1465:50;1511:3;1465:50;:::i;:::-;1453:63;;1545:2;1536:12;;;;1416;1383:175;;;1387:3;;;1577:6;1567:16;;;457:1132;;;;;:::o;1726:300::-;1837:6;1890:2;1878:9;1869:7;1865:23;1861:32;1858:52;;;1906:1;1903;1896:12;1858:52;1938:9;1932:16;1957:39;1990:5;1957:39;:::i;:::-;2015:5;1726:300;-1:-1:-1;;;1726:300:195:o;:::-;1333:22369:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {"contracts/libraries/Interest.sol": {"Interest": [{"start": 2043, "length": 20}]}}}, "deployedBytecode": {"object": "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__$0148cd7411c566e8e3abb1476dee2c2502$__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", "sourceMap": "1333:22369:38:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8309:2094;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;6717:282;;;:::i;:::-;;;;-1:-1:-1;;;;;750:43:195;;;732:62;;830:43;;;;825:2;810:18;;803:71;922:10;910:23;890:18;;;883:51;720:2;705:18;6717:282:38;532:408:195;7438:130:38;7524:17;;7438:130;;;-1:-1:-1;;;;;7524:17:38;;;1119:62:195;;-1:-1:-1;;;7543:17:38;;;;;;1212:2:195;1197:18;;1190:71;1092:18;7438:130:38;945:322:195;7005:427:38;;;:::i;:::-;;;;1467:1:195;1456:21;;;1438:40;;1514:21;;;;1509:2;1494:18;;1487:49;1411:18;7005:427:38;1272:270:195;5867:314:38;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;1919:32:195;;;1901:51;;1889:2;1874:18;5867:314:38;1732:226:195;4516:213:38;;;;;;:::i;:::-;;:::i;:::-;;2596:45;;;;;-1:-1:-1;;;;;2596:45:38;;;;;;-1:-1:-1;;;;;2429:43:195;;;2411:62;;2399:2;2384:18;2596:45:38;2265:214:195;3631:114:38;;;;-1:-1:-1;;;;;3723:6:38;2708:32:195;;2690:51;;3731:6:38;2777:32:195;2772:2;2757:18;;2750:60;2663:18;3631:114:38;2484:332:195;8309:2094:38;8353:17;;:::i;:::-;8386:19;;:24;8382:349;;-1:-1:-1;8426:294:38;;;;;;;;-1:-1:-1;;;;;;8459:19:38;8426:294;;;;8505:19;;8426:294;;;;;;8551:19;;8426:294;;;;;;;;;8597:18;;8426:294;;;;;;8642:18;;8426:294;;;;;;8687:18;;8426:294;;;;;;;;8309:2094::o;8382:349::-;8957:19;;9033:20;;26839:21:21;-1:-1:-1;;;8957:19:38;;;;;;26839:15:21;:21;;8938:38:38;;;;9033:20;;;;;9014:39;;;;9133:26;;8741:27;9133:26;9129:73;;9175:16;;;;;;;;;;;9182:9;;9175:16;;9182:9;-1:-1:-1;9175:16:38;;;;;;;;;;;-1:-1:-1;;;;;9175:16:38;-1:-1:-1;;;;;9175:16:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8309:2094;:::o;9129:73::-;9213:23;9238;9266:13;:11;:13::i;:::-;9212:67;-1:-1:-1;;;;;9212:67:38;;;-1:-1:-1;;;;;9212:67:38;;;9289:13;9305:31;-1:-1:-1;;;;;9305:41:38;;9360:86;9384:61;9399:15;-1:-1:-1;;;9422:15:38;9439:5;9384:14;:61::i;:::-;9360:23;:86::i;:::-;9305:151;;-1:-1:-1;;;;;;9305:151:38;;;;;;;2992:1:195;2981:21;;;;9305:151:38;;;2963:40:195;2936:18;;9305:151:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9692:135;;-1:-1:-1;;;9692:135:38;;3615:1:195;3604:21;;;9692:135:38;;;3586:40:195;9692:135:38;3662:23:195;;;3642:18;;;3635:51;3722:23;;3702:18;;;3695:51;9289:167:38;;-1:-1:-1;9467:22:38;;;;-1:-1:-1;;;;;9692:31:38;:51;;;;3559:18:195;;9692:135:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;9650:177;;;;;;;;9848:43;9894:318;;;;;;;;9948:21;9894:318;;;;;;10001:16;9894:318;;;;;;10056:60;10083:15;10100;10056:26;:60::i;:::-;9894:318;;;;;;;;;;;;;;;;;;-1:-1:-1;;9894:318:38;;-1:-1:-1;;9894:318:38;;;;;;;;;;;-1:-1:-1;;;;;9894:318:38;-1:-1:-1;;;;;9894:318:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;9894:318:38;;;-1:-1:-1;;;9894:318:38;;;;;10313:52;;-1:-1:-1;;;10313:52:38;;9848:364;;-1:-1:-1;;;10313:8:38;;:33;;:52;;10347:9;;9848:364;;10313:52;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;10274:91:38;;8309:2094;-1:-1:-1;;;;;;;;;;;;8309:2094:38:o;6717:282::-;6890:14;;-1:-1:-1;;;;;6890:14:38;;;;-1:-1:-1;;;6932:14:38;;;;;;-1:-1:-1;;;6973:19:38;;;;;6717:282::o;7005:427::-;7050:13;7065;7091:23;7116;7144:13;:11;:13::i;:::-;7090:67;;;;;7167:19;7189:61;7204:15;-1:-1:-1;;;;;7189:61:38;-1:-1:-1;;;7227:15:38;-1:-1:-1;;;;;7189:61:38;7244:5;7189:14;:61::i;:::-;7167:83;;7260:17;7280:36;7304:11;7280:23;:36::i;:::-;7347:78;;-1:-1:-1;;;7347:78:38;;7400:4;7347:78;;;7855:51:195;7420:4:38;7942:21:195;;;7922:18;;;7915:49;7980:18;;;7973:50;7260:56:38;;-1:-1:-1;7347:31:38;-1:-1:-1;;;;;7347:44:38;;;;7828:18:195;;7347:78:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;7326:99;;;;-1:-1:-1;7005:427:38;-1:-1:-1;;;;;7005:427:38:o;5867:314::-;6070:104;;;;;;;;-1:-1:-1;;;;;6071:14:38;6070:104;;;;6087:14;6070:104;;;;;;6103:14;6070:104;;;;;;;;;6119:13;6070:104;;;;;;6134:13;6070:104;;;;;;6149:13;6070:104;;;;;;;5946:14;;6164:9;6070:104;;;;;;;:::i;:::-;;;;;;5867:314;-1:-1:-1;;5867:314:38:o;4516:213::-;3442:18;:16;:18::i;:::-;4631:43:::1;::::0;-1:-1:-1;;;;;2429:43:195;;2411:62;;4631:43:38::1;::::0;2399:2:195;2384:18;4631:43:38::1;;;;;;;4684:17;:38:::0;;-1:-1:-1;;4684:38:38::1;-1:-1:-1::0;;;;;4684:38:38;;;::::1;::::0;;;::::1;::::0;;4516:213::o;1908:204:20:-;1997:14;2032:5;2036:1;2032;:5;:::i;:::-;2023:14;;2056:10;:49;;2095:10;2104:1;2095:6;:10;:::i;:::-;2056:49;;;2069:23;2082:6;2090:1;2069:12;:23::i;:::-;2047:58;1908:204;-1:-1:-1;;;;;1908:204:20:o;1966:3501:26:-;2048:5;821:7;2069:11;:31;:66;;;;2124:11;-1:-1:-1;;;2104:31:26;2069:66;2065:97;;;2144:18;;-1:-1:-1;;;2144:18:26;;;;;;;;;;;2065:97;-1:-1:-1;;;;;2271:41:26;;2268:1;2264:49;2361:9;;;2434:18;2428:25;;2425:1;2421:33;2502:9;;;2575:10;2569:17;;2566:1;2562:25;2635:9;;;2708:6;2702:13;;2699:1;2695:21;2764:9;;;2837:4;2831:11;;2828:1;2824:19;;;2891:9;;;2964:3;2958:10;;2955:1;2951:18;3017:9;;;3084:10;;;3081:1;3077:18;;;3143:9;;;;3203:10;;;2474;;2607;;;2736;;;2863;2989;;;3115;3233;2173:9;3323:3;3316:10;;3312:95;;3354:3;3348;:9;3332:11;:26;;3328:30;;3312:95;;;3403:3;3397;:9;3381:11;:26;;3377:30;;3312:95;-1:-1:-1;3515:9:26;;;3510:3;3506:19;;;3547:11;;;;3625:9;;;;3690;;3681:19;;;3722:11;;;3800:9;3865;;3856:19;;;3897:11;;;3975:9;4040;;4031:19;;;4072:11;;;4150:9;4215;;4206:19;;;4247:11;;;4325:9;4390;;4381:19;;;4422:11;;;4500:9;4565;;4556:19;;;4597:11;;;4675:9;4740;;4731:19;;;4772:11;;;4850:9;4915;;4906:19;;;;4947:11;;;;5025:9;;;;;3515;-1:-1:-1;;3433:17:26;;3455:2;3432:25;3596:10;;;;;;;3583:24;3771:10;;;;;;;3758:24;;;;3946:10;;;;;;;3933:24;;;;4121:10;;;;;;;4108:24;;;;4296:10;;;;;;;4283:24;4471:10;;;;;;;4458:24;4646:10;;;;;;;4633:24;4821:10;;;;;;;4808:24;4996:10;;;;;;;4983:24;550:20;5092:39;;-1:-1:-1;;5168:40:26;;3447:3;5167:49;;;;734:34;5253:39;;5252:48;;5319:17;;;;;;;;;5315:37;;-1:-1:-1;5345:7:26;1966:3501;-1:-1:-1;;;;;;1966:3501:26:o;5315:37::-;5396:11;5370:22;5385:6;5370:14;:22::i;:::-;:37;5366:56;;5416:6;1966:3501;-1:-1:-1;;;;;;;1966:3501:26:o;5366:56::-;-1:-1:-1;5443:7:26;1966:3501;-1:-1:-1;;;;;;1966:3501:26:o;21409:281:38:-;21540:31;21613:70;21650:15;21667;21613:36;:70::i;:::-;21583:100;21409:281;-1:-1:-1;;;;21409:281:38:o;3484:141::-;3553:7;-1:-1:-1;;;;;3553:19:38;;:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;3539:35:38;:10;-1:-1:-1;;;;;3539:35:38;;3535:84;;3597:11;;-1:-1:-1;;;3597:11:38;;;;;;;;;;;3535:84;3484:141::o;6215:704:89:-;6277:7;6300:1;6305;6300:6;6296:150;;6400:35;1035:4:79;6400:11:89;:35::i;:::-;6896:1;6891;6887;:5;6886:11;;;;;:::i;:::-;;6900:1;6886:15;6876:5;;;6860:42;6853:49;;6215:704;;;;;:::o;5473:602:26:-;5546:19;-1:-1:-1;;5581:15:26;;;;;;:34;;-1:-1:-1;5600:15:26;;;;1234:6;5600:15;5581:34;5577:64;;;5624:17;;-1:-1:-1;;;5624:17:26;;;;;;;;;;;5577:64;5651:21;;;;:14;5708:11;;;:32;;5733:7;5708:32;;;5722:8;5723:7;5722:8;:::i;:::-;5682:59;-1:-1:-1;5848:12:26;5859:1;5682:59;5848:12;:::i;:::-;;;5884:29;5905:7;5884:20;:29::i;:::-;5870:43;-1:-1:-1;5937:6:26;5927:16;;:21;5923:75;;5995:3;5965:25;:11;5979;5965:25;:::i;:::-;5964:34;;5950:48;;5923:75;6017:4;6013:8;;:1;:8;6009:59;;;6037:31;6057:11;-1:-1:-1;;6037:31:26;:::i;:::-;6023:45;;6009:59;5567:508;;5473:602;;;:::o;21696:453:38:-;21837:31;21870;21939:60;21966:15;21983;21939:26;:60::i;:::-;22062:25;;21913:86;;-1:-1:-1;22047:95:38;;-1:-1:-1;;;;;;;;22062:25:38;;;;;21913:86;;22114:20;;22047:14;:95::i;:::-;22009:133;;21696:453;;;;;:::o;1776:194:79:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;6081:2078:26;6164:19;6233:7;6243:3;6233:13;6250:1;6233:18;:93;;-1:-1:-1;;;6233:93:26;;;-1:-1:-1;;;6233:93:26;6219:107;;;-1:-1:-1;6354:3:26;6344:13;;:18;6340:95;;-1:-1:-1;;;6379:48:26;6432:3;6378:57;6340:95;6463:3;6453:13;;:18;6449:95;;-1:-1:-1;;;6488:48:26;6541:3;6487:57;6449:95;6572:3;6562:13;;:18;6558:95;;6611:34;6597:48;6650:3;6596:57;6558:95;6681:4;6671:14;;:19;6667:129;;6739:34;6725:48;6778:3;6724:57;6667:129;6823:4;6813:14;;:19;6809:129;;6881:34;6867:48;6920:3;6866:57;6809:129;6965:4;6955:14;;:19;6951:129;;7023:34;7009:48;7062:3;7008:57;6951:129;7107:4;7097:14;;:19;7093:129;;7165:34;7151:48;7204:3;7150:57;7093:129;7249:5;7239:15;;:20;7235:130;;7308:34;7294:48;7347:3;7293:57;7235:130;7392:5;7382:15;;:20;7378:130;;7451:34;7437:48;7490:3;7436:57;7378:130;7535:5;7525:15;;:20;7521:130;;7594:34;7580:48;7633:3;7579:57;7521:130;7678:5;7668:15;;:20;7664:129;;7737:33;7723:47;7775:3;7722:56;7664:129;7820:6;7810:16;;:21;7806:129;;7880:32;7866:46;7917:3;7865:55;7806:129;7962:6;7952:16;;:21;7948:93;;8004:29;7990:43;8038:3;7989:52;7948:93;8069:6;8059:16;;:21;8055:87;;8111:23;8097:37;8139:3;8096:46;8055:87;6081:2078;;;:::o;22155:207:38:-;22285:7;22311:44;22321:33;22339:15;22321;:33;:::i;:::-;22311:9;:44::i;:::-;22304:51;22155:207;-1:-1:-1;;;22155:207:38:o;20567:5181:89:-;20615:7;20733:1;20728;:6;20724:53;;-1:-1:-1;20761:1:89;20567:5181::o;20724:53::-;21717:1;21745;-1:-1:-1;;;21765:16:89;;21761:92;;21808:3;21801:10;;;;;21836:2;21829:9;21761:92;21877:7;21870:2;:15;21866:90;;21912:2;21905:9;;;;;21939:2;21932:9;21866:90;21980:7;21973:2;:15;21969:90;;22015:2;22008:9;;;;;22042:2;22035:9;21969:90;22083:7;22076:2;:15;22072:89;;22118:2;22111:9;;;;;22145:1;22138:8;22072:89;22185:6;22178:2;:14;22174:87;;22219:1;22212:8;;;;;22245:1;22238:8;22174:87;22285:6;22278:2;:14;22274:87;;22319:1;22312:8;;;;;22345:1;22338:8;22274:87;22385:6;22378:2;:14;22374:61;;22419:1;22412:8;22374:61;22861:1;:6;22872:1;22860:13;;;;;24771:1;22860:13;24771:6;;;;:::i;:::-;;24766:2;:11;24765:18;;24760:23;;24891:1;24884:2;24880:1;:6;;;;;:::i;:::-;;24875:2;:11;24874:18;;24869:23;;25002:1;24995:2;24991:1;:6;;;;;:::i;:::-;;24986:2;:11;24985:18;;24980:23;;25111:1;25104:2;25100:1;:6;;;;;:::i;:::-;;25095:2;:11;25094:18;;25089:23;;25221:1;25214:2;25210:1;:6;;;;;:::i;:::-;;25205:2;:11;25204:18;;25199:23;;25331:1;25324:2;25320:1;:6;;;;;:::i;:::-;;25315:2;:11;25314:18;;25309:23;;25703:28;25728:2;25724:1;:6;;;;;:::i;:::-;;25719:11;;;34795:145:90;25703:28:89;25698:33;;;20567:5181;-1:-1:-1;;;20567:5181:89:o;-1:-1:-1:-;;;;;;;;;;;;;;;;;;;;;;;;:::o;14:513:195:-;194:3;179:19;;183:9;275:6;152:4;309:212;323:4;320:1;317:11;309:212;;;386:13;;-1:-1:-1;;;;;382:54:195;370:67;;466:4;457:14;;;;494:17;;;;343:1;336:9;309:212;;;313:3;;;14:513;;;;:::o;1547:180::-;1606:6;1659:2;1647:9;1638:7;1634:23;1630:32;1627:52;;;1675:1;1672;1665:12;1627:52;-1:-1:-1;1698:23:195;;1547:180;-1:-1:-1;1547:180:195:o;1963:297::-;2022:6;2075:2;2063:9;2054:7;2050:23;2046:32;2043:52;;;2091:1;2088;2081:12;2043:52;2130:9;2117:23;-1:-1:-1;;;;;2173:5:195;2169:42;2162:5;2159:53;2149:81;;2226:1;2223;2216:12;3014:164;3091:13;;3144:1;3133:20;;;3123:31;;3113:59;;3168:1;3165;3158:12;3183:204;3251:6;3304:2;3292:9;3283:7;3279:23;3275:32;3272:52;;;3320:1;3317;3310:12;3272:52;3343:38;3371:9;3343:38;:::i;3757:311::-;3834:6;3842;3895:2;3883:9;3874:7;3870:23;3866:32;3863:52;;;3911:1;3908;3901:12;3863:52;3934:38;3962:9;3934:38;:::i;:::-;4034:2;4019:18;;;;4013:25;3924:48;;4013:25;;-1:-1:-1;;;3757:311:195:o;4073:1432::-;4362:3;4347:19;;4351:9;4443:6;4320:4;4487:361;4527:4;4523:1;4510:11;4506:19;4503:29;4487:361;;;4634:13;;-1:-1:-1;;;;;4672:45:195;;4660:58;;4758:3;4754:14;4747:4;4738:14;;4731:38;4798:2;4789:12;;;;4836:1;4824:14;;;;4587:1;4570:19;4487:361;;;4491:3;;;4891:6;4885:13;4879:3;4868:9;4864:19;4857:42;4968:4;4960:6;4956:17;4950:24;4947:1;4936:39;4930:3;4919:9;4915:19;4908:68;5031:2;5023:6;5019:15;5013:22;5007:3;4996:9;4992:19;4985:51;5083:4;5075:6;5071:17;5065:24;5126:3;5115:9;5111:19;5208:1;5218:220;5232:4;5229:1;5226:11;5218:220;;;5297:15;;-1:-1:-1;;;;;5293:52:195;5279:67;;5379:4;5409:19;;;;5368:16;;;;5252:1;5245:9;5218:220;;;5222:3;;;5493;5485:6;5481:16;5475:23;5469:3;5458:9;5454:19;5447:52;4073:1432;;;;;:::o;5510:372::-;5581:2;5575:9;5646:2;5627:13;;-1:-1:-1;;5623:27:195;5611:40;;5681:18;5666:34;;5702:22;;;5663:62;5660:185;;;5767:10;5762:3;5758:20;5755:1;5748:31;5802:4;5799:1;5792:15;5830:4;5827:1;5820:15;5660:185;5861:2;5854:22;5510:372;;-1:-1:-1;5510:372:195:o;5887:620::-;5948:5;6001:3;5994:4;5986:6;5982:17;5978:27;5968:55;;6019:1;6016;6009:12;5968:55;6120:19;6098:2;6120:19;:::i;:::-;6163:3;6201:2;6193:6;6189:15;6227:3;6219:6;6216:15;6213:35;;;6244:1;6241;6234:12;6213:35;6268:6;6283:193;6299:6;6294:3;6291:15;6283:193;;;6391:10;;6414:18;;6461:4;6452:14;;;;6316;6283:193;;;-1:-1:-1;6494:7:195;;5887:620;-1:-1:-1;;;;;5887:620:195:o;6512:1146::-;6655:6;6663;6671;6679;6732:3;6720:9;6711:7;6707:23;6703:33;6700:53;;;6749:1;6746;6739:12;6700:53;6798:7;6791:4;6780:9;6776:20;6772:34;6762:62;;6820:1;6817;6810:12;6762:62;6922:20;6899:3;6922:20;:::i;:::-;6964:3;7005;6994:9;6990:19;7032:7;7024:6;7021:19;7018:39;;;7053:1;7050;7043:12;7018:39;7077:9;7095:268;7111:6;7106:3;7103:15;7095:268;;;7186:3;7180:10;-1:-1:-1;;;;;7227:5:195;7223:46;7216:5;7213:57;7203:85;;7284:1;7281;7274:12;7203:85;7301:18;;7348:4;7339:14;;;;7128;7095:268;;;-1:-1:-1;7432:13:195;7537:3;7522:19;;7516:26;7382:5;;-1:-1:-1;7432:13:195;-1:-1:-1;7516:26:195;-1:-1:-1;7587:65:195;;-1:-1:-1;7644:7:195;7638:3;7623:19;;7587:65;:::i;:::-;7577:75;;6512:1146;;;;;;;:::o;8034:285::-;8109:6;8117;8170:2;8158:9;8149:7;8145:23;8141:32;8138:52;;;8186:1;8183;8176:12;8138:52;8209:38;8237:9;8209:38;:::i;:::-;8199:48;;8266:47;8309:2;8298:9;8294:18;8266:47;:::i;8324:127::-;8385:10;8380:3;8376:20;8373:1;8366:31;8416:4;8413:1;8406:15;8440:4;8437:1;8430:15;8456:127;8517:10;8512:3;8508:20;8505:1;8498:31;8548:4;8545:1;8538:15;8572:4;8569:1;8562:15;8588:127;8649:10;8644:3;8640:20;8637:1;8630:31;8680:4;8677:1;8670:15;8704:4;8701:1;8694:15;8720:168;8793:9;;;8824;;8841:15;;;8835:22;;8821:37;8811:71;;8862:18;;:::i;8893:217::-;8933:1;8959;8949:132;;9003:10;8998:3;8994:20;8991:1;8984:31;9038:4;9035:1;9028:15;9066:4;9063:1;9056:15;8949:132;-1:-1:-1;9095:9:195;;8893:217::o;9115:290::-;9185:6;9238:2;9226:9;9217:7;9213:23;9209:32;9206:52;;;9254:1;9251;9244:12;9206:52;9280:16;;-1:-1:-1;;;;;9325:31:195;;9315:42;;9305:70;;9371:1;9368;9361:12;9410:136;9445:3;-1:-1:-1;;;9466:22:195;;9463:48;;9491:18;;:::i;:::-;-1:-1:-1;9531:1:195;9527:13;;9410:136::o", "linkReferences": {"contracts/libraries/Interest.sol": {"Interest": [{"start": 1283, "length": 20}]}}, "immutableReferences": {"17857": [{"start": 416, "length": 32}], "17860": [{"start": 453, "length": 32}], "17863": [{"start": 1698, "length": 32}], "17866": [{"start": 1735, "length": 32}], "17869": [{"start": 1775, "length": 32}], "17872": [{"start": 1817, "length": 32}], "17875": [{"start": 1857, "length": 32}], "17878": [{"start": 1897, "length": 32}], "17927": [{"start": 2740, "length": 32}], "17930": [{"start": 775, "length": 32}, {"start": 1000, "length": 32}, {"start": 1553, "length": 32}]}}, "methodIdentifiers": {"externalLiquidity()": "861d4d88", "getReserves()": "0902f1ac", "getTickRange()": "37116566", "referenceReserves()": "2c0e7587", "tokens(uint256)": "4f64b2be", "totalAssets()": "01e1d114", "underlyingTokens()": "bd27dc9f", "updateExternalLiquidity(uint112)": "6945e18f"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"Forbidden\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PriceOutOfBounds\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TickOutOfBounds\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"badDebtAssets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"badDebtShares\",\"type\":\"uint256\"}],\"name\":\"BurnBadDebt\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositLAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositXAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"depositYAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowLAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowXAssets\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"borrowYAssets\",\"type\":\"uint128\"}],\"name\":\"InterestAccrued\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reserveXAssets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"reserveYAssets\",\"type\":\"uint256\"}],\"name\":\"Sync\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint112\",\"name\":\"externalLiquidity\",\"type\":\"uint112\"}],\"name\":\"UpdateExternalLiquidity\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"externalLiquidity\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getReserves\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"_reserveXAssets\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"_reserveYAssets\",\"type\":\"uint112\"},{\"internalType\":\"uint32\",\"name\":\"_lastTimestamp\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTickRange\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"minTick\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"maxTick\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"referenceReserves\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"}],\"name\":\"tokens\",\"outputs\":[{\"internalType\":\"contract IAmmalgamERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalAssets\",\"outputs\":[{\"internalType\":\"uint128[6]\",\"name\":\"\",\"type\":\"uint128[6]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"underlyingTokens\",\"outputs\":[{\"internalType\":\"contract IERC20\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"contract IERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint112\",\"name\":\"_externalLiquidity\",\"type\":\"uint112\"}],\"name\":\"updateExternalLiquidity\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Wrapper of the ERC20 tokens that has some functionality similar to the ERC1155.\",\"events\":{\"BurnBadDebt(address,uint256,uint256,uint256)\":{\"details\":\"Emitted when bad debt is burned\",\"params\":{\"badDebtAssets\":\"The amount of bad debt assets being burned\",\"badDebtShares\":\"The amount of bad debt shares being burned\",\"borrower\":\"The address of the borrower\",\"tokenType\":\"The type of token being burned\"}},\"InterestAccrued(uint128,uint128,uint128,uint128,uint128,uint128)\":{\"details\":\"Emitted when Interest gets accrued\",\"params\":{\"borrowLAssets\":\"The amount of total `BORROW_L` assets in the pool after interest accrual\",\"borrowXAssets\":\"The amount of total `BORROW_X` assets in the pool after interest accrual\",\"borrowYAssets\":\"The amount of total `BORROW_Y` assets in the pool after interest accrual\",\"depositLAssets\":\"The amount of total `DEPOSIT_L` assets in the pool after interest accrual\",\"depositXAssets\":\"The amount of total `DEPOSIT_X` assets in the pool after interest accrual\",\"depositYAssets\":\"The amount of total `DEPOSIT_Y` assets in the pool after interest accrual\"}},\"Sync(uint256,uint256)\":{\"details\":\"Emitted when reserves are synchronized\",\"params\":{\"reserveXAssets\":\"The updated reserve for token X\",\"reserveYAssets\":\"The updated reserve for token Y\"}},\"UpdateExternalLiquidity(uint112)\":{\"details\":\"Emitted when external liquidity is updated\",\"params\":{\"externalLiquidity\":\"The updated value for external liquidity\"}}},\"kind\":\"dev\",\"methods\":{\"getReserves()\":{\"returns\":{\"_lastTimestamp\":\"The timestamp of the last operation.\",\"_reserveXAssets\":\"The current reserve of asset X.\",\"_reserveYAssets\":\"The current reserve of asset Y.\"}},\"referenceReserves()\":{\"returns\":{\"_0\":\"The reference reserve for asset X.\",\"_1\":\"The reference reserve for asset Y.\"}},\"tokens(uint256)\":{\"params\":{\"tokenType\":\"The type of token for which the scaler is being computed.                  Can be one of BORROW_X, DEPOSIT_X, BORROW_Y, DEPOSIT_Y, BORROW_L, or DEPOSIT_L.\"},\"returns\":{\"_0\":\"The IAmmalgamERC20 token\"}},\"totalAssets()\":{\"details\":\"If the last lending state update is outdated (i.e., not matching the current block timestamp),      the function recalculates the assets based on the duration since the last update, the lending state,      and reserve balances. If the timestamp is current, the previous asset (without recalculation) is returned.\",\"returns\":{\"_0\":\"totalAssets An array of six `uint128` values representing the total assets for each of the 6 amalgam token types.  These values may be adjusted based on the time elapsed since the last update. If the timestamp is up-to-date, the  previously calculated total assets are returned without recalculation.\"}},\"underlyingTokens()\":{\"returns\":{\"_0\":\"The addresses of the underlying tokens.\"}},\"updateExternalLiquidity(uint112)\":{\"details\":\"This function sets the external liquidity to a new value and emits an event with the new value. It can only be called by the fee setter.\",\"params\":{\"_externalLiquidity\":\"The new external liquidity value.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"getReserves()\":{\"notice\":\"Fetches the current reserves of asset X and asset Y, as well as the block of the last operation.\"},\"referenceReserves()\":{\"notice\":\"Returns the reference reserves for the block, these represent a snapshot of the   reserves at the start of the block weighted for mints, burns, borrow and repayment of   liquidity. These amounts are critical to calculating the correct fees for any swap.\"},\"tokens(uint256)\":{\"notice\":\"Return the IAmmalgamERC20 token corresponding to the token type\"},\"totalAssets()\":{\"notice\":\"Computes the current total Assets.\"},\"underlyingTokens()\":{\"notice\":\"Get the underlying tokens for the AmmalgamERC20Controller.\"},\"updateExternalLiquidity(uint112)\":{\"notice\":\"Updates the external liquidity value.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/tokens/TokenController.sol\":\"TokenController\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/interfaces/ISaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20\",\"dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR\"]},\"contracts/interfaces/factories/IFactoryCallback.sol\":{\"keccak256\":\"0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b\",\"dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT\"]},\"contracts/interfaces/factories/INewTokensFactory.sol\":{\"keccak256\":\"0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b\",\"dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/GeometricTWAP.sol\":{\"keccak256\":\"0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa\",\"dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz\"]},\"contracts/libraries/Interest.sol\":{\"keccak256\":\"0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194\",\"dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX\"]},\"contracts/libraries/Liquidation.sol\":{\"keccak256\":\"0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877\",\"dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/Saturation.sol\":{\"keccak256\":\"0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20\",\"dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/Uint16Set.sol\":{\"keccak256\":\"0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06\",\"dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/TokenController.sol\":{\"keccak256\":\"0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159\",\"dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn\"]},\"lib/mangrove-core/lib/core/BitLib.sol\":{\"keccak256\":\"0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8\",\"dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr\"]},\"lib/morpho-blue/src/libraries/MathLib.sol\":{\"keccak256\":\"0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08\",\"dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "Forbidden"}, {"inputs": [], "type": "error", "name": "PriceOutOfBounds"}, {"inputs": [], "type": "error", "name": "TickOutOfBounds"}, {"inputs": [{"internalType": "address", "name": "borrower", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "tokenType", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "badDebtAssets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "badDebtShares", "type": "uint256", "indexed": false}], "type": "event", "name": "BurnBadDebt", "anonymous": false}, {"inputs": [{"internalType": "uint128", "name": "depositLAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "depositXAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "depositYAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowLAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowXAssets", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "borrowYAssets", "type": "uint128", "indexed": false}], "type": "event", "name": "InterestAccrued", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "reserveXAssets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "reserveYAssets", "type": "uint256", "indexed": false}], "type": "event", "name": "Sync", "anonymous": false}, {"inputs": [{"internalType": "uint112", "name": "externalLiquidity", "type": "uint112", "indexed": false}], "type": "event", "name": "UpdateExternalLiquidity", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "externalLiquidity", "outputs": [{"internalType": "uint112", "name": "", "type": "uint112"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getReserves", "outputs": [{"internalType": "uint112", "name": "_reserveXAssets", "type": "uint112"}, {"internalType": "uint112", "name": "_reserveYAssets", "type": "uint112"}, {"internalType": "uint32", "name": "_lastTimestamp", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getTickRange", "outputs": [{"internalType": "int16", "name": "minTick", "type": "int16"}, {"internalType": "int16", "name": "maxTick", "type": "int16"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "referenceReserves", "outputs": [{"internalType": "uint112", "name": "", "type": "uint112"}, {"internalType": "uint112", "name": "", "type": "uint112"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenType", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "tokens", "outputs": [{"internalType": "contract IAmmalgamERC20", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalAssets", "outputs": [{"internalType": "uint128[6]", "name": "", "type": "uint128[6]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "underlyingTokens", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}, {"internalType": "contract IERC20", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint112", "name": "_externalLiquidity", "type": "uint112"}], "stateMutability": "nonpayable", "type": "function", "name": "updateExternalLiquidity"}], "devdoc": {"kind": "dev", "methods": {"getReserves()": {"returns": {"_lastTimestamp": "The timestamp of the last operation.", "_reserveXAssets": "The current reserve of asset X.", "_reserveYAssets": "The current reserve of asset Y."}}, "referenceReserves()": {"returns": {"_0": "The reference reserve for asset X.", "_1": "The reference reserve for asset Y."}}, "tokens(uint256)": {"params": {"tokenType": "The type of token for which the scaler is being computed.                  Can be one of BORROW_X, DEPOSIT_X, BORROW_Y, DEPOSIT_Y, BORROW_L, or DEPOSIT_L."}, "returns": {"_0": "The IAmmalgamERC20 token"}}, "totalAssets()": {"details": "If the last lending state update is outdated (i.e., not matching the current block timestamp),      the function recalculates the assets based on the duration since the last update, the lending state,      and reserve balances. If the timestamp is current, the previous asset (without recalculation) is returned.", "returns": {"_0": "totalAssets An array of six `uint128` values representing the total assets for each of the 6 amalgam token types.  These values may be adjusted based on the time elapsed since the last update. If the timestamp is up-to-date, the  previously calculated total assets are returned without recalculation."}}, "underlyingTokens()": {"returns": {"_0": "The addresses of the underlying tokens."}}, "updateExternalLiquidity(uint112)": {"details": "This function sets the external liquidity to a new value and emits an event with the new value. It can only be called by the fee setter.", "params": {"_externalLiquidity": "The new external liquidity value."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"getReserves()": {"notice": "Fetches the current reserves of asset X and asset Y, as well as the block of the last operation."}, "referenceReserves()": {"notice": "Returns the reference reserves for the block, these represent a snapshot of the   reserves at the start of the block weighted for mints, burns, borrow and repayment of   liquidity. These amounts are critical to calculating the correct fees for any swap."}, "tokens(uint256)": {"notice": "Return the IAmmalgamERC20 token corresponding to the token type"}, "totalAssets()": {"notice": "Computes the current total Assets."}, "underlyingTokens()": {"notice": "Get the underlying tokens for the AmmalgamERC20Controller."}, "updateExternalLiquidity(uint112)": {"notice": "Updates the external liquidity value."}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/tokens/TokenController.sol": "TokenController"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/interfaces/ISaturationAndGeometricTWAPState.sol": {"keccak256": "0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c", "urls": ["bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20", "dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IFactoryCallback.sol": {"keccak256": "0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52", "urls": ["bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b", "dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/INewTokensFactory.sol": {"keccak256": "0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9", "urls": ["bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b", "dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/GeometricTWAP.sol": {"keccak256": "0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e", "urls": ["bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa", "dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz"], "license": "GPL-3.0-only"}, "contracts/libraries/Interest.sol": {"keccak256": "0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646", "urls": ["bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194", "dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX"], "license": "GPL-3.0-only"}, "contracts/libraries/Liquidation.sol": {"keccak256": "0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63", "urls": ["bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877", "dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/Saturation.sol": {"keccak256": "0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860", "urls": ["bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20", "dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Uint16Set.sol": {"keccak256": "0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f", "urls": ["bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06", "dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy"], "license": "GPL-3.0-only"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/TokenController.sol": {"keccak256": "0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6", "urls": ["bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159", "dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn"], "license": "GPL-3.0-only"}, "lib/mangrove-core/lib/core/BitLib.sol": {"keccak256": "0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3", "urls": ["bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8", "dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr"], "license": "MIT"}, "lib/morpho-blue/src/libraries/MathLib.sol": {"keccak256": "0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a", "urls": ["bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08", "dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd"], "license": "GPL-2.0-or-later"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}}, "version": 1}, "id": 38}