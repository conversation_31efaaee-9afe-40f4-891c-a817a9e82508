{"abi": [{"type": "function", "name": "checkSoftPremiums", "inputs": [{"name": "saturationAndGeometricTWAPState", "type": "ISaturationAndGeometricTWAPState", "internalType": "contract ISaturationAndGeometricTWAPState"}, {"name": "inputParams", "type": "tuple", "internalType": "struct Validation.InputParams", "components": [{"name": "userAssets", "type": "uint256[6]", "internalType": "uint256[6]"}, {"name": "sqrtPriceMinInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "sqrtPriceMaxInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityScalerInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesYAssets", "type": "uint256", "internalType": "uint256"}]}, {"name": "borrower", "type": "address", "internalType": "address"}, {"name": "depositLToTransferInLAssets", "type": "uint256", "internalType": "uint256"}, {"name": "depositXToTransferInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "depositYToTransferInYAssets", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "liquidateLeverageCalcDeltaAndPremium", "inputs": [{"name": "inputParams", "type": "tuple", "internalType": "struct Validation.InputParams", "components": [{"name": "userAssets", "type": "uint256[6]", "internalType": "uint256[6]"}, {"name": "sqrtPriceMinInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "sqrtPriceMaxInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityScalerInQ72", "type": "uint256", "internalType": "uint256"}, {"name": "activeLiquidityAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "reservesYAssets", "type": "uint256", "internalType": "uint256"}]}, {"name": "depositL", "type": "bool", "internalType": "bool"}, {"name": "repayL", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "leveragedLiquidationParams", "type": "tuple", "internalType": "struct Liquidation.LeveragedLiquidationParams", "components": [{"name": "closeInLAssets", "type": "uint256", "internalType": "uint256"}, {"name": "closeInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "closeInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "premiumInLAssets", "type": "uint256", "internalType": "uint256"}, {"name": "premiumLInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "premiumLInYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "badDebt", "type": "bool", "internalType": "bool"}]}], "stateMutability": "pure"}, {"type": "error", "name": "LiquidationPremiumTooHigh", "inputs": []}, {"type": "error", "name": "NotEnoughRepaidForLiquidation", "inputs": []}, {"type": "error", "name": "TooMuchDepositToTransferForLeverageLiquidation", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "670:16974:23:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;670:16974:23;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "670:16974:23:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3979:668;;;;;;:::i;:::-;;:::i;:::-;;5202:4398;;;;;;:::i;:::-;;:::i;:::-;;;;;;3926:4:195;3968:3;3957:9;3953:19;3945:27;;4005:6;3999:13;3988:9;3981:32;4069:4;4061:6;4057:17;4051:24;4044:4;4033:9;4029:20;4022:54;4132:4;4124:6;4120:17;4114:24;4107:4;4096:9;4092:20;4085:54;4195:4;4187:6;4183:17;4177:24;4170:4;4159:9;4155:20;4148:54;4258:4;4250:6;4246:17;4240:24;4233:4;4222:9;4218:20;4211:54;4321:4;4313:6;4309:17;4303:24;4296:4;4285:9;4281:20;4274:54;4398:4;4390:6;4386:17;4380:24;4373:32;4366:40;4359:4;4348:9;4344:20;4337:70;3738:675;;;;;5202:4398:23;;;;;;;;3979:668;4438:157;4479:11;4492:27;4521;4550;4438:19;:157::i;:::-;4339:80;4364:31;4397:11;4410:8;4339:24;:80::i;:::-;:256;4322:318;;;4613:27;;-1:-1:-1;;;4613:27:23;;;;;;;;;;;4322:318;3979:668;;;;;;:::o;5202:4398::-;5372:60;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5372:60:23;5458:27;5499:26;5647:47;5697:41;5726:11;5697:28;:41::i;:::-;5647:91;;5946:1;5905:14;:37;;;5865:14;:37;;;:77;;;;:::i;:::-;5864:83;;;;:::i;:::-;5822:125;;6066:1;6026:14;:36;;;5987:14;:36;;;:75;;;;:::i;:::-;5986:81;;;;:::i;:::-;5965:102;-1:-1:-1;6233:58:23;;-1:-1:-1;6272:19:23;4114:2:30;6233:58:23;:::i;:::-;6163:47;6192:18;4008:3:30;6163:47:23;:::i;:::-;:128;6095:3489;;;6451:321;1291:1;6621:58;6660:19;4114:2:30;6621:58:23;:::i;:::-;6547:47;6576:18;4008:3:30;6547:47:23;:::i;:::-;:132;;;;:::i;:::-;4008:3:30;6749:5:23;6451:14;:321::i;:::-;6405:43;;;:367;;;6795:65;;6791:534;;;6977:43;;;:65;;;6791:534;;;7263:43;;;;7241:65;;:19;:65;:::i;:::-;7173:133;;6791:534;7368:41;;7347:62;;7343:446;;;7619:41;;:62;;7663:18;;7619:62;:::i;:::-;7589:93;;4008:3:30;7589:93:23;:::i;:::-;7520:162;;:26;;:162;;;;;:::i;:::-;;;-1:-1:-1;7343:446:23;;;7766:4;7729:34;;;:41;7343:446;7812:8;7811:9;:20;;;;7825:6;7824:7;7811:20;7807:1763;;;7855:20;7878:307;7970:26;:43;;;7926:26;:41;;;:87;;;;:::i;:::-;8039:11;:29;;;8094:11;:38;;;8158:5;7878:22;:307::i;:::-;8333:43;;;;8402:41;;7855:330;;-1:-1:-1;8255:287:23;;7855:330;;8333:43;8402:87;;8333:43;;8402:87;:::i;:::-;8515:5;8255:14;:287::i;:::-;8208:44;;;:334;;;8633:59;;:12;:59;:::i;:::-;8565:41;;;:127;8830:43;;;;8786:41;;8715:20;;8738:307;;8786:87;;8830:43;8786:87;:::i;:::-;8899:11;:29;;;8954:11;:38;;;9018:5;8738:22;:307::i;:::-;9193:43;;;;9262:41;;8715:330;;-1:-1:-1;9115:287:23;;8715:330;;9193:43;9262:87;;9193:43;;9262:87;:::i;9115:287::-;9068:44;;;:334;;;9492:59;;:12;:59;:::i;:::-;9424:41;;;:127;-1:-1:-1;;7807:1763:23;5444:4150;;5202:4398;;;;;:::o;14779:1304::-;15083:22;;:33;;;15153;;;15223;;;;;15023:21;;15153:33;15285:21;;:56;;;;;15314:27;15310:1;:31;15285:56;15284:136;;;-1:-1:-1;15363:21:23;;:56;;;;;15392:27;15388:1;:31;15363:56;15284:214;;;-1:-1:-1;15441:21:23;;:56;;;;;15470:27;15466:1;:31;15441:56;15267:291;;;-1:-1:-1;;15523:24:23;;;;;;;15267:291;15596:20;;15592:149;;15652:74;15667:27;2789:6:30;15702:16:23;15720:5;15652:14;:74::i;:::-;15636:90;;15592:149;15758:20;;15754:150;;15815:74;15830:27;2789:6:30;15865:16:23;15883:5;15815:14;:74::i;:::-;15798:91;;;;15754:150;15921:20;;15917:150;;15978:74;15993:27;2789:6:30;16028:16:23;16046:5;15978:14;:74::i;:::-;15961:91;;;;15917:150;15046:1037;;;14779:1304;;;;;;;:::o;16491:1151::-;16704:22;16788:32;16822;16870:54;16901:11;:22;;;16870:30;:54::i;:::-;16787:137;;;;16970:21;17001;17040:24;17036:1;:28;:60;;;;17072:24;17068:1;:28;17036:60;17032:297;;;17145:173;;-1:-1:-1;;;17145:173:23;;-1:-1:-1;;;;;17145:54:23;;;;;:173;;17217:11;;17230:24;;17256;;17290:4;;17297:7;;17145:173;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;17112:206;;-1:-1:-1;17112:206:23;-1:-1:-1;17032:297:23;17338:17;17358:38;17367:13;17382;17358:8;:38::i;:::-;17338:58;;2789:6:30;17493:9:23;:16;17489:30;;;17518:1;17511:8;;;;;;;;;17489:30;4344:2:30;-1:-1:-1;;17601:16:23;;17600:25;17583:42;;16728:914;;;;;16491:1151;;;;;;:::o;3450:452:29:-;3546:36;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3546:36:29;3628:22;;:33;3594:31;;;:67;3750:27;3628:22;3750:14;:27::i;:::-;3710:36;;;3671:106;3672:36;;;3671:106;3868:27;3883:11;3868:14;:27::i;:::-;3827:37;;;3787:108;;;3788:14;3450:452;-1:-1:-1;3450:452:29:o;1908:204:20:-;1997:14;2032:5;2036:1;2032;:5;:::i;:::-;2023:14;;2056:10;:49;;2095:10;2104:1;2095:6;:10;:::i;:::-;2056:49;;;2069:23;2082:6;2090:1;2069:12;:23::i;:::-;2047:58;1908:204;-1:-1:-1;;;;;1908:204:20:o;11407:381:29:-;11577:21;11614:6;11624:1;11614:11;11610:25;;-1:-1:-1;11634:1:29;11627:8;;11610:25;11673:108;11688:64;11703:26;11731:6;-1:-1:-1;;;11744:7:29;11688:14;:64::i;:::-;11754:12;-1:-1:-1;;;11773:7:29;11673:14;:108::i;12871:334::-;13041:21;13078:6;13088:1;13078:11;13074:25;;-1:-1:-1;13098:1:29;13091:8;;13074:25;13125:73;13140:26;13168:6;13176:12;13190:7;13125:14;:73::i;68070:5273:25:-;68405:20;;;;68373:21;;68492:20;;;;68405;68460:21;;;68579:20;;;;68547:21;;;;68166:34;;;;68366:60;;68453;;;;68540;4401:3:30;68997:26:25;68166:34;;;69112:15;;;;:42;;69144:10;69143:11;;69112:42;;;69130:10;69112:42;69088:67;-1:-1:-1;69170:17:25;69190:15;-1:-1:-1;69190:15:25;;69243:39;;69272:10;69271:11;;69243:39;;;69258:10;69243:39;7233:3;69313:53;;-1:-1:-1;69313:53:25;;-1:-1:-1;69433:12:25;69428:43;;69461:10;69460:11;;69447:24;;69428:43;69486:17;69506:15;-1:-1:-1;69506:15:25;;69559:39;;69588:10;69587:11;;69559:39;;;69574:10;69559:39;6988:2;69629:44;;-1:-1:-1;69629:44:25;;-1:-1:-1;69740:12:25;69735:43;;69768:10;69767:11;;69754:24;;69735:43;68748:1041;;69827:13;69844:1;69827:18;69823:2707;;70050:26;70079:56;70094:13;-1:-1:-1;;;70114:13:25;70129:5;70079:14;:56::i;:::-;70199:20;;;;70050:85;;-1:-1:-1;70223:1:25;70199:25;70195:270;;70277:18;70248:47;;70195:270;;;70428:18;70399:47;;70195:270;69847:632;69823:2707;;;70526:13;70543:1;70526:18;70522:2008;;70701:26;70730:56;70745:13;-1:-1:-1;;;70765:13:25;70780:5;70730:14;:56::i;:::-;70850:20;;;;70701:85;;-1:-1:-1;70874:1:25;70850:25;70846:270;;70928:18;70899:47;;70846:270;;;71079:18;-1:-1:-1;70522:2008:25;;;71177:13;71194:1;71177:18;71173:1357;;71534:21;;;;71530:25;71526:116;;71587:21;;;;71583:25;71579:44;;-1:-1:-1;71618:1:25;;;;-1:-1:-1;68070:5273:25;-1:-1:-1;;;;;;;68070:5273:25:o;71579:44::-;71675:26;71704:68;71714:57;71729:13;-1:-1:-1;;;71750:13:25;71765:5;71714:14;:57::i;:::-;71704:9;:68::i;:::-;71840:21;;;;71675:97;;-1:-1:-1;71836:25:25;71832:270;;71914:18;71885:47;;71832:270;;71173:1357;72255:260;72315:182;;;;;;;;72382:10;72315:182;;;;72394:10;72315:182;;;;72406:10;72315:182;;;;72418:13;72315:182;;;;72433:10;373:1:19;72433:20:25;;;;;;;:::i;:::-;;;;;72315:182;;;;72455:10;404:1:19;72455:20:25;;;;;;;:::i;:::-;;;;;72315:182;;72255:38;:260::i;:::-;72196:319;;-1:-1:-1;72196:319:25;-1:-1:-1;71173:1357:25;1323:6:26;72590:26:25;:59;:142;;;;72706:26;1385:34:26;72673:59:25;72590:142;72569:241;;;72794:1;72765:30;;72569:241;1323:6:26;72844:26:25;:59;:142;;;;72960:26;1385:34:26;72927:59:25;72844:142;72823:241;;;73048:1;73019:30;;72823:241;73128:30;;73124:203;;73227:85;73242:26;4401:3:30;6988:2:25;73306:5;73227:14;:85::i;:::-;73178:134;;73124:203;68238:5105;;;;;;68070:5273;;;:::o;5435:111:89:-;5527:5;;;5312;;;5311:36;5306:42;;5435:111;;;;;:::o;9114:996:29:-;9302:22;;:32;;;;9415;;;;;9302;;9415:36;9411:342;;9532:22;;9492:250;;373:1:19;9532:32:29;;;;9582:11;:29;;;9668:11;:38;;;9724:4;9492:22;:250::i;:::-;9467:275;;;;:::i;:::-;;;9411:342;9766:22;;:32;;;:36;9762:342;;9883:22;;9843:250;;404:1:19;9883:32:29;;;;9933:11;:29;;;10019:11;:38;;;10075:4;9843:22;:250::i;:::-;9818:275;;;;:::i;:::-;;;9762:342;9114:996;;;:::o;8096:1012::-;8287:22;;:33;;;8407;;;;8287;;8403:37;8399:347;;8522:22;;8482:253;;279:1:19;8522:33:29;;;;8573:11;:29;;;8660:11;:38;;;8716:5;8482:22;:253::i;:::-;8456:279;;;;:::i;:::-;;;8399:347;8763:22;;:33;;;8759:37;8755:347;;8878:22;;8838:253;;311:1:19;8878:33:29;;;;8929:11;:29;;;9016:11;:38;;;9072:5;8838:22;:253::i;6215:704:89:-;6277:7;6300:1;6305;6300:6;6296:150;;6400:35;1035:4:79;6400:11:89;:35::i;:::-;6896:1;6891;6887;:5;6886:11;;;;;:::i;:::-;;6900:1;6886:15;6876:5;;;6860:42;;6215:704;-1:-1:-1;;;6215:704:89:o;20567:5181::-;20615:7;20733:1;20728;:6;20724:53;;-1:-1:-1;20761:1:89;20567:5181::o;20724:53::-;21717:1;21745;-1:-1:-1;;;21765:16:89;;21761:92;;21808:3;21801:10;;;;;21836:2;21829:9;21761:92;21877:7;21870:2;:15;21866:90;;21912:2;21905:9;;;;;21939:2;21932:9;21866:90;21980:7;21973:2;:15;21969:90;;22015:2;22008:9;;;;;22042:2;22035:9;21969:90;22083:7;22076:2;:15;22072:89;;22118:2;22111:9;;;;;22145:1;22138:8;22072:89;22185:6;22178:2;:14;22174:87;;22219:1;22212:8;;;;;22245:1;22238:8;22174:87;22285:6;22278:2;:14;22274:87;;22319:1;22312:8;;;;;22345:1;22338:8;22274:87;22385:6;22378:2;:14;22374:61;;22419:1;22412:8;22374:61;22861:1;:6;22872:1;22860:13;;;;;24771:1;22860:13;24771:6;;;;:::i;:::-;;24766:2;:11;24765:18;;24760:23;;24891:1;24884:2;24880:1;:6;;;;;:::i;:::-;;24875:2;:11;24874:18;;24869:23;;25002:1;24995:2;24991:1;:6;;;;;:::i;:::-;;24986:2;:11;24985:18;;24980:23;;25111:1;25104:2;25100:1;:6;;;;;:::i;:::-;;25095:2;:11;25094:18;;25089:23;;25221:1;25214:2;25210:1;:6;;;;;:::i;:::-;;25205:2;:11;25204:18;;25199:23;;25331:1;25324:2;25320:1;:6;;;;;:::i;:::-;;25315:2;:11;25314:18;;25309:23;;25703:28;25728:2;25724:1;:6;;;;;:::i;:::-;;25719:11;;;34795:145:90;25703:28:89;25698:33;;;20567:5181;-1:-1:-1;;;20567:5181:89:o;73662:2777:25:-;74039:16;;74194;;;;74175;;;;73803:34;;;;;;;;74039:16;74171:39;:1;:39;74145:23;;;:65;74228:17;;;74224:36;;;-1:-1:-1;74255:1:25;;;;-1:-1:-1;73662:2777:25;-1:-1:-1;;;;;73662:2777:25:o;74224:36::-;74489:13;74506:1;74489:18;74485:37;;-1:-1:-1;74517:1:25;;;;-1:-1:-1;73662:2777:25;-1:-1:-1;;;;;73662:2777:25:o;74485:37::-;74565:24;74599:33;74617:13;74599:9;:33::i;:::-;74844:19;;;;74669:30;;;;-1:-1:-1;74736:30:25;;;-1:-1:-1;74820:21:25;;-1:-1:-1;74820:21:25;;-1:-1:-1;74820:21:25;75071:24;;;:71;;75122:20;75071:71;;;75099:20;75098:21;;75071:71;75013:130;;75182:202;75244:1;75222:19;:23;:68;;75271:19;75222:68;;;75249:19;75248:20;;75222:68;-1:-1:-1;;;75334:13:25;75330:1;:17;75365:5;75182:14;:202::i;:::-;75157:227;;75424:70;75439:23;-1:-1:-1;;;75473:13:25;75469:1;:17;75488:5;75424:14;:70::i;:::-;75398:96;;74989:516;75519:5;:21;;;75544:1;75519:26;75515:918;;75565:5;:21;;;75590:1;75565:26;75561:390;;75690:22;75661:51;;75759:23;75730:52;;75515:918;;75561:390;75914:22;75885:51;;75515:918;;;76064:5;:21;;;76089:1;76064:26;76060:363;;76176:23;76147:52;;76060:363;;;76316:22;76287:51;;76385:23;76356:52;;76060:363;73875:2564;;;;;73662:2777;;;:::o;10973:428:29:-;11157:21;11194:15;11213:1;11194:20;11190:34;;-1:-1:-1;11223:1:29;11216:8;;11190:34;11250:144;11278:64;11293:15;-1:-1:-1;;;11315:17:29;11334:7;11278:14;:64::i;:::-;-1:-1:-1;;;11349:26:29;11377:7;11250:14;:144::i;12472:393::-;12674:23;12713:15;12732:1;12713:20;12709:34;;-1:-1:-1;12742:1:29;12735:8;;12709:34;12771:87;12786:15;12803:17;12822:26;12850:7;12771:14;:87::i;1776:194:79:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;14:165:195;-1:-1:-1;;;;;123:31:195;;113:42;;103:70;;169:1;166;159:12;103:70;14:165;:::o;184:127::-;245:10;240:3;236:20;233:1;226:31;276:4;273:1;266:15;300:4;297:1;290:15;316:253;388:2;382:9;430:4;418:17;;465:18;450:34;;486:22;;;447:62;444:88;;;512:18;;:::i;:::-;548:2;541:22;316:253;:::o;574:247::-;641:2;635:9;683:3;671:16;;717:18;702:34;;738:22;;;699:62;696:88;;;764:18;;:::i;826:1371::-;884:5;932:6;920:9;915:3;911:19;907:32;904:52;;;952:1;949;942:12;904:52;974:22;;:::i;:::-;965:31;;1041:3;1034:4;1023:9;1019:20;1015:30;1005:58;;1059:1;1056;1049:12;1005:58;1083:17;;:::i;:::-;1122:3;1163;1152:9;1148:19;1190:3;1182:6;1179:15;1176:35;;;1207:1;1204;1197:12;1176:35;1231:9;1249:206;1265:6;1260:3;1257:15;1249:206;;;1361:17;;1391:20;;1440:4;1431:14;;;;1282;1249:206;;;-1:-1:-1;1464:20:195;;;1529;1576:4;1565:16;;1558:33;-1:-1:-1;1664:4:195;1649:20;;1636:34;1697:4;1686:16;;1679:33;1785:3;1770:19;;1757:33;1817:4;1806:16;;1799:33;1905:3;1890:19;;1877:33;1937:4;1926:16;;1919:33;2025:3;2010:19;;1997:33;2057:4;2046:16;;2039:33;2145:3;2130:19;;;2117:33;2177:3;2166:15;;2159:32;1471:5;826:1371;-1:-1:-1;826:1371:195:o;2202:986::-;2377:6;2385;2393;2401;2409;2417;2470:3;2458:9;2449:7;2445:23;2441:33;2438:53;;;2487:1;2484;2477:12;2438:53;2526:9;2513:23;2545:65;2604:5;2545:65;:::i;:::-;2629:5;-1:-1:-1;2653:58:195;2703:7;2698:2;2683:18;;2653:58;:::i;:::-;2643:68;;2763:3;2752:9;2748:19;2735:33;2777:67;2836:7;2777:67;:::i;:::-;2202:986;;;;-1:-1:-1;2863:7:195;;2943:3;2928:19;;2915:33;;-1:-1:-1;3047:3:195;3032:19;;3019:33;;3151:3;3136:19;;;3123:33;;-1:-1:-1;2202:986:195;-1:-1:-1;;2202:986:195:o;3193:160::-;3258:20;;3314:13;;3307:21;3297:32;;3287:60;;3343:1;3340;3333:12;3287:60;3193:160;;;:::o;3358:375::-;3459:6;3467;3475;3528:3;3516:9;3507:7;3503:23;3499:33;3496:53;;;3545:1;3542;3535:12;3496:53;3568:49;3609:7;3598:9;3568:49;:::i;:::-;3558:59;;3636:36;3667:3;3656:9;3652:19;3636:36;:::i;:::-;3626:46;;3691:36;3722:3;3711:9;3707:19;3691:36;:::i;:::-;3681:46;;3358:375;;;;;:::o;4418:127::-;4479:10;4474:3;4470:20;4467:1;4460:31;4510:4;4507:1;4500:15;4534:4;4531:1;4524:15;4550:125;4615:9;;;4636:10;;;4633:36;;;4649:18;;:::i;4680:127::-;4741:10;4736:3;4732:20;4729:1;4722:31;4772:4;4769:1;4762:15;4796:4;4793:1;4786:15;4812:217;4852:1;4878;4868:132;;4922:10;4917:3;4913:20;4910:1;4903:31;4957:4;4954:1;4947:15;4985:4;4982:1;4975:15;4868:132;-1:-1:-1;5014:9:195;;4812:217::o;5034:168::-;5107:9;;;5138;;5155:15;;;5149:22;;5135:37;5125:71;;5176:18;;:::i;5207:128::-;5274:9;;;5295:11;;;5292:37;;;5309:18;;:::i;5340:127::-;5401:10;5396:3;5392:20;5389:1;5382:31;5432:4;5429:1;5422:15;5456:4;5453:1;5446:15;5581:1212;5910:13;;5887:3;5872:19;;;5876:9;5845:4;6030:171;6044:4;6041:1;6038:11;6030:171;;;6103:13;;6091:26;;6146:4;6174:17;;;;6137:14;;;;6064:1;6057:9;6030:171;;;6034:3;;;6257:4;6249:6;6245:17;6239:24;6232:4;6221:9;6217:20;6210:54;6320:4;6312:6;6308:17;6302:24;6295:4;6284:9;6280:20;6273:54;6385:4;6377:6;6373:17;6367:24;6358:6;6347:9;6343:22;6336:56;6450:4;6442:6;6438:17;6432:24;6423:6;6412:9;6408:22;6401:56;6515:4;6507:6;6503:17;6497:24;6488:6;6477:9;6473:22;6466:56;6580:4;6572:6;6568:17;6562:24;6553:6;6542:9;6538:22;6531:56;6624:6;6618:3;6607:9;6603:19;6596:35;6668:6;6662:3;6651:9;6647:19;6640:35;6684:47;6726:3;6715:9;6711:19;6703:6;-1:-1:-1;;;;;5538:31:195;5526:44;;5472:104;6684:47;-1:-1:-1;;;;;5538:31:195;;6782:3;6767:19;;5526:44;5581:1212;;;;;;;;:::o;6798:343::-;6877:6;6885;6938:2;6926:9;6917:7;6913:23;6909:32;6906:52;;;6954:1;6951;6944:12;6906:52;-1:-1:-1;;6999:16:195;;7105:2;7090:18;;;7084:25;6999:16;;7084:25;;-1:-1:-1;6798:343:195:o", "linkReferences": {}}, "methodIdentifiers": {"checkSoftPremiums(ISaturationAndGeometricTWAPState,Validation.InputParams,address,uint256,uint256,uint256)": "1eaa6783", "liquidateLeverageCalcDeltaAndPremium(Validation.InputParams,bool,bool)": "4b8109c6"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"LiquidationPremiumTooHigh\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotEnoughRepaidForLiquidation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TooMuchDepositToTransferForLeverageLiquidation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"contract ISaturationAndGeometricTWAPState\",\"name\":\"saturationAndGeometricTWAPState\",\"type\":\"ISaturationAndGeometricTWAPState\"},{\"components\":[{\"internalType\":\"uint256[6]\",\"name\":\"userAssets\",\"type\":\"uint256[6]\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMinInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMaxInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityScalerInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesYAssets\",\"type\":\"uint256\"}],\"internalType\":\"struct Validation.InputParams\",\"name\":\"inputParams\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"borrower\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"depositLToTransferInLAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"depositXToTransferInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"depositYToTransferInYAssets\",\"type\":\"uint256\"}],\"name\":\"checkSoftPremiums\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint256[6]\",\"name\":\"userAssets\",\"type\":\"uint256[6]\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMinInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"sqrtPriceMaxInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityScalerInQ72\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"activeLiquidityAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"reservesYAssets\",\"type\":\"uint256\"}],\"internalType\":\"struct Validation.InputParams\",\"name\":\"inputParams\",\"type\":\"tuple\"},{\"internalType\":\"bool\",\"name\":\"depositL\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"repayL\",\"type\":\"bool\"}],\"name\":\"liquidateLeverageCalcDeltaAndPremium\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"closeInLAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"closeInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"closeInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"premiumInLAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"premiumLInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"premiumLInYAssets\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"badDebt\",\"type\":\"bool\"}],\"internalType\":\"struct Liquidation.LeveragedLiquidationParams\",\"name\":\"leveragedLiquidationParams\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"liquidateLeverageCalcDeltaAndPremium(Validation.InputParams,bool,bool)\":{\"params\":{\"depositL\":\"Flag indicating whether the liquidator is transferring depositL.\",\"inputParams\":\"The params representing the position of the borrower.\",\"repayL\":\"Flag indicating whether the liquidator is repaying borrowL.\"},\"returns\":{\"leveragedLiquidationParams\":\"a struct of type LeveragedLiquidationParams containing         the amounts to be closed and the premium to be paid.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"liquidateLeverageCalcDeltaAndPremium(Validation.InputParams,bool,bool)\":{\"notice\":\"Calculate the amount to be closed (from both deposit and borrow) and premium to be         paid.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/Liquidation.sol\":\"Liquidation\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/interfaces/ISaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20\",\"dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/GeometricTWAP.sol\":{\"keccak256\":\"0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa\",\"dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz\"]},\"contracts/libraries/Interest.sol\":{\"keccak256\":\"0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194\",\"dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX\"]},\"contracts/libraries/Liquidation.sol\":{\"keccak256\":\"0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877\",\"dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/Saturation.sol\":{\"keccak256\":\"0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20\",\"dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/Uint16Set.sol\":{\"keccak256\":\"0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06\",\"dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"lib/mangrove-core/lib/core/BitLib.sol\":{\"keccak256\":\"0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8\",\"dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr\"]},\"lib/morpho-blue/src/libraries/MathLib.sol\":{\"keccak256\":\"0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08\",\"dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "LiquidationPremiumTooHigh"}, {"inputs": [], "type": "error", "name": "NotEnoughRepaidForLiquidation"}, {"inputs": [], "type": "error", "name": "TooMuchDepositToTransferForLeverageLiquidation"}, {"inputs": [{"internalType": "contract ISaturationAndGeometricTWAPState", "name": "saturationAndGeometricTWAPState", "type": "ISaturationAndGeometricTWAPState"}, {"internalType": "struct Validation.InputParams", "name": "inputParams", "type": "tuple", "components": [{"internalType": "uint256[6]", "name": "userAssets", "type": "uint256[6]"}, {"internalType": "uint256", "name": "sqrtPriceMinInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "sqrtPriceMaxInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityScalerInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesYAssets", "type": "uint256"}]}, {"internalType": "address", "name": "borrower", "type": "address"}, {"internalType": "uint256", "name": "depositLToTransferInLAssets", "type": "uint256"}, {"internalType": "uint256", "name": "depositXToTransferInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "depositYToTransferInYAssets", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "checkSoftPremiums"}, {"inputs": [{"internalType": "struct Validation.InputParams", "name": "inputParams", "type": "tuple", "components": [{"internalType": "uint256[6]", "name": "userAssets", "type": "uint256[6]"}, {"internalType": "uint256", "name": "sqrtPriceMinInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "sqrtPriceMaxInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityScalerInQ72", "type": "uint256"}, {"internalType": "uint256", "name": "activeLiquidityAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "reservesYAssets", "type": "uint256"}]}, {"internalType": "bool", "name": "depositL", "type": "bool"}, {"internalType": "bool", "name": "repayL", "type": "bool"}], "stateMutability": "pure", "type": "function", "name": "liquidateLeverageCalcDeltaAndPremium", "outputs": [{"internalType": "struct Liquidation.LeveragedLiquidationParams", "name": "leveragedLiquidationParams", "type": "tuple", "components": [{"internalType": "uint256", "name": "closeInLAssets", "type": "uint256"}, {"internalType": "uint256", "name": "closeInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "closeInYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "premiumInLAssets", "type": "uint256"}, {"internalType": "uint256", "name": "premiumLInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "premiumLInYAssets", "type": "uint256"}, {"internalType": "bool", "name": "badDebt", "type": "bool"}]}]}], "devdoc": {"kind": "dev", "methods": {"liquidateLeverageCalcDeltaAndPremium(Validation.InputParams,bool,bool)": {"params": {"depositL": "Flag indicating whether the liquidator is transferring depositL.", "inputParams": "The params representing the position of the borrower.", "repayL": "Flag indicating whether the liquidator is repaying borrowL."}, "returns": {"leveragedLiquidationParams": "a struct of type LeveragedLiquidationParams containing         the amounts to be closed and the premium to be paid."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"liquidateLeverageCalcDeltaAndPremium(Validation.InputParams,bool,bool)": {"notice": "Calculate the amount to be closed (from both deposit and borrow) and premium to be         paid."}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/Liquidation.sol": "Liquidation"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/interfaces/ISaturationAndGeometricTWAPState.sol": {"keccak256": "0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c", "urls": ["bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20", "dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/GeometricTWAP.sol": {"keccak256": "0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e", "urls": ["bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa", "dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz"], "license": "GPL-3.0-only"}, "contracts/libraries/Interest.sol": {"keccak256": "0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646", "urls": ["bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194", "dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX"], "license": "GPL-3.0-only"}, "contracts/libraries/Liquidation.sol": {"keccak256": "0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63", "urls": ["bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877", "dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/Saturation.sol": {"keccak256": "0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860", "urls": ["bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20", "dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Uint16Set.sol": {"keccak256": "0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f", "urls": ["bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06", "dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy"], "license": "GPL-3.0-only"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "lib/mangrove-core/lib/core/BitLib.sol": {"keccak256": "0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3", "urls": ["bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8", "dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr"], "license": "MIT"}, "lib/morpho-blue/src/libraries/MathLib.sol": {"keccak256": "0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a", "urls": ["bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08", "dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd"], "license": "GPL-2.0-or-later"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}}, "version": 1}, "id": 23}