# ERC4626DepositToken ownerBurn Vulnerability - Audit Report

## Summary
The `ownerBurn()` function in `ERC4626DepositToken` incorrectly burns tokens from `msg.sender` (the pair contract) instead of the intended `sender` parameter (the token owner), causing token accounting corruption during withdrawal operations.

## Finding Description
The `ERC4626DepositToken.ownerBurn()` function violates the `IAmmalgamERC20` interface specification by burning tokens from the wrong account. According to the interface, the function should burn tokens from the `sender` parameter (the token owner), but the implementation burns from `msg.sender` (the pair contract).

**Vulnerable Code:**
```solidity
function ownerBurn(address sender, address to, uint256 assets, uint256 shares) public virtual override onlyOwner {
    emit Withdraw(msg.sender, to, sender, assets, shares);
    _burn(msg.sender, shares); // ❌ Burns from msg.sender instead of sender
}
```

**Interface Specification:**
```solidity
/**
 * @notice Destroys `amount` tokens from `from` address, reducing the total supply.
 * @param from The account to deduct the tokens from.
 */
function ownerBurn(address sender, address from, uint256 assets, uint256 shares) external;
```

This breaks the security guarantee that tokens are burned from the specified account. When the `TokenController` calls `ownerBurn(sender, from, assets, shares)` intending to burn tokens from the `from` address, the function instead burns tokens from the pair contract's balance.

**Attack Vector:**
During normal withdrawal operations, when `TokenController.burnId()` is called:
1. `burnId(tokenType, sender, from, assets, shares)` calls `ownerBurn(sender, from, assets, shares)`
2. Instead of burning `shares` from `from` account, it burns from `msg.sender` (pair)
3. The `from` account retains tokens that should have been burned
4. The pair contract loses tokens from its own balance

## Impact Explanation
**HIGH SEVERITY** - This vulnerability causes direct token accounting corruption with the following impacts:

1. **Protocol Fund Loss**: The pair contract loses tokens from its own balance that should have been burned from users
2. **User Benefit**: Users retain deposit tokens that should have been burned during withdrawals
3. **Accounting Inconsistency**: Individual balances become incorrect while total supply decreases correctly
4. **Economic Imbalance**: Users can withdraw underlying assets while keeping their deposit tokens

The impact is high because it affects the core token burning mechanism used in withdrawal operations, directly impacting protocol solvency and user balances.

## Likelihood Explanation
**HIGH LIKELIHOOD** - This vulnerability occurs during normal protocol operations:

1. **Automatic Trigger**: Every withdrawal operation that involves burning deposit tokens triggers this vulnerability
2. **No Special Conditions**: No malicious input or edge cases required - happens in normal usage
3. **Frequent Operations**: Withdrawals are common user operations in DeFi protocols
4. **Systematic Issue**: Affects all instances where `ownerBurn` is called on `ERC4626DepositToken`

The likelihood is high because the vulnerability is embedded in the core withdrawal flow and occurs automatically without requiring any special conditions or malicious actors.

## Proof of Concept
The following test demonstrates the vulnerability:

```solidity
function testERC4626DepositTokenVulnerability() public {
    // Setup: User has 500e18 tokens, pair has 500e18 tokens
    vm.startPrank(user);
    depositTokenX.transfer(pairAddress, 500e18);
    vm.stopPrank();

    // Record balances before burn
    uint256 userBalanceBefore = depositTokenX.balanceOf(user);    // 500e18
    uint256 pairBalanceBefore = depositTokenX.balanceOf(pairAddress); // 500e18

    // Execute ownerBurn - should burn from user but burns from pair
    vm.startPrank(pairAddress);
    depositTokenX.ownerBurn(user, user, 100e18, 100e18);
    vm.stopPrank();

    // Check results
    uint256 userBalanceAfter = depositTokenX.balanceOf(user);     // 500e18 (unchanged!)
    uint256 pairBalanceAfter = depositTokenX.balanceOf(pairAddress);  // 400e18 (decreased!)

    // Vulnerability confirmed:
    assert(userBalanceAfter == userBalanceBefore);     // User balance unchanged
    assert(pairBalanceAfter == pairBalanceBefore - 100e18); // Pair balance decreased
}
```

**Test Results:**
- User balance change: 0 (should have decreased by 100e18)
- Pair balance change: 100e18 (incorrectly decreased)
- Transfer event: `Transfer(from: PairHarness, to: 0x0, value: 100e18)` confirms burning from pair

## Recommendation
Fix the `ownerBurn()` function to burn tokens from the correct account:

```solidity
function ownerBurn(address sender, address to, uint256 assets, uint256 shares) public virtual override onlyOwner {
    emit Withdraw(msg.sender, to, sender, assets, shares);
    _burn(sender, shares); // ✅ Burn from sender parameter, not msg.sender
}
```

This change ensures that tokens are burned from the intended account (`sender`) as specified in the interface, maintaining proper token accounting and protocol integrity.